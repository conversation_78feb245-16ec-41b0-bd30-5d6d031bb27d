document.addEventListener('DOMContentLoaded', function() {
    const API_BASE_URL = '';  // 如果API和前端在同一域名下，可以留空
    const fileInput = document.getElementById('fileInput');
    const fileList = document.getElementById('fileList');
    const uploadForm = document.getElementById('uploadForm');
    const uploadBtn = document.getElementById('uploadBtn');
    const taskList = document.getElementById('taskList');
    const taskDetailModal = new bootstrap.Modal(document.getElementById('taskDetailModal'));
    
    let selectedFiles = [];
    let tasks = [];
    let currentTaskId = null;
    let pollingInterval = null;

    // 初始化页面
    loadTasks();
    startTaskPolling();

    // 文件选择事件
    fileInput.addEventListener('change', function(e) {
        selectedFiles = Array.from(e.target.files);
        updateFileList();
    });

    // 更新文件列表显示
    function updateFileList() {
        fileList.innerHTML = '';
        selectedFiles.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <i class="bi ${getFileIcon(file.name)}"></i>
                <span class="file-name">${file.name}</span>
                <span class="remove-file" data-index="${index}"><i class="bi bi-x-circle"></i></span>
            `;
            fileList.appendChild(fileItem);
        });

        // 添加删除文件事件
        document.querySelectorAll('.remove-file').forEach(btn => {
            btn.addEventListener('click', function() {
                const index = parseInt(this.getAttribute('data-index'));
                selectedFiles.splice(index, 1);
                updateFileList();
            });
        });
    }

    // 根据文件扩展名获取图标
    function getFileIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const audioFormats = ['mp3', 'm4a', 'aac', 'ogg', 'wav', 'flac', 'wma', 'aif'];
        const videoFormats = ['mp4', 'avi', 'mov', 'mkv'];
        
        if (audioFormats.includes(ext)) {
            return 'bi-file-earmark-music';
        } else if (videoFormats.includes(ext)) {
            return 'bi-file-earmark-play';
        } else {
            return 'bi-file-earmark';
        }
    }

    // 提交表单
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (selectedFiles.length === 0) {
            alert('请选择至少一个文件');
            return;
        }
        
        const formData = new FormData();
        selectedFiles.forEach(file => {
            formData.append('file', file);
        });
        
        const splitChars = document.getElementById('splitChars').value;
        formData.append('split_chars', splitChars);
        
        uploadBtn.disabled = true;
        uploadBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 上传中...';
        
        fetch(`${API_BASE_URL}/api/upload`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            console.log('上传成功:', data);
            uploadBtn.disabled = false;
            uploadBtn.innerHTML = '<i class="bi bi-play-fill"></i> 开始分离';
            
            // 清空文件列表
            selectedFiles = [];
            updateFileList();
            fileInput.value = '';
            
            // 刷新任务列表
            loadTasks();
        })
        .catch(error => {
            console.error('上传失败:', error);
            uploadBtn.disabled = false;
            uploadBtn.innerHTML = '<i class="bi bi-play-fill"></i> 开始分离';
            alert('上传失败，请重试');
        });
    });

    // 加载任务列表
    function loadTasks() {
        fetch(`${API_BASE_URL}/api/tasks`)
            .then(response => response.json())
            .then(data => {
                tasks = data;
                renderTaskList();
            })
            .catch(error => {
                console.error('获取任务列表失败:', error);
            });
    }

    // 渲染任务列表
    function renderTaskList() {
        taskList.innerHTML = '';
        
        if (tasks.length === 0) {
            taskList.innerHTML = '<div class="text-center text-muted py-3">暂无任务</div>';
            return;
        }
        
        tasks.forEach(task => {
            const taskItem = document.createElement('a');
            taskItem.href = '#';
            taskItem.className = `list-group-item list-group-item-action task-item ${task.status === 'error' ? 'list-group-item-danger' : ''}`;
            taskItem.setAttribute('data-task-id', task.id);
            
            let statusBadge = '';
            let progressInfo = '';
            
            switch(task.status) {
                case 'uploading':
                    statusBadge = '<span class="badge status-badge status-uploading">上传中</span>';
                    break;
                case 'processing':
                    statusBadge = '<span class="badge status-badge status-processing">处理中</span>';
                    if (task.progress) {
                        let progressText = '';
                        switch(task.progress) {
                            case 'preprocessing':
                                progressText = '预处理中';
                                break;
                            case 'recognizing':
                                progressText = '识别中';
                                break;
                            case 'splitting':
                                progressText = '分割中';
                                break;
                            case 'merging':
                                progressText = '合并中';
                                break;
                            default:
                                progressText = task.progress;
                        }
                        progressInfo = `<div class="progress"><div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div></div><small class="text-muted">${progressText}</small>`;
                    }
                    break;
                case 'completed':
                    statusBadge = '<span class="badge status-badge status-completed">已完成</span>';
                    break;
                case 'error':
                    statusBadge = '<span class="badge status-badge status-error">错误</span>';
                    progressInfo = `<small class="text-danger">${task.error || '处理失败'}</small>`;
                    break;
                default:
                    statusBadge = `<span class="badge status-badge">${task.status}</span>`;
            }
            
            const fileNames = task.files.map(file => file.name).join(', ');
            const createdAt = new Date(task.created_at).toLocaleString();
            
            taskItem.innerHTML = `
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${fileNames} ${statusBadge}</h6>
                    <small class="time-stamp">${createdAt}</small>
                </div>
                ${progressInfo}
            `;
            
            taskItem.addEventListener('click', function(e) {
                e.preventDefault();
                showTaskDetails(task.id);
            });
            
            taskList.appendChild(taskItem);
        });
    }

    // 显示任务详情
    function showTaskDetails(taskId) {
        currentTaskId = taskId;
        
        fetch(`${API_BASE_URL}/api/tasks/${taskId}`)
            .then(response => response.json())
            .then(task => {
                const taskInfo = document.getElementById('taskInfo');
                const mergedFiles = document.getElementById('mergedFiles');
                const segmentsList = document.getElementById('segmentsList');
                
                // 任务基本信息
                let statusText = '';
                switch(task.status) {
                    case 'uploading': statusText = '上传中'; break;
                    case 'processing': statusText = '处理中'; break;
                    case 'completed': statusText = '已完成'; break;
                    case 'error': statusText = '错误'; break;
                    default: statusText = task.status;
                }
                
                taskInfo.innerHTML = `
                    <p><strong>任务ID:</strong> ${task.id}</p>
                    <p><strong>状态:</strong> ${statusText}</p>
                    <p><strong>创建时间:</strong> ${new Date(task.created_at).toLocaleString()}</p>
                    <p><strong>文件:</strong> ${task.files.map(file => file.name).join(', ')}</p>
                    <p><strong>分离字数:</strong> ${task.split_chars || '10'}</p>
                    ${task.error ? `<p class="text-danger"><strong>错误信息:</strong> ${task.error}</p>` : ''}
                `;
                
                // 显示合并文件
                const mergedFilesContainer = document.getElementById('mergedFiles');
                mergedFilesContainer.innerHTML = '';
                
                if (task.merged_files && task.merged_files.length > 0) {
                    task.merged_files.forEach(file => {
                        const fileItem = document.createElement('div');
                        fileItem.className = 'list-group-item d-flex justify-content-between align-items-center';
                        fileItem.innerHTML = `
                            <div>
                                <span class="speaker-name">${file.speaker}</span>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary edit-speaker" data-speaker="${file.speaker}">
                                    <i class="bi bi-pencil"></i> 修改名称
                                </button>
                                <a href="/api/download/${file.relative_path}" class="btn btn-sm btn-outline-success">
                                    <i class="bi bi-download"></i> 下载
                                </a>
                            </div>
                        `;
                        mergedFilesContainer.appendChild(fileItem);
                    });
                    
                    // 添加修改说话人名称的事件监听
                    document.querySelectorAll('.edit-speaker').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const speaker = this.getAttribute('data-speaker');
                            document.getElementById('editSpeakerTaskId').value = taskId;
                            document.getElementById('editSpeakerOriginal').value = speaker;
                            document.getElementById('editSpeakerName').value = speaker;
                            const editSpeakerModal = new bootstrap.Modal(document.getElementById('editSpeakerModal'));
                            editSpeakerModal.show();
                        });
                    });
                } else {
                    mergedFilesContainer.innerHTML = '<div class="text-center text-muted py-3">没有合并文件</div>';
                }
                
                // 显示分段详情
                segmentsList.innerHTML = '';
                if (task.segments && task.segments.length > 0) {
                    task.segments.forEach((segment, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${segment.speaker}</td>
                            <td>${segment.start}</td>
                            <td>${segment.end}</td>
                            <td>
                                <div class="segment-text">${segment.text}</div>
                                <button class="btn btn-sm btn-outline-primary edit-text mt-1" data-index="${index}">
                                    <i class="bi bi-pencil"></i> 编辑
                                </button>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="/api/download/${segment.relative_path}" class="btn btn-sm btn-outline-success">
                                        <i class="bi bi-download"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-info play-segment" data-url="/api/download/${segment.relative_path}">
                                        <i class="bi bi-play-fill"></i>
                                    </button>
                                </div>
                            </td>
                        `;
                        segmentsList.appendChild(row);
                    });
                    
                    // 添加编辑文本内容的事件监听
                    document.querySelectorAll('.edit-text').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const index = parseInt(this.getAttribute('data-index'));
                            const segment = task.segments[index];
                            document.getElementById('editTextTaskId').value = taskId;
                            document.getElementById('editTextSegmentId').value = index;
                            document.getElementById('editTextContent').value = segment.text;
                            const editTextModal = new bootstrap.Modal(document.getElementById('editTextModal'));
                            editTextModal.show();
                        });
                    });
                    
                    // 添加播放分段的事件监听
                    document.querySelectorAll('.play-segment').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const url = this.getAttribute('data-url');
                            playAudio(url, this);
                        });
                    });
                } else {
                    segmentsList.innerHTML = '<tr><td colspan="5" class="text-center text-muted py-3">没有分段详情</td></tr>';
                }
                
                taskDetailModal.show();
            })
            .catch(error => {
                console.error('获取任务详情失败:', error);
                alert('获取任务详情失败，请重试');
            });
    }

    // 播放音频
    function playAudio(url, button) {
        // 移除之前的播放器
        const existingPlayers = document.querySelectorAll('.audio-player');
        existingPlayers.forEach(player => player.remove());
        
        // 重置所有播放按钮
        document.querySelectorAll('.play-segment').forEach(btn => {
            btn.innerHTML = '<i class="bi bi-play-fill"></i>';
        });
        
        // 创建新的播放器
        const audioPlayer = document.createElement('audio');
        audioPlayer.className = 'audio-player';
        audioPlayer.controls = true;
        audioPlayer.src = url;
        audioPlayer.autoplay = true;
        
        // 更改按钮图标
        button.innerHTML = '<i class="bi bi-pause-fill"></i>';
        
        // 播放结束后恢复按钮
        audioPlayer.addEventListener('ended', function() {
            button.innerHTML = '<i class="bi bi-play-fill"></i>';
            audioPlayer.remove();
        });
        
        // 插入播放器
        button.parentNode.appendChild(audioPlayer);
    }

    // 开始轮询任务状态
    function startTaskPolling() {
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }
        
        pollingInterval = setInterval(() => {
            loadTasks();
            
            // 如果有正在查看的任务，更新其详情
            if (currentTaskId && document.getElementById('taskDetailModal').classList.contains('show')) {
                showTaskDetails(currentTaskId);
            }
        }, 5000); // 每5秒更新一次
    }

    // 添加修改说话人名称的表单提交处理
    document.getElementById('editSpeakerForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const taskId = document.getElementById('editSpeakerTaskId').value;
        const originalName = document.getElementById('editSpeakerOriginal').value;
        const newName = document.getElementById('editSpeakerName').value;
        
        if (!newName.trim()) {
            alert('请输入有效的名称');
            return;
        }
        
        fetch(`/api/tasks/${taskId}/rename-speaker`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                original_name: originalName,
                new_name: newName
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('修改失败');
            }
            return response.json();
        })
        .then(data => {
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('editSpeakerModal')).hide();
            
            // 刷新任务详情
            showTaskDetails(taskId);
            
            // 显示成功消息
            alert('修改成功');
        })
        .catch(error => {
            alert(`修改失败: ${error.message}`);
        });
    });

    // 添加修改文本内容的表单提交处理
    document.getElementById('editTextForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const taskId = document.getElementById('editTextTaskId').value;
        const segmentId = document.getElementById('editTextSegmentId').value;
        const newText = document.getElementById('editTextContent').value;
        
        if (!newText.trim()) {
            alert('请输入有效的文本内容');
            return;
        }
        
        fetch(`/api/tasks/${taskId}/edit-text`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                segment_id: segmentId,
                new_text: newText
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('修改失败');
            }
            return response.json();
        })
        .then(data => {
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('editTextModal')).hide();
            
            // 刷新任务详情
            showTaskDetails(taskId);
            
            // 显示成功消息
            alert('修改成功');
        })
        .catch(error => {
            alert(`修改失败: ${error.message}`);
        });
    });
}); 