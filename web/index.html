<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>名医传承</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container-fluid">
        <header class="app-header text-white text-center py-4 mb-4">
            <h1>名医传承</h1>
            <p class="mb-0">音视频说话人分离系统</p>
        </header>

        <div class="row">
            <!-- 左侧上传区域 -->
            <div class="col-md-6">
                <div class="card mb-4 flat-card">
                    <div class="card-header app-card-header text-white">
                        <h5 class="mb-0"><i class="bi bi-upload"></i> 上传文件</h5>
                    </div>
                    <div class="card-body">
                        <form id="uploadForm">
                            <div class="mb-3">
                                <label for="fileInput" class="form-label">选择音频或视频文件</label>
                                <input class="form-control flat-input" type="file" id="fileInput" multiple accept=".mp3,.m4a,.aac,.ogg,.wav,.flac,.wma,.aif,.mp4,.avi,.mov,.mkv">
                            </div>
                            <div class="mb-3">
                                <label for="splitChars" class="form-label">分离字数</label>
                                <input type="number" class="form-control flat-input" id="splitChars" value="10" min="1">
                                <div class="form-text">控制每个音频片段切割的最大字符数</div>
                            </div>
                            <div class="mb-3">
                                <div id="fileList" class="list-group flat-list">
                                    <!-- 文件列表将在这里显示 -->
                                </div>
                            </div>
                            <button type="submit" class="btn flat-btn-primary" id="uploadBtn">
                                <i class="bi bi-play-fill"></i> 开始分离
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 右侧任务状态区域 -->
            <div class="col-md-6">
                <div class="card mb-4 flat-card">
                    <div class="card-header app-card-header text-white">
                        <h5 class="mb-0"><i class="bi bi-list-check"></i> 任务状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group flat-list" id="taskList">
                            <!-- 任务列表将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务详情模态框 -->
        <div class="modal fade" id="taskDetailModal" tabindex="-1" aria-labelledby="taskDetailModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content flat-modal">
                    <div class="modal-header app-card-header text-white">
                        <h5 class="modal-title" id="taskDetailModalLabel">任务详情</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3 flat-card">
                                    <div class="card-header flat-card-header">
                                        <h6 class="mb-0">任务信息</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="taskInfo">
                                            <!-- 任务信息将在这里显示 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card mb-3 flat-card">
                                    <div class="card-header flat-card-header">
                                        <h6 class="mb-0">合并文件</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="mergedFiles" class="list-group flat-list">
                                            <!-- 合并后的文件将在这里显示 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card flat-card">
                            <div class="card-header flat-card-header">
                                <h6 class="mb-0">分段详情</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover flat-table">
                                        <thead>
                                            <tr>
                                                <th>说话人</th>
                                                <th>开始时间</th>
                                                <th>结束时间</th>
                                                <th>文本内容</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="segmentsList">
                                            <!-- 分段详情将在这里显示 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn flat-btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加编辑说话人名称的模态框 -->
        <div class="modal fade" id="editSpeakerModal" tabindex="-1" aria-labelledby="editSpeakerModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content flat-modal">
                    <div class="modal-header app-card-header text-white">
                        <h5 class="modal-title" id="editSpeakerModalLabel">修改说话人名称</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editSpeakerForm">
                            <input type="hidden" id="editSpeakerTaskId">
                            <input type="hidden" id="editSpeakerOriginal">
                            <div class="mb-3">
                                <label for="editSpeakerName" class="form-label">新名称</label>
                                <input type="text" class="form-control flat-input" id="editSpeakerName" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn flat-btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" form="editSpeakerForm" class="btn flat-btn-primary">保存</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加编辑文本内容的模态框 -->
        <div class="modal fade" id="editTextModal" tabindex="-1" aria-labelledby="editTextModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content flat-modal">
                    <div class="modal-header app-card-header text-white">
                        <h5 class="modal-title" id="editTextModalLabel">修改文本内容</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editTextForm">
                            <input type="hidden" id="editTextTaskId">
                            <input type="hidden" id="editTextSegmentId">
                            <div class="mb-3">
                                <label for="editTextContent" class="form-label">文本内容</label>
                                <textarea class="form-control flat-input" id="editTextContent" rows="4" required></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn flat-btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" form="editTextForm" class="btn flat-btn-primary">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
</body>
</html> 