body {
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
}

.app-header {
    background-color: #2980b9;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.flat-card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: none;
    margin-bottom: 20px;
    overflow: hidden;
}

.app-card-header {
    background-color: #2980b9;
    border-bottom: none;
    font-weight: 600;
    padding: 15px 20px;
}

.flat-card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    font-weight: 600;
    padding: 15px 20px;
}

.flat-input {
    border-radius: 6px;
    border: 1px solid #ddd;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.flat-input:focus {
    border-color: #2980b9;
    box-shadow: 0 0 0 0.2rem rgba(41, 128, 185, 0.25);
}

.flat-btn-primary {
    background-color: #2980b9;
    border: none;
    border-radius: 6px;
    color: white;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.flat-btn-primary:hover {
    background-color: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.flat-btn-secondary {
    background-color: #95a5a6;
    border: none;
    border-radius: 6px;
    color: white;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.flat-btn-secondary:hover {
    background-color: #7f8c8d;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.flat-list .list-group-item {
    border-left: none;
    border-right: none;
    border-radius: 0;
    transition: all 0.3s ease;
}

.flat-list .list-group-item:first-child {
    border-top: none;
}

.flat-list .list-group-item:hover {
    background-color: #f1f8ff;
    transform: translateX(5px);
}

.task-item {
    transition: all 0.3s ease;
    cursor: pointer;
    padding: 15px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #eee;
    transition: all 0.3s ease;
}

.file-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.file-item .file-name {
    flex-grow: 1;
    margin-left: 12px;
}

.file-item .remove-file {
    color: #e74c3c;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-item .remove-file:hover {
    transform: scale(1.2);
}

.progress {
    height: 8px;
    margin-top: 10px;
    border-radius: 4px;
    overflow: hidden;
}

.status-badge {
    font-size: 0.8rem;
    padding: 5px 10px;
    border-radius: 20px;
}

.status-uploading {
    background-color: #95a5a6;
}

.status-processing {
    background-color: #2980b9;
}

.status-completed {
    background-color: #27ae60;
}

.status-error {
    background-color: #e74c3c;
}

.time-stamp {
    font-size: 0.8rem;
    color: #7f8c8d;
}

.flat-modal .modal-header {
    border-bottom: 0;
}

.flat-modal .modal-footer {
    border-top: 0;
}

.flat-table {
    font-size: 0.9rem;
}

.flat-table thead th {
    background-color: #f8f9fa;
    border-top: none;
    border-bottom: 2px solid #eee;
}

.audio-player {
    width: 100%;
    margin-top: 10px;
    border-radius: 6px;
}

.segment-text {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@media (max-width: 768px) {
    .segment-text {
        max-width: 150px;
    }
} 