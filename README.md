# 1. 安装
执行下面命令来安装依赖
```shell
pip install -U funasr modelscope ffmpeg-python pydub
```
此外还需要安装 torch

# 2. 模型下载
执行下面程序，会自动下载模型到当前用户 .cache/modelscope/hub/models/iic/ 目录中
```shell
python download_model.py
```

# 医患对话识别系统

## 系统概述

医患对话识别系统是一个集视频录制、音频分离和语音识别于一体的医疗场景专用系统。系统可以根据API信号自动录制诊室的视频，在录制结束后自动处理视频文件，提取并分离医生和患者的语音，生成转写文本，方便后续分析和归档。

## 主要功能

1. **视频录制**：通过API信号触发，根据房间ID录制对应的视频流
2. **音频处理**：自动从视频中提取音频并进行说话人分离
3. **视频分割**：按照说话人自动分割视频片段，保存为MP4格式
4. **语音识别**：将分离后的音频转写为文本
5. **Agent分析**：将转写结果发送至Dify Agent进行智能分析，并保存结果
6. **结构化存储**：按照诊室和任务ID组织文件，便于查询和管理

## 系统架构

系统分为三个主要模块：

1. **视频流捕获模块** (`stream_capture`)：负责接收API信号，控制视频流的录制和保存
2. **音频处理模块** (`backend`)：负责从视频中提取音频，进行说话人分离和转写
3. **Agent集成模块** (`agent`)：负责将转写结果发送给Dify Agent进行分析处理

### 目录结构

```
/
├── stream_capture/           # 视频流捕获模块
│   ├── api/                  # API接口
│   ├── core/                 # 核心功能
│   ├── config/               # 配置文件
│   ├── logs/                 # 日志文件
│   ├── recordings/           # 录制的视频文件
│   └── main.py               # 主程序
├── backend/                  # 音频处理模块
│   ├── audio_processor.py    # 音频处理核心
│   ├── app_api.py            # 应用接口
│   └── __init__.py           # 模块初始化
├── agent/                    # Agent集成模块
│   ├── dify_client.py        # Dify Agent客户端
│   ├── test_agent.py         # 测试工具
│   └── README.md             # 模块说明文档
├── SeparatedAudioText/       # 处理结果输出目录
├── start_service.sh          # 启动脚本
└── stop_service.sh           # 停止脚本
```

## 安装和配置

### 环境要求

- Python 3.8+
- FFmpeg 4.0+
- CUDA支持（推荐用于ASR模型加速）

### 安装步骤

1. 克隆代码库
```bash
git clone <repository-url>
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 配置房间映射
编辑 `stream_capture/config/room_stream_mapping.json` 文件，设置诊室名称与视频流URL的映射关系。

## 使用说明

### 启动服务

使用启动脚本启动服务：

```bash
bash start_service.sh
```

### API调用

#### 开始录制

```bash
curl -X POST http://**************:5000/api/signal/start \
  -H "Content-Type: application/json" \
  -d '{
    "patient_name": "患者姓名",
    "patient_id": "患者ID",
    "task_id": "202403280001",
    "room_id": "A402诊室"
  }'
```

#### 停止录制

```bash
curl -X POST http://**************:5000/api/signal/stop \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "202403280001"
  }'
```

### 停止服务

使用停止脚本停止服务：

```bash
bash stop_service.sh
```

## 输出结果

### 视频文件

录制的视频保存在 `recordings/诊室名称/任务ID.mp4` 路径下。

### 音频处理结果

处理后的音频和文本保存在 `SeparatedAudioText/诊室名称/任务ID/` 目录下，包括：

- `full_transcript.txt`：完整的转写文本
- `speaker_0.txt`, `speaker_1.txt` 等：各说话人的转写文本
- `speaker_0.mp3`, `speaker_1.mp3` 等：各说话人的分离音频
- `speaker_0/` 和 `speaker_1/` 等目录：包含各说话人的视频片段（MP4格式）
- `sentences.json`：结构化的转写结果，包含时间戳和说话人信息
- `task_id.json`：Dify Agent的分析结果（以任务ID命名）
- `agent_response.json`：兼容版本的Dify Agent分析结果

## Agent集成功能

系统能够自动处理转写完成的医患对话，将其发送至Dify平台进行分析，并保存分析结果。

#### 主要功能
- 自动处理：系统自动检测转写完成的文件，无需手动操作
- 实时分析：将转写结果发送至Dify平台，获取AI分析
- 只支持流式响应模式：当前仅支持"streaming"响应模式
- 分析结果保存：自动将分析结果保存为JSON文件
- **新增: 转写就绪通知机制**：确保无论转写多长时间都能触发Agent分析

#### 转写就绪通知机制

为解决长视频转写时可能超过最大重试次数导致无法调用Agent的问题，系统新增了转写就绪通知机制：

1. **主动通知**：当sentences.json文件生成完成后，音频处理器会主动通知信号处理器
2. **可靠调用**：不再依赖有限次数的重试机制，而是在文件确实生成后才调用Agent
3. **防重复处理**：系统会记录已处理的任务，避免重复调用Agent
4. **适用长视频**：特别适合处理长时间的医患对话，不会因为转写时间过长而错过Agent分析

这一机制确保了无论转写过程需要多长时间，只要sentences.json最终生成，系统就能可靠地将其发送给Agent进行分析。

#### 响应格式与保存

Agent的响应将被保存为两种格式的文件：

1. **`task_id.json`** - 仅包含Agent的纯回复内容：
   - 如果Agent返回的是JSON格式的内容，将保持其JSON结构不变
   - 如果Agent返回的是纯文本内容，则直接以文本格式保存
   - 该文件不包含任何API元数据，如`conversation_id`等
   - 推荐优先使用此文件获取Agent分析结果

2. **`agent_response.json`** - 包含完整API响应：
   - 包含`conversation_id`、`answer`等所有API返回的字段
   - 保留此文件是为了向后兼容和调试目的

示例位置：
```
SeparatedAudioText/402诊室/<任务ID>/任务ID.json     # 推荐使用，只包含纯回复内容
SeparatedAudioText/402诊室/<任务ID>/agent_response.json  # 包含完整API响应
```

#### 使用方法

默认情况下，Agent集成功能在系统启动后会自动工作，无需额外配置。

#### 手动测试

可以使用测试工具手动检查和测试Agent功能：

```bash
# 列出所有可用的转写结果
python agent/test_agent.py --list

# 测试最新的转写结果
python agent/test_agent.py --latest

# 测试特定的转写文件
python agent/test_agent.py --input SeparatedAudioText/402诊室/任务ID/sentences.json
```

更多测试命令和选项，请参考 [Agent模块说明文档](agent/README.md)。

## 故障排除

如遇问题，请查看日志文件：

- 视频录制日志：`stream_capture/logs/service.log`
- 音频处理日志：`backend/audio_processor.log`
- Agent集成日志：`agent/agent.log`

### 常见问题

1. **API连接问题**：确保Dify服务在`http://**************:5023`上运行，并且API密钥正确。
2. **响应模式**：Agent只支持流式(streaming)响应模式，请勿使用阻塞(blocking)模式。
3. **转写文件不存在**：检查音频处理是否完成，转写文件应位于`SeparatedAudioText/[诊室名]/[任务ID]/sentences.json`。

## 开发者文档

详细的设计文档和API接口说明请参考：

- 设计说明：`stream_capture/设计任务说明.md`
- 接口文档：`stream_capture/HSI系统接口交互说明.md`

