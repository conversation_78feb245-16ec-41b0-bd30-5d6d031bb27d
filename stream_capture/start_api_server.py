#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API服务器启动脚本
-------------
此脚本用于启动API服务器，提供房间映射API等功能。
"""

import os
import sys
import argparse
import logging
from typing import Dict, List, Optional, Any

# 设置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('api_server.log')
    ]
)
logger = logging.getLogger('api_server')

# 获取项目根目录
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

# 导入API服务器
try:
    from stream_capture.api.api import start_api_server
except ImportError as e:
    logger.error(f"导入API服务器模块失败: {e}")
    sys.exit(1)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='启动API服务器')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='监听地址')
    parser.add_argument('--port', type=int, default=5000, help='监听端口')
    parser.add_argument('--debug', action='store_true', help='是否开启调试模式')
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 创建必要的目录
    os.makedirs(os.path.join(root_dir, 'stream_capture', 'api'), exist_ok=True)
    
    # 确保已安装必要的依赖
    try:
        import flask
        import flask_cors
    except ImportError as e:
        logger.error(f"缺少必要的依赖: {e}")
        logger.error("请安装以下依赖: pip install flask flask-cors")
        sys.exit(1)
    
    # 初始化并启动API服务器
    logger.info(f"启动API服务器: {args.host}:{args.port}, 调试模式: {args.debug}")
    success = start_api_server(args.host, args.port, args.debug)
    
    if not success:
        logger.error("启动API服务器失败")
        sys.exit(1)

if __name__ == "__main__":
    main() 