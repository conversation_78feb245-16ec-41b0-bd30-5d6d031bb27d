# HSI系统与视频流捕获系统接口交互说明

## 1. 系统概述

HSI系统需要通过API接口向视频流捕获系统发送"开始"和"停止"信号来控制视频录制。视频流捕获系统接收到信号后，会根据MAC地址查询对应的诊室信息和视频流URL，并开始或停止录制视频。录制的视频文件将按诊室名称组织，并使用任务ID作为文件名。

当视频停止录制后，系统会自动进行音频处理，包括说话人分离和语音识别，生成结构化的文本和音频文件，同时生成的电子病历结果将保存到数据库中。

## 2. 数据流程

1. HSI系统发送"开始"信号，携带任务ID和设备MAC地址
2. 视频流捕获系统接收到信号，通过MAC地址查询对应的诊室信息
3. 视频流捕获系统开始录制对应诊室的视频流
4. HSI系统发送"停止"信号，仅携带任务ID
5. 视频流捕获系统停止录制视频并自动触发音频处理
6. 处理完成后，生成的文件可在指定目录查看，同时系统自动将电子病例结果保存到数据库中

## 3. 注意事项

1. 视频数据不会自动保存，需要通过API信号触发录制
2. 录制过程基于提供的MAC地址，必须确保MAC地址在系统中正确配置
3. 视频保存为MP4格式，保存路径为`recordings/诊室名称/任务ID.mp4`
4. 系统会自动记录任务开始和结束时间
5. 音频处理结果保存在`SeparatedAudioText/诊室名称/任务ID/`目录下
6. **任务ID**: 必须唯一，建议使用前缀+日期+序号的格式，如`TASK20231101001`。
7. **MAC地址**: 必须与配置文件中的`mac_address`一致，否则会返回错误。MAC地址格式示例：`70:85:c4:61:66:78`。
8. **错误处理**: 接口调用失败时，请根据返回的错误信息进行相应处理。
9. **重复调用**: 对于同一个任务，不要重复发送开始信号；任务完成后，不要重复发送停止信号。
10. **时间处理**: 系统会自动记录任务的开始和结束时间，无需在请求中包含时间信息。
11. **视频录制**: 系统使用ffmpeg直接从流媒体URL录制视频，确保流媒体URL可以正常访问。
12. **电子病历结果**: 处理完成后，系统会将电子病历结果保存在数据库中，可通过数据库查询获取。
13. **用户信息**: 提供的userid和username信息将用于记录电子病历的创建者，并会被保存到biz_cloud_health表中，用于系统消息通知。

## 4. 接口规范

### 4.1 开始录制视频

- **URL**: `/api/signal/start`
- **Method**: `POST`
- **Headers**: 
  - Content-Type: application/json
- **Body**:
  ```json
  {
    "task_id": "任务ID",
    "mac_address": "设备MAC地址"
  }
  ```
- **可选参数**:
  ```json
  {
    "patient_name": "患者姓名",  // 可选，默认为"未知患者"
    "patient_id": "患者ID",     // 可选，默认自动生成
    "userid": "用户ID",        // 可选，用于记录创建者信息
    "username": "用户姓名"      // 可选，用于记录创建者信息
  }
  ```
- **成功响应**:
  ```json
  {
    "status": "success",
    "message": "开始录制任务: [任务ID]",
    "room_name": "诊室名称",
    "stream_url": "视频流URL"
  }
  ```
- **错误响应**:
  ```json
  {
    "status": "error",
    "message": "错误信息"
  }
  ```

### 4.2 停止录制视频

- **URL**: `/api/signal/stop`
- **Method**: `POST`
- **Headers**: 
  - Content-Type: application/json
- **Body**:
  ```json
  {
    "task_id": "任务ID"
  }
  ```
- **成功响应**:
  ```json
  {
    "status": "success",
    "message": "停止录制任务: [任务ID], 录制时长: [时长]秒",
    "duration": 120.5  // 录制时长，单位：秒
  }
  ```
- **错误响应**:
  ```json
  {
    "status": "error",
    "message": "错误信息"
  }
  ```

## 5. 调用示例

### 5.1 开始录制视频

```bash
curl -X POST http://**************:5000/api/signal/start \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "TASK2025042400007",
    "mac_address": "00:FF:71:2D:0B:7F",
    "userid": "doctor_12345678910",
    "username": "周良龙10",
    "chief_complaint": "头痛伴眼部胀痛1周",
    "present_illness": "患者1周前开始出现头痛，夜间伴有眼部胀痛，未进行任何治疗及检查。",
    "past_medical_history": "既往有高血压病史，否认糖尿病、心脏病、肝炎、结核病等病史",
    "allergic_history": "否认药物、食物过敏史"
  }'
```

### 5.2 停止录制视频

```bash
curl -X POST http://**************:5000/api/signal/stop \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "TASK2025042400007"
  }'
```

## 6. 输出文件说明

### 6.1 视频文件

- **存储位置**: `recordings/诊室名称/任务ID.mp4`
- **格式**: MP4
- **命名规则**: 使用任务ID作为文件名

### 6.2 音频处理结果

- **存储位置**: `SeparatedAudioText/诊室名称/任务ID/`
- **文件内容**:
  - `full_transcript.txt`: 完整的转写文本
  - `speaker_0.txt`, `speaker_1.txt`, ...: 各说话人的转写文本
  - `speaker_0.mp3`, `speaker_1.mp3`, ...: 各说话人的分离音频
  - `sentences.json`: 转写结果的JSON格式
  - `task_id.json`: 处理后的电子病例结果（同时保存在数据库中）

## 7. 故障排除

1. 如果API调用返回500错误，检查服务是否正常运行
2. 如果录制没有开始，检查MAC地址是否正确配置
3. 如果视频文件没有生成，检查磁盘空间是否充足
4. 如果音频处理结果未生成，检查日志文件中的错误信息

## 8. API服务管理

- **启动服务**: `bash start_service.sh`
- **停止服务**: `bash stop_service.sh`
- **查看日志**: `tail -f stream_capture/logs/service.log`

## 配置文件

系统使用`config/room_stream_mapping.json`配置文件来定义诊室与流媒体URL的映射关系，现在改用MAC地址作为唯一标识。配置格式如下：

```json
[
  {
    "room_name": "A402诊室",
    "mac_address": "70:85:c4:61:66:78",
    "stream_url": "https://livell.cdutcm.edu.cn/live/f2ya.live.flv"
  },
  {
    "room_name": "笔记本电脑",
    "mac_address": "e4:60:17:1d:42:b0",
    "stream_url": "https://livell.cdutcm.edu.cn/live/efy1a.live.flv"
  }
]
```

## 文件存储说明

### 视频文件存储结构
- 所有视频文件保存在`recordings`目录下
- 按诊室名称组织成子目录，如`recordings/402诊室/`
- 视频文件直接使用任务ID命名，如`TASK2023110100001.mp4`
- 完整路径示例：`recordings/402诊室/TASK2023110100001.mp4`

## 系统架构

```
stream_capture/
├── config/                    # 配置文件目录
│   └── room_stream_mapping.json    # 房间映射配置
├── core/                     # 核心功能模块
│   ├── consumer/            # 消费者模块
│   │   └── stream_saver.py         # 视频流保存实现
│   ├── db/                  # 数据库访问层
│   │   └── room_mapping_dao.py     # 房间映射数据访问对象
│   ├── producer/            # 生产者模块
│   │   └── stream_capturer.py      # 视频流捕获实现
│   └── signal/              # 信号处理模块
│       └── signal_handler.py        # 信号处理实现
├── api/                     # API接口
│   └── signal_receiver.py   # 信号接收服务
├── logs/                    # 日志文件目录
├── recordings/              # 视频录制目录
│   ├── 402诊室/             # 各诊室的视频文件目录
│   └── 笔记本电脑/           # 各诊室的视频文件目录
└── main.py                  # 主程序入口
```

## 故障排除

1. **连接失败**: 
   - 检查服务器IP和端口是否正确
   - 检查网络连接是否正常
   - 检查服务是否启动

2. **请求失败**: 
   - 检查请求格式是否正确
   - 检查参数是否完整
   - 检查必要字段是否都已提供

3. **录制失败**: 
   - 检查MAC地址是否正确
   - 检查流媒体URL是否可访问
   - 检查ffmpeg是否正确安装
   - 查看服务器日志获取详细错误信息 