#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
视频流捕获服务主程序
-----------------
此模块是系统的主入口，负责启动信号处理服务。
"""

import os
import sys
import logging
import argparse
import signal
import time
import threading
from flask import Flask

# 将项目根目录添加到Python路径
# 获取当前文件所在目录的上一级目录（即项目根目录）
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_dir)

# 全局日志配置将在parse_args后设置
logger = None

# 全局变量
running = True
server_thread = None
asr_preloaded = False
logs_dir = None

# 模块初始化标志
_ASR_PRELOAD_LOCK = threading.Lock()
_ASR_PRELOAD_STARTED = False
_ASR_PRELOAD_COMPLETED = False
_TRANSCRIPT_CALLBACK_REGISTERED = False
_SERVER_THREAD_STARTED = False

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='视频流捕获服务')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=5000, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='是否开启调试模式')
    parser.add_argument('--config', type=str, default=None, help='配置文件路径')
    parser.add_argument('--no-asr-preload', action='store_true', help='禁用ASR模型预加载')
    parser.add_argument('--logs-dir', type=str, default='logs', help='日志目录路径')
    return parser.parse_args()

def setup_logging(logs_dir):
    """
    设置全局日志配置
    
    Args:
        logs_dir: 日志目录路径
    """
    global logger
    
    # 确保日志目录存在
    os.makedirs(logs_dir, exist_ok=True)
    
    # 设置日志文件路径
    main_log_path = os.path.join(logs_dir, 'service.log')
    
    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(main_log_path),
            logging.StreamHandler()
        ]
    )
    
    # 设置环境变量，让其他模块能够获取统一的日志目录
    os.environ['AUDIO_SEPARATION_LOGS_DIR'] = logs_dir
    
    # 创建主程序日志记录器
    logger = logging.getLogger('main')
    logger.info(f"日志系统初始化完成，日志将保存在 {logs_dir} 目录")

def signal_handler(sig, frame):
    """信号处理函数"""
    global running
    if logger:
        logger.info(f"接收到信号: {sig}，准备关闭服务...")
    running = False

def setup_signal_handlers():
    """设置信号处理器"""
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def preload_asr_model():
    """预加载ASR模型"""
    global asr_preloaded, _ASR_PRELOAD_STARTED, _ASR_PRELOAD_COMPLETED
    
    # 使用锁确保线程安全
    with _ASR_PRELOAD_LOCK:
        # 如果已经完成预加载，直接返回
        if _ASR_PRELOAD_COMPLETED:
            logger.debug("ASR模型已经预加载完成，跳过")
            return True
            
        # 如果正在预加载中，不重复启动
        if _ASR_PRELOAD_STARTED:
            logger.debug("ASR模型预加载已经启动，跳过")
            return True
            
        # 标记为已启动预加载
        _ASR_PRELOAD_STARTED = True
    
    try:
        # 导入AudioProcessor
        from backend import AudioProcessor
        
        # 只有在第一次预加载时记录日志
        logger.info("开始预加载ASR模型...")
        
        # 直接调用AudioProcessor的预加载方法
        AudioProcessor.preload_model()
        
        # 标记为已完成预加载
        with _ASR_PRELOAD_LOCK:
            _ASR_PRELOAD_COMPLETED = True
            asr_preloaded = True
        
        return True
    except Exception as e:
        logger.error(f"预加载ASR模型时发生异常: {str(e)}")
        asr_preloaded = False
        return False

def register_transcript_ready_callback(signal_handler):
    """注册转写就绪回调函数"""
    global _TRANSCRIPT_CALLBACK_REGISTERED
    
    # 避免重复注册
    if _TRANSCRIPT_CALLBACK_REGISTERED:
        logger.debug("转写就绪回调函数已注册，跳过重复注册")
        return True
        
    try:
        # 导入AudioProcessor
        from backend import AudioProcessor
        
        # 定义回调函数
        def callback(task_id, room_name):
            # 转发到SignalHandler的handle_transcript_ready方法
            signal_handler.handle_transcript_ready(task_id, room_name)
        
        # 注册回调函数
        success = AudioProcessor.register_transcript_ready_callback(callback)
        if success:
            logger.info("已成功注册转写就绪回调函数")
            # 标记为已注册
            _TRANSCRIPT_CALLBACK_REGISTERED = True
            return True
        else:
            logger.error("注册转写就绪回调函数失败")
            return False
    except Exception as e:
        logger.error(f"注册转写就绪回调函数时发生异常: {str(e)}")
        return False

def start_server_in_thread(host, port, debug, config_path, logs_dir):
    """在线程中启动服务"""
    global _SERVER_THREAD_STARTED, server_thread
    
    # 避免重复启动服务器
    if _SERVER_THREAD_STARTED and server_thread and server_thread.is_alive():
        logger.debug("Flask服务器已经在运行，跳过重复启动")
        return server_thread
    
    # 标记服务器线程已启动
    _SERVER_THREAD_STARTED = True
    
    # 使用importlib避免循环导入
    import importlib
    signal_receiver = importlib.import_module('stream_capture.api.signal_receiver')
    
    # 获取Flask应用实例，初始化会在get_app中自动完成
    app = signal_receiver.get_app()
    
    # 只在第一次启动时初始化信号处理器
    if not hasattr(signal_receiver, '_signal_handler_initialized_in_main'):
        signal_handler = signal_receiver.init_signal_handler(config_path, logs_dir)
        signal_receiver._signal_handler_initialized_in_main = True
        
        # 注册转写就绪回调函数
        if signal_handler:
            register_transcript_ready_callback(signal_handler)
        else:
            logger.error("初始化信号处理器失败，无法注册转写就绪回调函数")
    
    # 启动Flask应用（在线程中）
    server_thread = threading.Thread(
        target=lambda: app.run(
            host=host,
            port=port,
            debug=False,  # 在线程中禁用debug模式
            use_reloader=False,  # 禁用重载器，避免创建子进程
            threaded=True
        ),
        daemon=True  # 设置为守护线程，主线程结束时自动结束
    )
    server_thread.start()
    
    logger.info(f"Flask应用已在线程中启动，监听地址: {host}:{port}")
    return server_thread

def main():
    """主函数"""
    global running, server_thread, logs_dir
    
    # 解析命令行参数
    args = parse_args()
    
    # 设置日志目录
    logs_dir = args.logs_dir
    
    # 设置日志系统
    setup_logging(logs_dir)
    
    # 创建必要的目录，使用相对路径
    os.makedirs('stream_capture/recordings', exist_ok=True)
    os.makedirs('SeparatedAudioText', exist_ok=True)
    os.makedirs('temp', exist_ok=True)
    
    # 设置配置文件路径
    if args.config is None:
        args.config = 'stream_capture/config/room_stream_mapping.json'
        if not os.path.exists(args.config):
            logger.warning(f"默认配置文件不存在: {args.config}")
            # 尝试其他可能的路径
            alt_config = 'config/room_stream_mapping.json'
            if os.path.exists(alt_config):
                args.config = alt_config
                logger.info(f"使用替代配置文件: {args.config}")
    
    # 检查配置文件是否存在
    if not os.path.exists(args.config):
        logger.error(f"找不到配置文件: {args.config}")
        logger.error("请确保配置文件存在，或使用--config参数指定配置文件路径")
    else:
        logger.info(f"使用配置文件: {args.config}")
    
    # 设置信号处理器
    setup_signal_handlers()
    
    try:
        logger.info("正在初始化视频流捕获服务...")
        
        # 检查是否已经有服务实例在运行
        if _SERVER_THREAD_STARTED and server_thread and server_thread.is_alive():
            logger.info("视频流捕获服务已经在运行，使用现有实例")
        else:
            # 在线程中启动API服务
            logger.info(f"正在启动API服务，监听地址: {args.host}:{args.port}")
            server_thread = start_server_in_thread(
                host=args.host, 
                port=args.port, 
                debug=args.debug,
                config_path=args.config,
                logs_dir=logs_dir
            )
        
        # 如果未禁用，预加载ASR模型
        if not args.no_asr_preload:
            if preload_asr_model():
                logger.info("ASR模型预加载成功启动")
        
        # 主循环，等待退出信号
        logger.info("服务已启动，按 CTRL+C 停止服务")
        while running:
            time.sleep(0.5)
        
        logger.info("正在关闭服务...")
        
        # 等待所有线程结束（只等待一小段时间）
        time.sleep(1)
        
        logger.info("服务已关闭")
        return 0
        
    except Exception as e:
        logger.error(f"服务启动失败: {str(e)}")
        return 1

if __name__ == "__main__":
    exit(main()) 