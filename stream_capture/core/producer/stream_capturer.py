#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
视频流捕获模块
------------
此模块基于ffmpeg实现视频流的实时捕获，支持多路视频流并发捕获和健康检查。
"""

import os
import json
import time
import signal
import logging
import threading
import subprocess
from datetime import datetime
import sys
import cv2
import numpy as np
from typing import Dict, List, Optional

# 添加项目根目录到Python路径
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

# 导入房间映射数据访问对象
from stream_capture.core.db.room_mapping_dao import RoomMappingDAO

class StreamCapturerThread(threading.Thread):
    """
    视频流捕获线程
    
    负责捕获单路视频流并将数据放入队列。
    """
    
    def __init__(self, room_name, stream_url, queue_manager, chunk_size=1024*8,
                 max_retries=5, retry_interval=5):
        """
        初始化视频流捕获线程
        
        Args:
            room_name: 诊室名称
            stream_url: 视频流URL
            queue_manager: 队列管理器
            chunk_size: 每次读取的数据大小
            max_retries: 最大重试次数
            retry_interval: 重试间隔（秒）
        """
        super().__init__()
        self.room_name = room_name
        self.stream_url = stream_url
        self.queue_manager = queue_manager
        self.chunk_size = chunk_size
        self.max_retries = max_retries
        self.retry_interval = retry_interval
        
        self.daemon = True
        self.running = False
        self.process = None
        self.last_frame_time = None
        self.retry_count = 0
        
        self.logger = logging.getLogger(f'stream_capturer.{room_name}')
        
    def run(self):
        """运行视频流捕获线程"""
        self.running = True
        self.logger.info(f"开始捕获视频流: {self.room_name}, URL: {self.stream_url}")
        
        while self.running:
            try:
                # 启动ffmpeg进程
                self._start_ffmpeg_process()
                
                # 读取流数据
                while self.running and self.process.poll() is None:
                    data = self.process.stdout.read(self.chunk_size)
                    if not data:
                        self.logger.warning(f"未读取到数据，可能流已中断: {self.room_name}")
                        break
                    
                    # 更新最后帧时间
                    self.last_frame_time = datetime.now()
                    
                    # 将数据放入队列
                    self.queue_manager.put(self.room_name, data)
                
                # 如果进程已结束但线程仍在运行，则尝试重启
                if self.running:
                    self.logger.warning(f"视频流中断，准备重试: {self.room_name}")
                    self._handle_retry()
                
            except Exception as e:
                self.logger.error(f"捕获视频流时发生错误: {self.room_name}, 错误: {str(e)}")
                if self.running:
                    self._handle_retry()
        
        # 确保进程已终止
        self._stop_ffmpeg_process()
        self.logger.info(f"视频流捕获线程已停止: {self.room_name}")
    
    def stop(self):
        """停止视频流捕获线程"""
        self.logger.info(f"正在停止视频流捕获: {self.room_name}")
        self.running = False
        self._stop_ffmpeg_process()
    
    def is_healthy(self, timeout=10):
        """
        检查视频流是否健康
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            bool: 视频流是否健康
        """
        if self.last_frame_time is None:
            return False
            
        # 检查最后帧时间是否在超时范围内
        time_since_last_frame = (datetime.now() - self.last_frame_time).total_seconds()
        return time_since_last_frame < timeout
    
    def _start_ffmpeg_process(self):
        """启动ffmpeg进程"""
        # 构建ffmpeg命令
        cmd = [
            'ffmpeg',
            '-i', self.stream_url,
            '-c', 'copy',              # 复制编码，不重新编码
            '-f', 'mpegts',            # 使用MPEG-TS格式输出
            '-nostats',                # 不显示统计信息
            '-loglevel', 'error',      # 只显示错误信息
            'pipe:1'                   # 输出到标准输出
        ]
        
        # 启动进程
        self.process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            bufsize=10**8             # 大缓冲区
        )
        
        self.logger.info(f"已启动ffmpeg进程: {self.room_name}")
        self.retry_count = 0  # 重置重试计数
    
    def _stop_ffmpeg_process(self):
        """停止ffmpeg进程"""
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except (subprocess.TimeoutExpired, ProcessLookupError):
                try:
                    self.process.kill()
                except ProcessLookupError:
                    pass
            self.process = None
            self.logger.info(f"已停止ffmpeg进程: {self.room_name}")
    
    def _handle_retry(self):
        """处理重试逻辑"""
        self._stop_ffmpeg_process()
        
        # 检查是否达到最大重试次数
        if self.max_retries > 0 and self.retry_count >= self.max_retries:
            self.logger.error(f"达到最大重试次数，停止捕获: {self.room_name}")
            self.running = False
            return
        
        # 重试
        self.retry_count += 1
        self.logger.info(f"重试 {self.retry_count}/{self.max_retries}: {self.room_name}, 将在 {self.retry_interval} 秒后重试")
        time.sleep(self.retry_interval)


class StreamHealthChecker(threading.Thread):
    """
    视频流健康检查线程
    
    负责检查多路视频流的健康状态并在必要时重启。
    """
    
    def __init__(self, capturer_threads, check_interval=10):
        """
        初始化健康检查线程
        
        Args:
            capturer_threads: 捕获线程字典
            check_interval: 检查间隔（秒）
        """
        super().__init__()
        self.capturer_threads = capturer_threads
        self.check_interval = check_interval
        self.daemon = True
        self.running = False
        self.logger = logging.getLogger('stream_health_checker')
    
    def run(self):
        """运行健康检查线程"""
        self.running = True
        self.logger.info("启动视频流健康检查线程")
        
        while self.running:
            try:
                self._check_all_streams()
                time.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"健康检查过程中发生错误: {str(e)}")
                time.sleep(self.check_interval)
    
    def stop(self):
        """停止健康检查线程"""
        self.logger.info("停止视频流健康检查线程")
        self.running = False
    
    def _check_all_streams(self):
        """检查所有视频流的健康状态"""
        for room_name, thread in self.capturer_threads.items():
            if not thread.is_alive():
                self.logger.warning(f"捕获线程已死亡，重启中: {room_name}")
                self._restart_thread(room_name, thread)
            elif not thread.is_healthy():
                self.logger.warning(f"视频流不健康，重启中: {room_name}")
                thread.stop()
                time.sleep(1)  # 给线程一些时间来停止
                self._restart_thread(room_name, thread)
    
    def _restart_thread(self, room_name, thread):
        """重启捕获线程"""
        # 创建新线程
        new_thread = StreamCapturerThread(
            room_name=room_name,
            stream_url=thread.stream_url,
            queue_manager=thread.queue_manager,
            chunk_size=thread.chunk_size,
            max_retries=thread.max_retries,
            retry_interval=thread.retry_interval
        )
        
        # 启动新线程
        new_thread.start()
        
        # 更新线程字典
        self.capturer_threads[room_name] = new_thread
        self.logger.info(f"已重启捕获线程: {room_name}")


class StreamCapturer:
    """
    视频流捕获器
    
    负责管理多路视频流的捕获，并进行健康检查。
    """
    
    def __init__(self, queue_manager, config_path=None, max_retries=0):
        """
        初始化视频流捕获器
        
        Args:
            queue_manager: 队列管理器
            config_path: 配置文件路径
            max_retries: 最大重试次数，0表示无限重试
        """
        self.logger = logging.getLogger('stream_capturer')
        self.queue_manager = queue_manager
        self.max_retries = max_retries
        
        # 加载房间流媒体映射
        self.room_mapping = self._load_room_mapping(config_path)
        
        # 捕获线程字典
        self.capturer_threads = {}
        
        # 健康检查线程
        self.health_checker = None
    
    def _load_room_mapping(self, config_path=None):
        """
        加载房间流媒体映射
        
        Args:
            config_path: 配置文件路径（用于备用）
            
        Returns:
            list: 房间映射列表
        """
        try:
            # 使用RoomMappingDAO获取所有房间映射
            dao = RoomMappingDAO(config_path)
            room_mappings = dao.get_all_rooms()
            
            # 转换为与原代码兼容的格式
            if room_mappings:
                # 如果数据来自数据库
                if isinstance(room_mappings, list) and room_mappings and isinstance(room_mappings[0], dict) and 'id' in room_mappings[0]:
                    # 转换为简化格式
                    result = []
                    for item in room_mappings:
                        # 只保留需要的字段，使用mac_address而非ip_address
                        mapping = {
                            'room_name': item['room_name'],
                            'mac_address': item.get('mac_address', item.get('ip_address', '')),  # 兼容处理
                            'stream_url': item['stream_url']
                        }
                        if 'description' in item and item['description']:
                            mapping['description'] = item['description']
                        result.append(mapping)
                    room_mappings = result
            
            self.logger.info(f"成功加载房间映射，共 {len(room_mappings)} 个房间")
            return room_mappings
            
        except Exception as e:
            self.logger.error(f"加载房间映射失败: {str(e)}")
            self.logger.warning("将使用备用方法加载配置文件")
            
            # 备用方法：直接读取配置文件
            try:
                if config_path is None:
                    # 尝试多个可能的配置文件路径
                    possible_paths = [
                        os.path.join('stream_capture', 'config', 'room_stream_mapping.json'),
                        os.path.join('config', 'room_stream_mapping.json'),
                        'room_stream_mapping.json'
                    ]
                    
                    for path in possible_paths:
                        if os.path.exists(path):
                            config_path = path
                            self.logger.info(f"找到配置文件: {config_path}")
                            break
                    else:
                        self.logger.error(f"找不到配置文件，尝试路径: {', '.join(possible_paths)}")
                        return []
                
                with open(config_path, 'r', encoding='utf-8') as f:
                    mapping = json.load(f)
                    
                self.logger.info(f"成功从配置文件加载房间映射，共 {len(mapping)} 个房间")
                return mapping
                
            except Exception as e:
                self.logger.error(f"加载备用配置文件失败: {str(e)}")
                return []
    
    def get_room_by_stream_url(self, stream_url):
        """
        根据视频流URL查询对应的诊室信息
        
        Args:
            stream_url: 视频流URL
            
        Returns:
            dict: 诊室信息，如果未找到则返回None
        """
        try:
            # 使用RoomMappingDAO查询
            dao = RoomMappingDAO()
            room_info = dao.get_room_by_stream_url(stream_url)
            
            if room_info:
                self.logger.info(f"通过流媒体URL找到诊室: {room_info['room_name']}")
                return {
                    'room_name': room_info['room_name'],
                    'mac_address': room_info.get('mac_address', room_info.get('ip_address', '')),
                    'stream_url': room_info['stream_url']
                }
            else:
                self.logger.warning(f"未通过流媒体URL {stream_url} 找到诊室")
                
                # 尝试从本地映射中查找
                for room in self.room_mapping:
                    if room['stream_url'] == stream_url:
                        self.logger.info(f"在本地映射中找到诊室: {room['room_name']}")
                        return room
                
                return None
                
        except Exception as e:
            self.logger.error(f"根据流媒体URL查询诊室时发生错误: {str(e)}")
            return None
    
    def start(self):
        """启动视频流捕获"""
        self.logger.info("启动视频流捕获")
        
        # 为每个房间创建并启动捕获线程
        for room in self.room_mapping:
            room_name = room['room_name']
            stream_url = room['stream_url']
            mac_address = room.get('mac_address', room.get('ip_address', '未知'))  # 兼容处理
            
            # 记录房间信息
            self.logger.info(f"处理房间: {room_name}, MAC: {mac_address}, URL: {stream_url}")
            
            # 将房间添加到队列管理器
            self.queue_manager.add_room(room_name)
            
            # 创建并启动捕获线程
            thread = StreamCapturerThread(
                room_name=room_name,
                stream_url=stream_url,
                queue_manager=self.queue_manager,
                max_retries=self.max_retries
            )
            thread.start()
            
            self.capturer_threads[room_name] = thread
            self.logger.info(f"已启动捕获线程: {room_name}, MAC: {mac_address}, URL: {stream_url}")
        
        # 启动健康检查线程
        self.health_checker = StreamHealthChecker(self.capturer_threads)
        self.health_checker.start()
        self.logger.info("已启动健康检查线程")
    
    def stop(self):
        """停止视频流捕获"""
        self.logger.info("停止视频流捕获")
        
        # 停止健康检查线程
        if self.health_checker:
            self.health_checker.stop()
        
        # 停止所有捕获线程
        for room_name, thread in self.capturer_threads.items():
            thread.stop()
            self.logger.info(f"已停止捕获线程: {room_name}")
        
        # 等待所有线程结束
        for thread in self.capturer_threads.values():
            thread.join(timeout=5)
        
        self.capturer_threads.clear()
        self.logger.info("所有视频流捕获线程已停止")
    
    def get_status(self):
        """
        获取视频流捕获状态
        
        Returns:
            dict: 状态信息
        """
        status = {
            'rooms': {},
            'active_threads': len(self.capturer_threads),
            'queue_status': self.queue_manager.get_status()
        }
        
        for room_name, thread in self.capturer_threads.items():
            status['rooms'][room_name] = {
                'is_alive': thread.is_alive(),
                'is_healthy': thread.is_healthy(),
                'retry_count': thread.retry_count,
                'last_frame_time': thread.last_frame_time.isoformat() if thread.last_frame_time else None
            }
        
        return status
