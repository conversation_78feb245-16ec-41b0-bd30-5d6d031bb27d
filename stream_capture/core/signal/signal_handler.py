#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
信号处理器
-------
此模块提供用于处理HSI系统发送的"开始"和"停止"信号的处理器。
处理器负责根据信号控制视频流捕获和保存，并触发后续处理。
"""

import os
import json
import time
import sys
import logging
import threading
import subprocess
from typing import Dict, List, Optional, Any

# 将项目根目录添加到Python路径
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, root_dir)

# 导入视频保存管理器
from stream_capture.core.consumer.stream_saver import StreamSaverManager

# 尝试导入音频处理模块
audio_processor_available = False
try:
    from backend import AudioProcessor
    audio_processor_available = True
except ImportError:
    pass

# 尝试导入Dify Agent客户端
dify_agent_available = False
try:
    from agent.dify_client import DifyAgentClient
    dify_agent_available = True
except ImportError:
    pass

# 尝试导入请求模块
try:
    import requests
except ImportError:
    requests = None

# 日志初始化状态
_LOGGER_INITIALIZED = False
_LOGGER_LOCK = threading.Lock()

class SignalHandler:
    """
    信号处理器
    
    负责处理从API接收到的开始和停止信号，并协调视频流录制。
    """
    
    # 单例实例
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, *args, **kwargs):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(SignalHandler, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance
    
    def __init__(self, config_path=None, logs_dir=None):
        """
        初始化信号处理器
        
        Args:
            config_path: 配置文件路径
            logs_dir: 日志目录路径，用于统一日志保存位置
        """
        # 避免重复初始化
        if getattr(self, '_initialized', False):
            return
            
        # 设置初始化标志，防止重复初始化
        self._initialized = True
            
        # 如果未提供日志目录，尝试从环境变量获取
        if logs_dir is None:
            logs_dir = os.environ.get('AUDIO_SEPARATION_LOGS_DIR', 'logs')
            
        # 确保日志目录存在
        os.makedirs(logs_dir, exist_ok=True)
        
        # 配置日志 - 使用全局锁确保线程安全
        global _LOGGER_INITIALIZED, _LOGGER_LOCK
        with _LOGGER_LOCK:
            if not _LOGGER_INITIALIZED:
                self.logger = logging.getLogger('signal_handler')
                signal_log_path = os.path.join(logs_dir, 'signal.log')
                
                # 检查是否已经有处理器
                if not self.logger.handlers:
                    # 添加文件处理器
                    handler = logging.FileHandler(signal_log_path)
                    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
                    self.logger.addHandler(handler)
                    self.logger.setLevel(logging.INFO)
                    
                    # 记录初始化信息
                    self.logger.info(f"信号处理器日志系统初始化完成，日志保存在 {signal_log_path}")
                
                _LOGGER_INITIALIZED = True
            else:
                # 如果已经初始化，直接使用现有的日志记录器
                self.logger = logging.getLogger('signal_handler')
        
        # 从配置文件加载房间流媒体映射
        self.room_mapping = self._load_room_mapping(config_path)
        
        # 初始化视频保存管理器
        self.saver_manager = StreamSaverManager(self.room_mapping)
        
        # 检查音频处理模块是否可用
        if audio_processor_available:
            self.logger.info("音频处理模块已加载，将自动处理录制的视频")
        else:
            self.logger.warning("未找到音频处理模块，录制的视频将不会自动处理")
            
        # 检查Dify Agent客户端是否可用
        if dify_agent_available:
            self.logger.info("Dify Agent客户端已加载，将自动发送转写结果给Agent")
            self.dify_client = DifyAgentClient(base_url="http://**************:5023")
        else:
            self.logger.warning("未找到Dify Agent客户端，转写结果将不会自动发送给Agent")
        
        # 用于跟踪已处理的转写任务
        self.processed_transcripts = set()
        
        # 用于跟踪已保存到数据库的任务
        self.saved_to_database = set()
        
    def _load_room_mapping(self, config_path=None):
        """
        加载房间流媒体映射
        
        Args:
            config_path: 配置文件路径（用于备用）
            
        Returns:
            dict: 房间映射
        """
        try:
            # 导入房间映射数据访问对象
            from stream_capture.core.db.room_mapping_dao import RoomMappingDAO
            
            # 使用DAO获取所有房间映射
            dao = RoomMappingDAO(config_path)
            room_mappings = dao.get_all_rooms()
            
            # 转换为以room_name为键的字典
            room_dict = {}
            if room_mappings:
                for item in room_mappings:
                    # 处理数据库结果或备用JSON配置结果
                    if isinstance(item, dict):
                        room_dict[item['room_name']] = {
                            'ip_address': item.get('ip_address', item.get('mac_address', '')),
                            'stream_url': item['stream_url']
                        }
            
            self.logger.info(f"成功加载配置文件，房间映射: {list(room_dict.keys())}")    
            return room_dict
            
        except Exception as e:
            self.logger.error(f"加载房间映射失败: {str(e)}")
            self.logger.warning("将使用备用方法加载配置文件")
            
            # 备用方法：直接读取配置文件
            try:
                # 如果提供了特定的配置路径，就使用它
                if config_path is None:
                    # 获取项目根目录路径
                    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                    
                    # 尝试以下几个可能的路径
                    possible_paths = [
                        os.path.join(project_root, 'stream_capture', 'config', 'room_stream_mapping.json'),  # 项目根目录/stream_capture/config
                        os.path.join(project_root, 'config', 'room_stream_mapping.json'),                    # 项目根目录/config
                        os.path.join('stream_capture', 'config', 'room_stream_mapping.json'),               # 相对路径
                        os.path.join('config', 'room_stream_mapping.json'),                                 # 相对路径
                    ]
                    
                    # 尝试各种可能的路径，直到找到配置文件
                    config_found = False
                    for path in possible_paths:
                        if os.path.exists(path):
                            config_path = path
                            config_found = True
                            self.logger.info(f"找到配置文件: {config_path}")
                            break
                    
                    if not config_found:
                        self.logger.error(f"找不到配置文件。尝试路径: {', '.join(possible_paths)}")
                        return {}
                
                # 读取和解析配置文件
                with open(config_path, 'r', encoding='utf-8') as f:
                    mapping = json.load(f)
                    
                # 转换为以room_name为键的字典
                room_dict = {}
                for item in mapping:
                    room_dict[item['room_name']] = {
                        'ip_address': item.get('ip_address', item.get('mac_address', '')),
                        'stream_url': item['stream_url']
                    }
                
                self.logger.info(f"成功从配置文件加载房间映射: {list(room_dict.keys())}")    
                return room_dict
                
            except Exception as e:
                self.logger.error(f"加载备用配置文件失败: {str(e)}")
                return {}
    
    def handle_start_signal(self, signal_data):
        """
        处理开始信号
        
        Args:
            signal_data: 包含信号数据的字典，需包含task_id和mac_address
            
        Returns:
            dict: 处理结果
        """
        try:
            # 提取信号数据
            task_id = signal_data['task_id']
            mac_address = signal_data['mac_address']
            
            # 使用RoomMappingDAO查询房间信息
            from stream_capture.core.db.room_mapping_dao import RoomMappingDAO
            dao = RoomMappingDAO()
            room_info = dao.get_room_by_ip(mac_address)
            
            if not room_info:
                self.logger.error(f"找不到MAC地址 {mac_address} 对应的房间信息")
                return {
                    'status': 'error',
                    'message': f'找不到MAC地址 {mac_address} 对应的房间信息'
                }
            
            # 获取房间名称
            room_name = room_info['room_name']
            self.logger.info(f"通过MAC地址 {mac_address} 找到房间: {room_name}")
            
            # 为兼容起见，设置默认的patient信息
            patient_name = signal_data.get('patient_name', '未知患者')
            patient_id = signal_data.get('patient_id', f'AUTO_{task_id}')
            
            # 新增：提取userid和username字段
            userid = signal_data.get('userid', '')
            username = signal_data.get('username', '')
            self.logger.info(f"接收到用户信息: userid={userid}, username={username}")
            
            # 保存医疗信息到文件，如果存在的话
            medical_info = {}
            if 'medical_info' in signal_data and signal_data['medical_info']:
                medical_info = signal_data['medical_info']
            
            # 在medical_info中添加userid和username，以便在后续处理中使用
            if userid:
                medical_info['userid'] = userid
            if username:
                medical_info['username'] = username
            
            # 确保目录存在
            transcript_dir = os.path.join('SeparatedAudioText', room_name, task_id)
            os.makedirs(transcript_dir, exist_ok=True)
            
            # 保存医疗信息到文件
            if medical_info:
                medical_info_path = os.path.join(transcript_dir, 'medical_info.json')
                with open(medical_info_path, 'w', encoding='utf-8') as f:
                    json.dump(medical_info, f, ensure_ascii=False, indent=2)
                self.logger.info(f"已保存医疗信息到文件: {medical_info_path}")
            
            # 开始录制视频
            result = self.saver_manager.start_recording(
                task_id=task_id,
                room_id=room_name,
                patient_id=patient_id,
                patient_name=patient_name,
                userid=userid,
                username=username
            )
            
            # 添加房间信息到结果中
            if result['status'] == 'success':
                result['room_name'] = room_name
                result['stream_url'] = room_info['stream_url']
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理开始信号时发生错误: {str(e)}")
            return {
                'status': 'error',
                'message': f'处理开始信号时发生错误: {str(e)}'
            }
    
    def handle_stop_signal(self, signal_data):
        """
        处理停止信号，停止录制视频并触发音频处理
        
        Args:
            signal_data: 包含信号数据的字典
            
        Returns:
            dict: 处理结果
        """
        try:
            # 提取信号数据
            task_id = signal_data['task_id']
            
            # 停止录制视频
            result = self.saver_manager.stop_recording(task_id)
            
            # 如果成功停止录制且音频处理模块可用，则自动处理录制的视频
            if result['status'] == 'success' and audio_processor_available:
                # 在新线程中处理录制的视频，避免阻塞API响应
                processing_thread = threading.Thread(
                    target=self._process_recording,
                    args=(task_id,)
                )
                processing_thread.daemon = True
                processing_thread.start()
                self.logger.info(f"已启动后台线程处理任务 {task_id} 的录制视频")
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理停止信号时发生错误: {str(e)}")
            return {
                'status': 'error',
                'message': f'处理停止信号时发生错误: {str(e)}'
            }
    
    def _process_recording(self, task_id):
        """
        处理录制的视频
        
        Args:
            task_id: 任务ID
        """
        try:
            # 查找录制的视频文件
            # 使用相对路径
            recordings_dir = os.path.join('stream_capture', 'recordings')
            
            # 查找对应的房间和视频文件
            room_name = None
            video_path = None
            for room in self.room_mapping.keys():
                path = os.path.join(recordings_dir, room, f"{task_id}.mp4")
                if os.path.exists(path):
                    room_name = room
                    video_path = path
                    self.logger.info(f"找到录制的视频文件: {video_path}")
                    break
            
            if not video_path:
                self.logger.warning(f"未找到任务 {task_id} 的录制视频文件")
                return
                    
            # 处理视频文件
            try:
                # 开始处理录制的视频
                self.logger.info(f"开始处理录制的视频: {task_id}")
                process_result = AudioProcessor.process_video(video_path, room_name)
                
                # 检查处理结果
                if process_result and process_result.get('status') == 'success':
                    self.logger.info(f"视频处理成功，任务ID: {task_id}")
                    
                    # 检查并发送转写结果到Dify Agent
                    self._send_transcript_to_agent(task_id, room_name)
                else:
                    self.logger.error(f"视频处理失败，任务ID: {task_id}，结果: {process_result}")
            except Exception as e:
                self.logger.error(f"处理视频时发生错误: {str(e)}")
            
        except Exception as e:
            self.logger.error(f"处理录制视频的整体流程发生错误: {str(e)}")
    
    def _send_transcript_to_agent(self, task_id, room_name):
        """
        将转写结果发送给Dify Agent
        
        Args:
            task_id: 任务ID
            room_name: 房间名称
        """
        if not dify_agent_available:
            self.logger.warning("Dify Agent客户端不可用，无法发送转写结果")
            return
        
        # 检查是否已处理过该转写任务
        if task_id in self.processed_transcripts:
            self.logger.info(f"转写结果已经处理过，任务ID: {task_id}")
            # 如果已处理过转写结果，但还没保存到数据库，则继续保存到数据库
            if task_id not in self.saved_to_database:
                self.logger.info(f"尝试将任务 {task_id} 的数据保存到数据库")
                self._save_to_database(task_id, room_name)
            return
            
        # 添加延迟，确保音频处理和转写已完成
        self.logger.info(f"等待转写完成，5秒后尝试发送到Agent，任务ID: {task_id}")
        time.sleep(5)
        
        # 最大重试次数
        max_retries = 3
        retry_count = 0
        retry_delay = 5  # 重试间隔秒数
            
        while retry_count < max_retries:
            try:
                # 查找转写结果文件
                transcript_dir = os.path.join('SeparatedAudioText', room_name, task_id)
                sentences_path = os.path.join(transcript_dir, 'sentences.json')
                
                if not os.path.exists(sentences_path):
                    self.logger.warning(f"未找到转写结果文件: {sentences_path}, 尝试次数: {retry_count+1}/{max_retries}")
                    retry_count += 1
                    if retry_count < max_retries:
                        self.logger.info(f"等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)
                        # 增加重试延迟，以便有更多时间完成转写
                        retry_delay *= 2
                        continue
                    else:
                        self.logger.error(f"达到最大重试次数，放弃发送转写结果到Agent，任务ID: {task_id}")
                        return
                
                self.logger.info(f"找到转写结果文件: {sentences_path}")
                
                # 尝试加载医疗信息并与转写结果合并
                sentences = self._load_and_merge_medical_info(sentences_path, transcript_dir)
                
                # 使用修改后的sentences数据发送到Dify Agent
                self.logger.info(f"开始发送转写结果到Dify Agent，任务ID: {task_id}")
                
                # 修改调用方式，直接发送组合后的数据，而不是文件路径
                formatted_text = f"转写结果: {json.dumps(sentences, ensure_ascii=False)}"
                response = self.dify_client.send_to_agent(formatted_text, "streaming")
                
                if response:
                    self.logger.info(f"转写结果已成功发送到Dify Agent，任务ID: {task_id}")
                    
                    # 从响应中提取纯回复内容
                    answer_content = response.get('answer', '')
                    
                    # 将Agent的响应保存到文件
                    # 先保存完整响应到agent_response.json以保持兼容性
                    compat_path = os.path.join(transcript_dir, 'agent_response.json')
                    with open(compat_path, 'w', encoding='utf-8') as f:
                        json.dump(response, f, ensure_ascii=False, indent=2)
                    self.logger.info(f"完整响应已保存到: {compat_path}")
                    
                    # 使用task_id作为文件名，并只保存纯回复内容
                    agent_response_path = os.path.join(transcript_dir, f"{task_id}.json")
                    
                    # 尝试解析回复内容是否为JSON
                    try:
                        # 尝试解析为JSON对象
                        json_content = json.loads(answer_content)
                        # 如果成功解析为JSON，直接以JSON格式保存
                        with open(agent_response_path, 'w', encoding='utf-8') as f:
                            # 使用json.dump保存，以保持格式完全一致
                            json.dump(json_content, f, ensure_ascii=False, indent=2)
                        
                        # 电子病历结果已保存到文件，将直接由数据库保存工具处理    
                        if isinstance(json_content, dict):
                            self.logger.info(f"电子病历结果已保存到文件，任务ID: {task_id}")
                        else:
                            self.logger.warning(f"Agent返回的JSON内容不是字典格式，但已保存到文件: {task_id}")
                    except json.JSONDecodeError:
                        # 如果不是JSON格式，直接保存原始字符串
                        with open(agent_response_path, 'w', encoding='utf-8') as f:
                            f.write(answer_content)
                        self.logger.warning(f"Agent返回的内容不是有效的JSON，已保存为文本: {task_id}")
                    
                    self.logger.info(f"Agent纯回复内容已保存到: {agent_response_path}")
                    
                    # 标记为已处理
                    self.processed_transcripts.add(task_id)
                    
                    # 调用数据库保存方法
                    self.logger.info(f"开始保存任务数据到数据库: {task_id}")
                    self._save_to_database(task_id, room_name)
                    
                    return True
                else:
                    self.logger.error(f"发送转写结果到Dify Agent失败，任务ID: {task_id}")
                    retry_count += 1
                    if retry_count < max_retries:
                        self.logger.info(f"等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)
                        retry_delay *= 2
                        continue
                    else:
                        self.logger.error(f"达到最大重试次数，放弃发送转写结果到Agent，任务ID: {task_id}")
                        return False
                    
            except Exception as e:
                self.logger.error(f"发送转写结果到Dify Agent时发生错误: {str(e)}")
                retry_count += 1
                if retry_count < max_retries:
                    self.logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    self.logger.error(f"达到最大重试次数，放弃发送转写结果到Agent，任务ID: {task_id}")
                    return False
    
    def handle_transcript_ready(self, task_id, room_name):
        """
        处理转写就绪通知，在sentences.json文件生成后被调用
        
        这个方法设计为可以在任何时候被调用，不受重试次数限制
        
        Args:
            task_id: 任务ID
            room_name: 房间名称
            
        Returns:
            bool: 是否成功处理
        """
        if not dify_agent_available:
            self.logger.warning("Dify Agent客户端不可用，无法发送转写结果")
            return False
        
        # 检查是否已处理过该转写任务
        if task_id in self.processed_transcripts:
            self.logger.info(f"转写结果已经处理过，任务ID: {task_id}")
            # 如果已处理过转写结果，但还没保存到数据库，则继续保存到数据库
            if task_id not in self.saved_to_database:
                self.logger.info(f"尝试将任务 {task_id} 的数据保存到数据库")
                return self._save_to_database(task_id, room_name)
            return True
        
        try:
            # 查找转写结果文件
            transcript_dir = os.path.join('SeparatedAudioText', room_name, task_id)
            sentences_path = os.path.join(transcript_dir, 'sentences.json')
            
            if not os.path.exists(sentences_path):
                self.logger.warning(f"收到转写就绪通知，但未找到转写结果文件: {sentences_path}")
                return False
            
            self.logger.info(f"找到转写结果文件: {sentences_path}")
            
            # 尝试加载医疗信息并与转写结果合并
            sentences = self._load_and_merge_medical_info(sentences_path, transcript_dir)
            
            # 使用修改后的sentences数据发送到Dify Agent
            self.logger.info(f"开始发送转写结果到Dify Agent，任务ID: {task_id}")
            
            # 修改调用方式，直接发送组合后的数据，而不是文件路径
            formatted_text = f"转写结果: {json.dumps(sentences, ensure_ascii=False)}"
            response = self.dify_client.send_to_agent(formatted_text, "streaming")
            
            if response:
                self.logger.info(f"转写结果已成功发送到Dify Agent，任务ID: {task_id}")
                
                # 从响应中提取纯回复内容
                answer_content = response.get('answer', '')
                
                # 将Agent的响应保存到文件
                # 先保存完整响应到agent_response.json以保持兼容性
                compat_path = os.path.join(transcript_dir, 'agent_response.json')
                with open(compat_path, 'w', encoding='utf-8') as f:
                    json.dump(response, f, ensure_ascii=False, indent=2)
                self.logger.info(f"完整响应已保存到: {compat_path}")
                
                # 使用task_id作为文件名，并只保存纯回复内容
                agent_response_path = os.path.join(transcript_dir, f"{task_id}.json")
                
                # 尝试解析回复内容是否为JSON
                try:
                    # 尝试解析为JSON对象
                    json_content = json.loads(answer_content)
                    # 如果成功解析为JSON，直接以JSON格式保存
                    with open(agent_response_path, 'w', encoding='utf-8') as f:
                        # 使用json.dump保存，以保持格式完全一致
                        json.dump(json_content, f, ensure_ascii=False, indent=2)
                    
                    # 电子病历结果已保存到文件，将直接由数据库保存工具处理
                    if isinstance(json_content, dict):
                        self.logger.info(f"电子病历结果已保存到文件，任务ID: {task_id}")
                    else:
                        self.logger.warning(f"Agent返回的JSON内容不是字典格式，但已保存到文件: {task_id}")
                except json.JSONDecodeError:
                    # 如果不是JSON格式，直接保存原始字符串
                    with open(agent_response_path, 'w', encoding='utf-8') as f:
                        f.write(answer_content)
                    self.logger.warning(f"Agent返回的内容不是有效的JSON，已保存为文本: {task_id}")
                
                self.logger.info(f"Agent纯回复内容已保存到: {agent_response_path}")
                
                # 标记为已处理
                self.processed_transcripts.add(task_id)
                
                # 调用数据库保存方法
                self.logger.info(f"开始保存任务数据到数据库: {task_id}")
                return self._save_to_database(task_id, room_name)
            else:
                self.logger.error(f"发送转写结果到Dify Agent失败，任务ID: {task_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"处理转写就绪通知时发生错误: {str(e)}")
            return False
    
    def get_task_status(self):
        """
        获取当前任务状态
        
        Returns:
            dict: 任务状态字典
        """
        active_tasks = self.saver_manager.active_tasks
        return {task_id: "recording" for task_id in active_tasks.keys()}

    def _save_to_database(self, task_id, room_name):
        """
        调用media_uploader.py脚本将数据保存到数据库
        
        Args:
            task_id: 任务ID
            room_name: 房间名称
            
        Returns:
            bool: 是否成功保存
        """
        try:
            # 检查任务是否已经保存到数据库
            if task_id in self.saved_to_database:
                self.logger.info(f"任务 {task_id} 已经保存到数据库，不再重复保存")
                return True
                
            # 构建命令
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            script_path = os.path.join(project_root, "front", "media_uploader.py")
            
            if not os.path.exists(script_path):
                self.logger.error(f"media_uploader.py脚本不存在: {script_path}")
                return False
                
            self.logger.info(f"正在调用media_uploader.py将任务数据保存到数据库: {task_id}")
            
            # 获取用户信息
            userid = ''
            username = ''
            chief_complaint = ''
            
            # 尝试从active_tasks中获取用户信息
            if task_id in self.saver_manager.active_tasks:
                task_info = self.saver_manager.active_tasks[task_id]
                userid = task_info.get('userid', '')
                username = task_info.get('username', '')
            else:
                self.logger.warning(f"无法从active_tasks中获取用户信息，任务可能已结束: {task_id}")
            
            # 尝试从医疗信息文件中获取信息
            medical_info_path = os.path.join('SeparatedAudioText', room_name, task_id, 'medical_info.json')
            if os.path.exists(medical_info_path):
                try:
                    with open(medical_info_path, 'r', encoding='utf-8') as f:
                        medical_info = json.load(f)
                        
                        # 尝试获取主诉
                        chief_complaint = medical_info.get('患者主诉', '')
                        self.logger.info(f"从医疗信息中获取到主诉: {chief_complaint}")
                        
                        # 尝试获取userid和username（如果存在的话）
                        if not userid and 'userid' in medical_info:
                            userid = medical_info['userid']
                            self.logger.info(f"从医疗信息中获取到userid: {userid}")
                        
                        if not username and 'username' in medical_info:
                            username = medical_info['username']
                            self.logger.info(f"从医疗信息中获取到username: {username}")
                except Exception as e:
                    self.logger.error(f"读取医疗信息文件失败: {str(e)}")
            
            # 构建命令行
            cmd = [
                sys.executable,  # 当前Python解释器路径
                script_path,
                "--room", room_name,
                "--task", task_id
            ]
            
            # 添加用户信息参数
            if userid:
                cmd.extend(["--userid", userid])
            if username:
                cmd.extend(["--username", username])
            if chief_complaint:
                cmd.extend(["--chief-complaint", chief_complaint])
            
            # 添加电子病历JSON文件路径作为参数
            medical_record_path = os.path.join('SeparatedAudioText', room_name, task_id, f"{task_id}.json")
            if os.path.exists(medical_record_path):
                cmd.extend(["--medical-record", medical_record_path])
                self.logger.info(f"添加电子病历JSON文件路径: {medical_record_path}")
            else:
                self.logger.warning(f"电子病历JSON文件不存在: {medical_record_path}")
            
            # 使用subprocess启动进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                cwd=project_root  # 设置工作目录为项目根目录
            )
            
            # 记录启动信息
            self.logger.info(f"数据库保存进程已启动，任务ID: {task_id}, PID: {process.pid}")
            
            # 非阻塞方式处理输出，避免主进程被阻塞
            def monitor_process():
                stdout, stderr = process.communicate()
                exit_code = process.wait()
                
                if exit_code == 0:
                    self.logger.info(f"数据库保存成功完成，任务ID: {task_id}")
                    # 将任务标记为已保存到数据库
                    self.saved_to_database.add(task_id)
                    self.logger.debug(f"进程输出: {stdout}")
                else:
                    self.logger.error(f"数据库保存失败，任务ID: {task_id}, 退出码: {exit_code}")
                    self.logger.error(f"进程错误输出: {stderr}")
            
            # 使用线程在后台监控进程
            import threading
            monitor_thread = threading.Thread(target=monitor_process)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动数据库保存进程时发生错误: {str(e)}")
            return False
            
    def _send_result_to_hsi(self, task_id, agent_response=None):
        """
        将电子病历结果发送到HSI系统（该功能已禁用）
        
        此方法已禁用，电子病历结果现在直接保存到数据库中，不再发送到HSI系统。
        保留此方法仅用于兼容现有测试代码。
        
        Args:
            task_id: 任务ID
            agent_response: Agent响应内容
            
        Returns:
            bool: 总是返回True，表示操作"成功"
        """
        self.logger.info(f"尝试向HSI系统发送电子病例结果: {task_id}")
        self.logger.info(f"此功能已禁用，电子病历结果现在直接保存到数据库中")
        return True

    def _load_and_merge_medical_info(self, sentences_path, transcript_dir):
        """
        加载医疗信息并与sentences.json中的转写结果合并
        
        Args:
            sentences_path: sentences.json文件路径
            transcript_dir: 转写结果目录
            
        Returns:
            list: 合并后的数据
        """
        try:
            # 读取sentences.json
            with open(sentences_path, 'r', encoding='utf-8') as f:
                sentences = json.load(f)
                
            # 医疗信息文件路径
            medical_info_path = os.path.join(transcript_dir, 'medical_info.json')
            
            # 检查医疗信息文件是否存在
            if os.path.exists(medical_info_path):
                self.logger.info(f"找到医疗信息文件: {medical_info_path}")
                
                # 读取医疗信息
                with open(medical_info_path, 'r', encoding='utf-8') as f:
                    medical_info = json.load(f)
                
                # 准备医疗信息条目
                medical_item = {}
                
                # 如果医疗信息不为空，将其添加为第一条记录
                if medical_info:
                    medical_item = medical_info  # 直接使用医疗信息字典
                    
                    # 将处理过的医疗信息插入到转写结果的开头
                    merged_sentences = [medical_item] + sentences
                    self.logger.info(f"已将医疗信息合并到转写结果中，医疗字段: {', '.join(medical_info.keys())}")
                    
                    # 保存合并后的数据到新文件，便于调试和验证
                    merged_path = os.path.join(transcript_dir, 'sentences_with_medical_info.json')
                    with open(merged_path, 'w', encoding='utf-8') as f:
                        json.dump(merged_sentences, f, ensure_ascii=False, indent=2)
                    self.logger.info(f"合并后的转写结果已保存到: {merged_path}")
                    
                    return merged_sentences
                else:
                    self.logger.warning(f"医疗信息文件存在但内容为空: {medical_info_path}")
            else:
                self.logger.info(f"未找到医疗信息文件: {medical_info_path}")
            
            # 如果没有医疗信息或处理失败，返回原始转写结果
            return sentences
                
        except Exception as e:
            self.logger.error(f"加载和合并医疗信息时发生错误: {str(e)}")
            
            # 发生错误时，尝试返回原始转写结果
            try:
                with open(sentences_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                # 如果连原始转写结果都无法读取，返回空列表
                self.logger.error(f"无法读取原始转写结果: {sentences_path}")
                return [] 