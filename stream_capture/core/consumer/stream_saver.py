#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
视频流保存模块
------------
此模块负责根据信号控制视频流的录制和保存。
"""

import os
import time
import logging
import subprocess
import threading
from datetime import datetime

class StreamSaver:
    """
    视频流保存器
    
    负责从流媒体URL直接录制视频并保存为MP4文件。
    """
    
    def __init__(self, stream_url, output_path):
        """
        初始化视频流保存器
        
        Args:
            stream_url: 视频流URL
            output_path: 输出文件路径
        """
        self.stream_url = stream_url
        self.output_path = output_path
        self.process = None
        self.running = False
        self.logger = logging.getLogger(f'stream_saver.{os.path.basename(output_path)}')
        
        # 创建输出目录
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    def start(self):
        """开始录制视频"""
        if self.running:
            self.logger.warning(f"已经在录制中: {self.output_path}")
            return False
        
        try:
            # 构建ffmpeg命令，直接从流复制到MP4文件
            cmd = [
                'ffmpeg',
                '-i', self.stream_url,         # 输入流
                '-c', 'copy',                  # 复制编码，不重新编码
                '-f', 'mp4',                   # 输出格式为MP4
                '-movflags', 'faststart',      # 优化MP4文件结构，便于快速播放
                '-y',                          # 覆盖已有文件
                '-loglevel', 'error',          # 只显示错误信息
                self.output_path               # 输出文件路径
            ]
            
            # 启动进程
            self.logger.info(f"开始录制视频: {self.stream_url} -> {self.output_path}")
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE
            )
            
            self.running = True
            
            # 启动监控线程
            self.monitor_thread = threading.Thread(target=self._monitor_process)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"开始录制视频时发生错误: {str(e)}")
            return False
    
    def stop(self):
        """停止录制视频"""
        if not self.running:
            self.logger.warning(f"没有正在进行的录制: {self.output_path}")
            return False
        
        try:
            self.running = False
            
            # 发送退出信号给ffmpeg (q键)
            if self.process:
                self.logger.info(f"正在停止视频录制: {self.output_path}")
                
                # 尝试优雅地结束进程
                try:
                    # 发送q命令给ffmpeg
                    self.process.communicate(input=b'q', timeout=5)
                except subprocess.TimeoutExpired:
                    # 如果超时，则强制终止
                    self.process.terminate()
                    try:
                        self.process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        self.process.kill()
                
                self.process = None
                
            # 检查文件是否生成
            if not os.path.exists(self.output_path) or os.path.getsize(self.output_path) == 0:
                self.logger.error(f"视频文件未生成或大小为零: {self.output_path}")
                return False
                
            self.logger.info(f"已停止视频录制，文件已保存: {self.output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"停止录制视频时发生错误: {str(e)}")
            return False
    
    def is_running(self):
        """检查是否正在录制"""
        return self.running and self.process and self.process.poll() is None
    
    def _monitor_process(self):
        """监控ffmpeg进程"""
        while self.running and self.process and self.process.poll() is None:
            time.sleep(1)
        
        # 如果进程意外结束
        if self.running:
            self.running = False
            if self.process and self.process.poll() is not None:
                stderr = self.process.stderr.read().decode('utf-8', errors='ignore')
                self.logger.error(f"ffmpeg进程意外结束，错误信息: {stderr}")
                
class StreamSaverManager:
    """
    视频流保存管理器
    
    管理多个视频录制任务，提供统一的接口进行控制。
    """
    
    def __init__(self, room_mapping):
        """
        初始化视频流保存管理器
        
        Args:
            room_mapping: 房间-流媒体URL映射
        """
        self.room_mapping = room_mapping
        self.active_tasks = {}  # 任务ID -> StreamSaver
        self.logger = logging.getLogger('stream_saver_manager')
    
    def start_recording(self, task_id, room_id, patient_id, patient_name, userid='', username=''):
        """
        开始录制视频
        
        Args:
            task_id: 任务ID
            room_id: 诊室ID
            patient_id: 患者ID
            patient_name: 患者姓名
            userid: 用户ID
            username: 用户姓名
            
        Returns:
            dict: 处理结果
        """
        try:
            # 检查任务是否已存在
            if task_id in self.active_tasks:
                self.logger.warning(f"任务已存在: {task_id}")
                return {
                    'status': 'error',
                    'message': f'任务已存在: {task_id}'
                }
            
            # 检查房间是否在映射中
            if room_id not in self.room_mapping:
                self.logger.error(f"房间不存在于映射中: {room_id}")
                return {
                    'status': 'error',
                    'message': f'房间不存在于映射中: {room_id}'
                }
            
            # 获取流媒体URL
            stream_url = self.room_mapping[room_id]['stream_url']
            
            # 生成输出文件路径：stream_capture/recordings/诊室名称/任务ID.mp4
            # 使用相对路径，确保在项目迁移时路径正确
            recordings_dir = os.path.join('stream_capture', 'recordings')
            room_dir = os.path.join(recordings_dir, room_id)
            os.makedirs(room_dir, exist_ok=True)
            
            video_path = os.path.join(room_dir, f"{task_id}.mp4")
            
            # 创建并启动视频保存器
            saver = StreamSaver(stream_url, video_path)
            if not saver.start():
                return {
                    'status': 'error',
                    'message': f'启动视频录制失败: {task_id}'
                }
            
            # 记录开始时间
            now = datetime.now()
            
            # 保存任务信息
            self.active_tasks[task_id] = {
                'saver': saver,
                'room_id': room_id,
                'patient_id': patient_id,
                'patient_name': patient_name,
                'userid': userid,
                'username': username,
                'start_time': now,
                'video_path': video_path
            }
            
            self.logger.info(f"已启动视频录制: {task_id}, 房间: {room_id}, 患者: {patient_name}, 保存路径: {video_path}")
            
            # 简化返回结果
            return {
                'status': 'success',
                'message': f'成功启动视频录制: {task_id}'
            }
            
        except Exception as e:
            self.logger.error(f"开始录制时发生错误: {str(e)}")
            return {
                'status': 'error',
                'message': f'开始录制时发生错误: {str(e)}'
            }
    
    def stop_recording(self, task_id):
        """
        停止录制视频
        
        Args:
            task_id: 任务ID
            
        Returns:
            dict: 处理结果
        """
        try:
            # 检查任务是否存在
            if task_id not in self.active_tasks:
                self.logger.warning(f"任务不存在: {task_id}")
                return {
                    'status': 'error',
                    'message': f'任务不存在: {task_id}'
                }
            
            # 获取任务信息
            task_info = self.active_tasks[task_id]
            saver = task_info['saver']
            
            # 停止录制
            if not saver.stop():
                return {
                    'status': 'error',
                    'message': f'停止视频录制失败: {task_id}'
                }
            
            # 记录结束时间
            end_time = datetime.now()
            duration = end_time - task_info['start_time']
            
            # 移除任务
            self.active_tasks.pop(task_id)
            
            self.logger.info(f"已停止视频录制: {task_id}, 持续时间: {duration}, 保存路径: {task_info['video_path']}")
            
            # 简化返回结果
            return {
                'status': 'success',
                'message': f'成功停止视频录制: {task_id}'
            }
            
        except Exception as e:
            self.logger.error(f"停止录制时发生错误: {str(e)}")
            return {
                'status': 'error',
                'message': f'停止录制时发生错误: {str(e)}'
            }
