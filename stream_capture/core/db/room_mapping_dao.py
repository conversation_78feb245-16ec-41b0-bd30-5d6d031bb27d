#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
房间映射数据访问对象
----------------
此模块提供对房间流媒体映射表的数据库访问接口，
用于查询诊室名称与流媒体URL之间的映射关系。
"""

import os
import sys
import json
import logging
import threading
from typing import Dict, List, Optional, Any
import time

# 尝试导入pymysql
try:
    import pymysql
    pymysql_available = True
except ImportError:
    pymysql_available = False
    logging.warning("pymysql模块未找到，将使用本地JSON配置文件作为备用")

# 数据库配置
DB_CONFIG = {
    "host": "*************",
    "port": 3307,
    "user": "sgkj",
    "password": "cdutcm@123",
    "database": "chd6_4_yph_test",
    "charset": "utf8mb4"
}

class RoomMappingDAO:
    """房间映射数据访问对象"""
    
    # 单例实例
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, *args, **kwargs):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(RoomMappingDAO, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance
    
    def __init__(self, config_path=None, logs_dir=None):
        """
        初始化房间映射DAO
        
        Args:
            config_path: 配置文件路径（用于备用）
            logs_dir: 日志目录
        """
        # 避免重复初始化
        if getattr(self, '_initialized', False):
            return
            
        # 设置日志
        self.logger = logging.getLogger('room_mapping_dao')
        
        # 数据库连接
        self.conn = None
        self.cursor = None
        
        # 本地缓存，避免频繁查询数据库
        self.mapping_cache = {}
        self.last_cache_update = 0
        self.cache_ttl = 30  # 缓存有效期（秒）
        
        # 备用配置文件路径
        self.config_path = config_path
        
        # 初始化数据库连接
        self.init_db_connection()
        
        # 标记为已初始化
        self._initialized = True
    
    def init_db_connection(self) -> bool:
        """
        初始化数据库连接
        
        Returns:
            bool: 是否成功连接
        """
        if not pymysql_available:
            self.logger.warning("pymysql模块未安装，无法连接数据库")
            return False
        
        try:
            self.conn = pymysql.connect(
                host=DB_CONFIG["host"],
                port=DB_CONFIG["port"],
                user=DB_CONFIG["user"],
                password=DB_CONFIG["password"],
                database=DB_CONFIG["database"],
                charset=DB_CONFIG["charset"],
                connect_timeout=5  # 5秒连接超时
            )
            self.cursor = self.conn.cursor(pymysql.cursors.DictCursor)  # 使用字典游标
            self.logger.info("成功连接到数据库")
            return True
        except Exception as e:
            self.logger.error(f"连接数据库失败: {str(e)}")
            self.logger.info("将使用本地配置文件作为备用")
            return False
    
    def ensure_connection(self) -> bool:
        """
        确保数据库连接有效
        
        Returns:
            bool: 连接是否有效
        """
        if not pymysql_available:
            return False
            
        try:
            if self.conn is None or self.cursor is None:
                return self.init_db_connection()
            
            # 检查连接是否有效
            self.conn.ping(reconnect=True)
            return True
        except Exception as e:
            self.logger.error(f"数据库连接检查失败: {str(e)}")
            
            # 尝试重新连接
            try:
                if self.cursor:
                    self.cursor.close()
                if self.conn:
                    self.conn.close()
                
                return self.init_db_connection()
            except Exception as re:
                self.logger.error(f"重新连接数据库失败: {str(re)}")
                return False
    
    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
            self.logger.info("数据库连接已关闭")
    
    def get_room_by_ip(self, ip_address: str) -> Optional[Dict]:
        """
        根据MAC地址查询房间信息
        
        Args:
            ip_address: MAC地址
            
        Returns:
            Optional[Dict]: 房间信息，如果未找到则返回None
        """
        # 先检查缓存
        cache_key = f"mac_{ip_address}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result
        
        # 尝试从数据库查询
        if self.ensure_connection():
            try:
                self.cursor.execute("""
                    SELECT * FROM `room_stream_mapping` 
                    WHERE `mac_address` = %s AND `is_active` = 1
                """, (ip_address,))
                
                result = self.cursor.fetchone()
                if result:
                    self.logger.info(f"通过MAC地址 {ip_address} 找到房间: {result['room_name']}")
                    # 更新缓存
                    self._update_cache(cache_key, result)
                    return result
                else:
                    self.logger.warning(f"未通过MAC地址 {ip_address} 找到房间")
            except Exception as e:
                self.logger.error(f"根据MAC地址查询房间失败: {str(e)}")
        
        # 尝试从备用配置文件查询
        return self._find_in_backup_config('mac_address', ip_address)
    
    def get_room_by_stream_url(self, stream_url: str) -> Optional[Dict]:
        """
        根据流媒体URL查询房间信息
        
        Args:
            stream_url: 流媒体URL
            
        Returns:
            Optional[Dict]: 房间信息，如果未找到则返回None
        """
        # 先检查缓存
        cache_key = f"url_{stream_url}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result
        
        # 尝试从数据库查询
        if self.ensure_connection():
            try:
                self.cursor.execute("""
                    SELECT * FROM `room_stream_mapping` 
                    WHERE `stream_url` = %s AND `is_active` = 1
                """, (stream_url,))
                
                result = self.cursor.fetchone()
                if result:
                    self.logger.info(f"通过流媒体URL找到房间: {result['room_name']}")
                    # 确保结果包含mac_address字段
                    if 'mac_address' not in result and 'ip_address' in result:
                        result['mac_address'] = result['ip_address']
                    # 更新缓存
                    self._update_cache(cache_key, result)
                    return result
                else:
                    self.logger.warning(f"未通过流媒体URL {stream_url} 找到房间")
            except Exception as e:
                self.logger.error(f"根据流媒体URL查询房间失败: {str(e)}")
        
        # 尝试从备用配置文件查询
        return self._find_in_backup_config('stream_url', stream_url)
    
    def get_room_by_name(self, room_name: str) -> Optional[Dict]:
        """
        根据房间名称查询信息
        
        Args:
            room_name: 房间名称
            
        Returns:
            Optional[Dict]: 房间信息，如果未找到则返回None
        """
        # 先检查缓存
        cache_key = f"name_{room_name}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result
        
        # 尝试从数据库查询
        if self.ensure_connection():
            try:
                self.cursor.execute("""
                    SELECT * FROM `room_stream_mapping` 
                    WHERE `room_name` = %s AND `is_active` = 1
                """, (room_name,))
                
                result = self.cursor.fetchone()
                if result:
                    self.logger.debug(f"通过房间名称 {room_name} 找到映射信息")
                    # 确保结果包含mac_address字段
                    if 'mac_address' not in result and 'ip_address' in result:
                        result['mac_address'] = result['ip_address']
                    # 更新缓存
                    self._update_cache(cache_key, result)
                    return result
                else:
                    self.logger.warning(f"未通过房间名称 {room_name} 找到映射信息")
            except Exception as e:
                self.logger.error(f"根据房间名称查询映射失败: {str(e)}")
        
        # 尝试从备用配置文件查询
        return self._find_in_backup_config('room_name', room_name)
    
    def get_all_rooms(self) -> List[Dict]:
        """
        获取所有房间映射
        
        Returns:
            List[Dict]: 房间映射列表
        """
        # 先检查缓存
        cache_key = "all_rooms"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result
        
        # 尝试从数据库查询
        if self.ensure_connection():
            try:
                self.cursor.execute("""
                    SELECT * FROM `room_stream_mapping` 
                    WHERE `is_active` = 1
                    ORDER BY `id`
                """)
                
                results = self.cursor.fetchall()
                self.logger.info(f"获取到 {len(results)} 条房间映射记录")
                
                # 确保所有结果都包含mac_address字段
                for result in results:
                    if 'mac_address' not in result and 'ip_address' in result:
                        result['mac_address'] = result['ip_address']
                
                # 更新缓存
                self._update_cache(cache_key, results)
                return results
            except Exception as e:
                self.logger.error(f"获取所有房间映射失败: {str(e)}")
        
        # 尝试从备用配置文件查询
        backup_data = self._load_backup_config()
        if backup_data:
            return backup_data
        return []
    
    def _get_from_cache(self, key: str) -> Any:
        """
        从缓存中获取数据
        
        Args:
            key: 缓存键名
            
        Returns:
            Any: 缓存的数据，如果不存在或已过期则返回None
        """
        now = time.time()
        if now - self.last_cache_update > self.cache_ttl:
            # 缓存已过期，清空
            self.mapping_cache.clear()
            return None
        
        return self.mapping_cache.get(key)
    
    def _update_cache(self, key: str, data: Any):
        """
        更新缓存
        
        Args:
            key: 缓存键名
            data: 要缓存的数据
        """
        self.mapping_cache[key] = data
        self.last_cache_update = time.time()
    
    def _load_backup_config(self) -> List[Dict]:
        """
        加载备用配置文件
        
        Returns:
            List[Dict]: 配置数据
        """
        try:
            if self.config_path is None:
                # 尝试多个可能的配置文件路径
                possible_paths = [
                    os.path.join('stream_capture', 'config', 'room_stream_mapping.json'),
                    os.path.join('config', 'room_stream_mapping.json'),
                    'room_stream_mapping.json'
                ]
                
                # 获取项目根目录路径
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                
                # 添加基于项目根目录的路径
                possible_paths.extend([
                    os.path.join(project_root, path) for path in possible_paths
                ])
                
                for path in possible_paths:
                    if os.path.exists(path):
                        self.config_path = path
                        self.logger.info(f"找到备用配置文件: {self.config_path}")
                        break
                else:
                    self.logger.error(f"找不到备用配置文件，尝试路径: {', '.join(possible_paths)}")
                    return []
            
            # 读取配置文件
            with open(self.config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            if not isinstance(data, list):
                self.logger.error(f"备用配置文件格式错误，应为列表: {self.config_path}")
                return []
            
            # 确保所有项都有mac_address字段
            for item in data:
                if 'mac_address' not in item and 'ip_address' in item:
                    item['mac_address'] = item['ip_address']
                elif 'ip_address' not in item and 'mac_address' in item:
                    item['ip_address'] = item['mac_address']
                    
            self.logger.info(f"成功加载备用配置文件，共 {len(data)} 个房间")
            return data
            
        except Exception as e:
            self.logger.error(f"加载备用配置文件失败: {str(e)}")
            return []
    
    def _find_in_backup_config(self, key: str, value: str) -> Optional[Dict]:
        """
        在备用配置文件中查找匹配项
        
        Args:
            key: 键名
            value: 要匹配的值
            
        Returns:
            Optional[Dict]: 匹配的项，如果未找到则返回None
        """
        backup_data = self._load_backup_config()
        
        for item in backup_data:
            if key == 'mac_address' and ('mac_address' in item and item['mac_address'] == value or 
                                        'ip_address' in item and item['ip_address'] == value):
                # 格式化为与数据库结果兼容的格式
                result = {
                    'id': 0,  # 占位ID
                    'room_name': item['room_name'],
                    'mac_address': item.get('mac_address', item.get('ip_address', '')),
                    'stream_url': item['stream_url'],
                    'description': item.get('description', ''),
                    'is_active': 1
                }
                self.logger.info(f"在备用配置中通过 {key}={value} 找到房间: {result['room_name']}")
                return result
            elif key in item and item[key] == value:
                # 格式化为与数据库结果兼容的格式
                result = {
                    'id': 0,  # 占位ID
                    'room_name': item['room_name'],
                    'mac_address': item.get('mac_address', item.get('ip_address', '')),
                    'stream_url': item['stream_url'],
                    'description': item.get('description', ''),
                    'is_active': 1
                }
                self.logger.info(f"在备用配置中通过 {key}={value} 找到房间: {result['room_name']}")
                return result
                
        self.logger.warning(f"在备用配置中未通过 {key}={value} 找到房间")
        return None 