# 视频流捕获系统设计文档

## 1. 系统概述

视频流捕获系统是一个基于API信号触发的按需视频录制模型。当接收到HSI系统发送的"开始"信号时，系统会根据房间ID找到对应的视频流URL，并开始录制视频。当接收到"停止"信号时，系统会停止录制并保存视频文件，同时启动音频处理，分离说话人并生成转写文本。

### 功能特点

1. 支持多房间视频流并发录制
2. 基于API信号触发的录制控制
3. 结构化的视频文件存储
4. 集成音频处理功能，自动分离说话人并转写
5. 完善的日志记录和异常处理
6. 通过IP地址区分不同诊室的视频流来源

## 2. 目录结构

```
stream_capture/
├── config/                 # 配置文件目录
│   └── room_stream_mapping.json  # 房间与流媒体URL映射
├── core/                   # 核心模块
│   ├── consumer/           # 流媒体消费者模块
│   │   └── stream_saver.py  # 视频流保存器
│   └── signal/             # 信号处理模块
│       └── signal_handler.py  # 信号处理器
├── api/                    # API接口模块
│   └── signal_receiver.py  # 信号接收服务
├── logs/                   # 日志目录
├── recordings/             # 录制视频保存目录
│   ├── 诊室1/              # 按房间名称组织的子目录
│   │   └── task_id.mp4     # 录制的视频文件
│   └── 诊室2/              # 另一个房间的子目录
├── main.py                 # 主程序
├── 设计任务说明.md           # 设计说明文档
└── HSI系统接口交互说明.md     # 接口说明文档
```

## 3. 核心模块设计

### 3.1 视频流录制模块

- **文件位置**: `core/consumer/stream_saver.py`
- **主要功能**:
  - 连接到指定的视频流URL
  - 创建视频保存线程
  - 将视频流内容保存为MP4文件
  - 管理多个并发录制任务

### 3.2 信号处理模块

- **文件位置**: `core/signal/signal_handler.py`
- **主要功能**:
  - 处理从API接收到的"开始"和"停止"信号
  - 根据信号中的房间ID找到对应的视频流URL
  - 调用视频流保存器开始或停止录制
  - 在视频停止录制后，自动调用音频处理模块
  - 处理与音频处理模块的集成

### 3.3 API接口模块

- **文件位置**: `api/signal_receiver.py`
- **主要功能**:
  - 提供RESTful API接口，接收HSI系统发送的信号
  - 验证信号数据的有效性
  - 将信号传递给信号处理器
  - 返回处理结果

### 3.4 音频处理集成

- **文件位置**: 
  - `main.py` (ASR模型预加载)
  - `core/signal/signal_handler.py` (触发音频处理)
- **主要功能**:
  - 系统启动时预加载ASR模型，提高处理速度
  - 视频录制结束后自动触发音频处理
  - 将视频提取为音频，进行说话人分离和转写
  - 生成分离后的音频文件和转写文本
  - 保存处理结果到`SeparatedAudioText`目录下

### 3.5 房间与IP地址映射

- **文件位置**: `config/room_stream_mapping.json`
- **主要功能**:
  - 定义诊室名称、IP地址和流媒体URL之间的映射关系
  - 用于区分不同诊室的视频流来源
  - 支持动态配置和更新
  - 提供系统扩展性，便于添加新诊室

房间映射配置示例：
```json
[
  {
    "room_name": "A402诊室",
    "ip_address": "************",
    "stream_url": "https://livell.cdutcm.edu.cn/live/f2ya.live.flv"
  },
  {
    "room_name": "笔记本电脑",
    "ip_address": "**************",
    "stream_url": "https://livell.cdutcm.edu.cn/live/efy1a.live.flv"
  }
]
```

这个配置使系统能够根据房间ID或IP地址识别视频流的来源，避免不同诊室的视频流混淆。

## 4. 数据流程

### 4.1 初始化流程

1. 加载房间与流媒体URL的映射配置
2. 初始化视频流保存器
3. 启动信号接收服务
4. 预加载ASR模型（如启用）
5. 准备接收信号

### 4.2 信号处理流程

1. 接收到"开始"信号
2. 提取信号中的任务ID、房间ID等信息
3. 查找对应的视频流URL
4. 启动视频录制
5. 返回处理结果

### 4.3 视频录制流程

1. 连接到视频流URL
2. 按帧读取视频流内容
3. 保存为MP4文件
4. 接收到"停止"信号时，完成录制并关闭文件

### 4.4 音频处理流程

1. 视频录制停止后，自动查找录制的视频文件
2. 使用ffmpeg从视频中提取音频
3. 调用ASR模型进行说话人分离和语音识别
4. 生成分离后的音频文件和转写文本
5. 保存处理结果到`SeparatedAudioText`目录下

## 5. 异常处理机制

### 5.1 视频录制异常

- 无法连接视频流URL时，记录错误并返回失败结果
- 录制过程中网络中断，自动重试连接
- 磁盘空间不足，停止录制并返回错误

### 5.2 信号处理异常

- 接收到无效的信号数据，返回错误响应
- 找不到对应的房间ID，记录错误并返回失败结果
- 重复的开始/停止信号，处理为幂等操作

### 5.3 音频处理异常

- ASR模型加载失败，记录错误并继续运行（不中断视频录制功能）
- 音频提取失败，记录错误并尝试其他后续视频处理
- 处理过程中出现异常，捕获并记录，不影响主服务

## 6. 文件组织

所有录制的视频将保存在`recordings`目录下，按房间名称组织子目录，并使用任务ID作为文件名。

音频处理结果将保存在`SeparatedAudioText`目录下，按照以下结构组织：

```
SeparatedAudioText/
├── 诊室1/              # 按房间名称组织的子目录
│   └── task_id/        # 按任务ID组织的子目录
│       ├── full_transcript.txt              # 完整转写文本
│       ├── speaker_0.txt                    # 说话人0的转写文本
│       ├── speaker_0.mp3                    # 说话人0的音频文件
│       ├── speaker_1.txt                    # 说话人1的转写文本
│       ├── speaker_1.mp3                    # 说话人1的音频文件
│       └── ...                             # 其他说话人文件
└── 诊室2/              # 另一个房间的子目录
```

## 7. 使用说明

### 7.1 启动服务

使用提供的启动脚本启动服务:

```bash
bash start_service.sh
```

### 7.2 调用API

通过HTTP请求触发视频录制：

#### 开始录制

```bash
curl -X POST http://服务器IP:5000/api/signal/start \
  -H "Content-Type: application/json" \
  -d '{
    "patient_name": "患者姓名",
    "patient_id": "患者ID",
    "task_id": "任务ID",
    "room_id": "房间ID"
  }'
```

#### 停止录制

```bash
curl -X POST http://服务器IP:5000/api/signal/stop \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "任务ID"
  }'
```

### 7.3 停止服务

使用提供的停止脚本停止服务:

```bash
bash stop_service.sh
```

## 8. 注意事项

1. 确保配置文件中的房间ID与HSI系统中的一致
2. 确保服务器有足够的磁盘空间用于视频录制
3. 服务启动时会自动创建必要的目录结构
4. 视频文件将按房间名称组织，并使用任务ID作为文件名
5. 音频处理结果将保存在SeparatedAudioText目录下，按房间名称和任务ID组织
