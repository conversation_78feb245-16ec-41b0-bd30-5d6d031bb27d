#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API入口
-----
此模块为API服务的入口，注册所有API蓝图。
"""

import os
import logging
from typing import Dict, List, Optional, Any

# 尝试导入Flask
try:
    from flask import Flask, jsonify
    from flask_cors import CORS
    flask_available = True
except ImportError:
    flask_available = False
    logging.warning("Flask模块未安装，API服务将不可用")

# 导入各个API模块
if flask_available:
    try:
        from stream_capture.api.room_mapping_api import register_api as register_room_mapping_api
    except ImportError:
        logging.warning("无法导入房间映射API模块")
        register_room_mapping_api = None


class APIServer:
    """API服务器"""
    
    def __init__(self, host='0.0.0.0', port=5000, debug=False):
        """
        初始化API服务器
        
        Args:
            host: 监听地址
            port: 监听端口
            debug: 是否开启调试模式
        """
        if not flask_available:
            logging.error("Flask模块未安装，无法启动API服务")
            self.app = None
            return
        
        self.logger = logging.getLogger('api_server')
        self.host = host
        self.port = port
        self.debug = debug
        
        # 创建Flask应用
        self.app = Flask(__name__)
        
        # 启用CORS
        CORS(self.app)
        
        # 配置
        self.app.config['JSON_AS_ASCII'] = False
        self.app.config['JSONIFY_PRETTYPRINT_REGULAR'] = True
        
        # 注册路由
        self._register_routes()
        
        self.logger.info("API服务器已初始化")
    
    def _register_routes(self):
        """注册API路由"""
        if not self.app:
            return
        
        # 根路由
        @self.app.route('/')
        def root():
            return jsonify({
                'code': 200,
                'message': 'API服务器运行正常',
                'data': {
                    'version': '1.0.0',
                    'status': 'running'
                }
            })
        
        # 注册房间映射API
        if register_room_mapping_api:
            register_room_mapping_api(self.app)
        
        self.logger.info("API路由已注册")
    
    def run(self):
        """运行API服务器"""
        if not self.app:
            self.logger.error("Flask应用未初始化，无法启动API服务")
            return False
        
        try:
            self.logger.info(f"启动API服务器: {self.host}:{self.port}")
            self.app.run(host=self.host, port=self.port, debug=self.debug)
            return True
        except Exception as e:
            self.logger.error(f"启动API服务器失败: {str(e)}")
            return False


def start_api_server(host='0.0.0.0', port=5000, debug=False):
    """
    启动API服务器
    
    Args:
        host: 监听地址
        port: 监听端口
        debug: 是否开启调试模式
        
    Returns:
        bool: 是否成功启动
    """
    server = APIServer(host, port, debug)
    return server.run()


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 启动API服务器
    start_api_server(debug=True) 