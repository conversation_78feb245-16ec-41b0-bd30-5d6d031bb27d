#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
信号接收服务
------------
此模块提供用于接收HSI系统发送的"开始"和"停止"信号的RESTful API接口。
接收到的信号将被传递给信号处理器进行进一步处理。
"""

import os
import json
import logging
import threading
import time
from flask import Flask, request, jsonify

from stream_capture.core.signal.signal_handler import SignalHandler

# 模块初始化标志，用于防止重复初始化
_MODULE_INITIALIZED = False

# 全局变量
signal_handler = None
logger = None
_app = None  # 全局应用实例
_app_lock = threading.Lock()  # 应用实例锁

# 日志初始化状态标记
_LOGGING_INITIALIZED = False
_LOGGING_LOCK = threading.Lock()

# 初始化函数，确保模块只初始化一次
def _initialize_module():
    global _MODULE_INITIALIZED
    if _MODULE_INITIALIZED:
        return
    _MODULE_INITIALIZED = True
    # 模块初始化代码可以放在这里

# 调用初始化函数
_initialize_module()

def setup_logging(logs_dir):
    """
    设置API日志 - 使用简单的单例模式确保只初始化一次
    
    Args:
        logs_dir: 日志目录路径
    """
    global logger, _LOGGING_INITIALIZED
    
    # 使用锁确保线程安全
    with _LOGGING_LOCK:
        # 如果日志已经设置，直接返回
        if _LOGGING_INITIALIZED and logger is not None:
            return logger
        
        # 标记为已初始化
        _LOGGING_INITIALIZED = True
        
        # 确保日志目录存在
        os.makedirs(logs_dir, exist_ok=True)
        
        # 设置日志文件路径
        api_log_path = os.path.join(logs_dir, 'api.log')
        
        # 获取或创建日志记录器
        logger = logging.getLogger('signal_api')
        
        # 清除现有处理器，避免重复
        logger.handlers.clear()
        
        # 添加文件处理器
        file_handler = logging.FileHandler(api_log_path)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        logger.addHandler(console_handler)
        
        # 设置日志级别
        logger.setLevel(logging.INFO)

        # 只打印一次初始化完成日志
        logger.info(f"API日志系统初始化完成，日志将保存在 {api_log_path}")
        
        return logger

# 信号处理器初始化状态标记
_SIGNAL_HANDLER_INITIALIZED = False
_SIGNAL_HANDLER_LOCK = threading.Lock()

def init_signal_handler(config_path=None, logs_dir=None):
    """
    初始化信号处理器 - 使用简单的单例模式确保只初始化一次
    
    Args:
        config_path: 配置文件路径
        logs_dir: 日志目录路径
    
    Returns:
        SignalHandler: 初始化的信号处理器
    """
    global signal_handler, logger, _SIGNAL_HANDLER_INITIALIZED
    
    # 使用锁确保线程安全
    with _SIGNAL_HANDLER_LOCK:
        # 如果信号处理器已初始化，直接返回现有实例
        if _SIGNAL_HANDLER_INITIALIZED and signal_handler is not None:
            return signal_handler
        
        # 标记为已初始化
        _SIGNAL_HANDLER_INITIALIZED = True
        
        try:
            # 如果未提供日志目录，尝试从环境变量获取
            if logs_dir is None:
                logs_dir = os.environ.get('AUDIO_SEPARATION_LOGS_DIR', 'logs')
            
            # 设置API日志
            logger = setup_logging(logs_dir)
            
            # 只打印一次初始化日志
            logger.info("初始化信号处理器")
            
            # 初始化信号处理器
            signal_handler = SignalHandler(config_path, logs_dir)
            
            # 只打印一次完成日志
            logger.info("信号处理器初始化完成")
            
            return signal_handler
        except Exception as e:
            # 初始化失败，重置标记
            _SIGNAL_HANDLER_INITIALIZED = False
            if logger:
                logger.error(f"初始化信号处理器失败: {str(e)}")
            raise

def get_app():
    """
    获取Flask应用实例 - 使用单例模式
    
    Returns:
        Flask: Flask应用实例
    """
    global _app, _app_lock
    
    with _app_lock:
        if _app is None:
            _app = Flask(__name__)
            
            # 注册路由和初始化函数
            register_routes(_app)
            
    return _app

def register_routes(app):
    """
    注册API路由
    
    Args:
        app: Flask应用实例
    """
    @app.route('/api/signal/start', methods=['POST'])
    def handle_start_signal():
        """
        处理开始信号
        
        接收HSI系统发送的"开始"信号，开始录制视频流。
        
        请求体示例:
        {
            "task_id": "TASK2025042400001",
            "mac_address": "70:85:c4:61:66:78",
            "chief_complaint": "头痛伴眼部胀痛1周",
            "present_illness": "患者1周前开始出现头痛，夜间伴有眼部胀痛，未进行任何治疗及检查。",
            "past_medical_history": "既往有高血压病史，否认糖尿病、心脏病、肝炎、结核病等病史",
            "allergic_history": "否认药物、食物过敏史"
        }
        
        必要参数:
            task_id: 任务ID
            mac_address: 设备MAC地址
        
        可选参数:
            chief_complaint: 患者主诉
            present_illness: 现病史
            past_medical_history: 既往史
            allergic_history: 过敏史
        
        返回:
            JSON: 处理结果
        """
        try:
            # 获取请求数据
            data = request.get_json()
            
            # 验证必要字段
            required_fields = ['task_id', 'mac_address']
            for field in required_fields:
                if field not in data:
                    return jsonify({
                        'status': 'error',
                        'message': f'缺少必要字段: {field}'
                    }), 400
            
            # 提取医疗信息字段
            medical_fields = {
                'chief_complaint': '患者主诉',
                'present_illness': '现病史',
                'past_medical_history': '既往史',
                'allergic_history': '过敏史'
            }
            
            medical_info = {}
            for field, label in medical_fields.items():
                if field in data and data[field]:
                    medical_info[label] = data[field]
            
            # 添加医疗信息到数据中
            if medical_info:
                data['medical_info'] = medical_info
                logger.info(f"接收到医疗信息字段: {', '.join(medical_info.keys())}")
            
            # 记录日志
            logger.info(f"接收到开始信号: {json.dumps(data, ensure_ascii=False)}")
            
            # 获取信号处理器实例
            global signal_handler
            # 延迟初始化：只在需要时才初始化，并确保不重复
            if signal_handler is None:
                # 初始化会在外部的main.py中完成，这里只是安全检查
                from stream_capture.core.signal.signal_handler import SignalHandler
                signal_handler = SignalHandler()
            result = signal_handler.handle_start_signal(data)
            
            # 简化响应，只返回状态和消息
            if result['status'] == 'success':
                return jsonify({
                    'status': 'success',
                    'message': f'成功启动视频录制: {data["task_id"]}',
                    'room_name': result.get('room_name', '未知诊室'),
                    'stream_url': result.get('stream_url', '未知URL')
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': result.get('message', '处理失败')
                }), 400
            
        except Exception as e:
            logger.error(f"处理开始信号时发生错误: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'服务器内部错误: {str(e)}'
            }), 500
    
    @app.route('/api/signal/stop', methods=['POST'])
    def handle_stop_signal():
        """
        处理停止信号
        
        接收HSI系统发送的"停止"信号，停止录制视频流。
        
        请求体示例:
        {
            "task_id": "TASK2023110100001"
        }
        
        返回:
            JSON: 处理结果
        """
        try:
            # 获取请求数据
            data = request.get_json()
            
            # 验证必要字段
            if 'task_id' not in data:
                return jsonify({
                    'status': 'error',
                    'message': '缺少必要字段: task_id'
                }), 400
            
            # 记录日志
            logger.info(f"接收到停止信号: {json.dumps(data, ensure_ascii=False)}")
            
            # 获取信号处理器实例
            global signal_handler
            # 延迟初始化：只在需要时才初始化，并确保不重复
            if signal_handler is None:
                # 初始化会在外部的main.py中完成，这里只是安全检查
                from stream_capture.core.signal.signal_handler import SignalHandler
                signal_handler = SignalHandler()
            result = signal_handler.handle_stop_signal(data)
            
            # 简化响应，只返回状态和消息
            if result['status'] == 'success':
                return jsonify({
                    'status': 'success',
                    'message': f'成功停止视频录制: {data["task_id"]}'
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': result.get('message', '处理失败')
                }), 404 if '不存在' in result.get('message', '') else 400
            
        except Exception as e:
            logger.error(f"处理停止信号时发生错误: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'服务器内部错误: {str(e)}'
            }), 500

# Flask应用实例会在需要时通过get_app()创建

def start_signal_receiver(host='0.0.0.0', port=5000, debug=False, config_path=None, logs_dir=None):
    """
    启动信号接收服务
    
    Args:
        host: 服务器主机地址
        port: 服务器端口
        debug: 是否开启调试模式
        config_path: 配置文件路径
        logs_dir: 日志目录路径
    """
    # 获取Flask应用实例（信号处理器会在实际需要时初始化）
    app = get_app()
    
    # 确保有logger可用
    global logger
    if logger is None:
        logger = setup_logging(logs_dir or os.environ.get('AUDIO_SEPARATION_LOGS_DIR', 'logs'))
    
    # 启动服务
    logger.info(f"启动信号接收服务，监听地址: {host}:{port}")
    app.run(host=host, port=port, debug=debug, use_reloader=False, threaded=True)


if __name__ == '__main__':
    # 获取日志目录
    logs_dir = os.environ.get('AUDIO_SEPARATION_LOGS_DIR', 'logs')
    
    # 确保日志目录存在
    os.makedirs(logs_dir, exist_ok=True)
    
    # 设置日志
    setup_logging(logs_dir)
    
    # 启动服务
    start_signal_receiver(debug=True, logs_dir=logs_dir) 