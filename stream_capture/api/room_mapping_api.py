#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
房间映射API
--------
此模块提供REST API接口，用于管理房间流媒体映射表。
支持查询、添加、修改、删除等操作。
"""

import json
import logging
import traceback
from typing import Dict, List, Optional, Any

# 尝试导入Flask
try:
    from flask import Blueprint, request, jsonify
    flask_available = True
except ImportError:
    flask_available = False
    logging.warning("Flask模块未安装，房间映射API将不可用")

# 导入房间映射管理器
from front.room_mapping_manager import RoomMappingManager

# 创建蓝图
if flask_available:
    room_mapping_bp = Blueprint('room_mapping', __name__, url_prefix='/api/room-mapping')
    logger = logging.getLogger('room_mapping_api')
else:
    room_mapping_bp = None


# API路由
if flask_available:
    @room_mapping_bp.route('/list', methods=['GET'])
    def list_mappings():
        """获取所有房间映射"""
        try:
            manager = RoomMappingManager()
            if not manager.connect_db():
                return jsonify({
                    'code': 500,
                    'message': '连接数据库失败',
                    'data': None
                }), 500
            
            # 获取是否只查询激活的映射
            active_only = request.args.get('active', 'true').lower() == 'true'
            
            mappings = manager.get_all_mappings(active_only)
            
            # 清理结果，去除敏感信息
            clean_mappings = []
            for item in mappings:
                clean_item = {
                    'id': item['id'],
                    'room_name': item['room_name'],
                    'ip_address': item['ip_address'],
                    'stream_url': item['stream_url'],
                    'description': item['description'],
                    'is_active': item['is_active'],
                    'create_time': item['create_time'].isoformat() if hasattr(item['create_time'], 'isoformat') else item['create_time'],
                    'update_time': item['update_time'].isoformat() if hasattr(item['update_time'], 'isoformat') else item['update_time']
                }
                clean_mappings.append(clean_item)
            
            # 返回结果
            return jsonify({
                'code': 200,
                'message': '成功获取房间映射列表',
                'data': clean_mappings
            })
        except Exception as e:
            logger.error(f"获取房间映射列表失败: {str(e)}")
            logger.error(traceback.format_exc())
            
            return jsonify({
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }), 500
        finally:
            if 'manager' in locals() and manager:
                manager.close_db()
    
    @room_mapping_bp.route('/detail/<int:id>', methods=['GET'])
    def get_mapping_detail(id):
        """获取指定ID的房间映射详情"""
        try:
            manager = RoomMappingManager()
            if not manager.connect_db():
                return jsonify({
                    'code': 500,
                    'message': '连接数据库失败',
                    'data': None
                }), 500
            
            mapping = manager.get_mapping_by_id(id)
            
            if not mapping:
                return jsonify({
                    'code': 404,
                    'message': f'未找到ID为 {id} 的房间映射',
                    'data': None
                }), 404
            
            # 清理结果，去除敏感信息
            clean_mapping = {
                'id': mapping['id'],
                'room_name': mapping['room_name'],
                'ip_address': mapping['ip_address'],
                'stream_url': mapping['stream_url'],
                'description': mapping['description'],
                'is_active': mapping['is_active'],
                'create_time': mapping['create_time'].isoformat() if hasattr(mapping['create_time'], 'isoformat') else mapping['create_time'],
                'update_time': mapping['update_time'].isoformat() if hasattr(mapping['update_time'], 'isoformat') else mapping['update_time']
            }
            
            # 返回结果
            return jsonify({
                'code': 200,
                'message': '成功获取房间映射详情',
                'data': clean_mapping
            })
        except Exception as e:
            logger.error(f"获取房间映射详情失败: {str(e)}")
            logger.error(traceback.format_exc())
            
            return jsonify({
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }), 500
        finally:
            if 'manager' in locals() and manager:
                manager.close_db()
    
    @room_mapping_bp.route('/query', methods=['GET'])
    def query_mapping():
        """根据条件查询房间映射"""
        try:
            manager = RoomMappingManager()
            if not manager.connect_db():
                return jsonify({
                    'code': 500,
                    'message': '连接数据库失败',
                    'data': None
                }), 500
            
            # 获取查询参数
            room_name = request.args.get('room_name')
            ip_address = request.args.get('ip_address')
            stream_url = request.args.get('stream_url')
            
            if room_name:
                mapping = manager.get_room_by_name(room_name)
            elif ip_address:
                mapping = manager.get_room_by_ip(ip_address)
            elif stream_url:
                mapping = manager.get_room_by_stream_url(stream_url)
            else:
                return jsonify({
                    'code': 400,
                    'message': '请至少提供一个查询参数: room_name, ip_address, stream_url',
                    'data': None
                }), 400
            
            if not mapping:
                return jsonify({
                    'code': 404,
                    'message': '未找到匹配的房间映射',
                    'data': None
                }), 404
            
            # 清理结果，去除敏感信息
            clean_mapping = {
                'id': mapping.get('id', 0),
                'room_name': mapping['room_name'],
                'ip_address': mapping['ip_address'],
                'stream_url': mapping['stream_url'],
                'description': mapping.get('description', ''),
                'is_active': mapping.get('is_active', 1)
            }
            
            if 'create_time' in mapping:
                clean_mapping['create_time'] = mapping['create_time'].isoformat() if hasattr(mapping['create_time'], 'isoformat') else mapping['create_time']
            
            if 'update_time' in mapping:
                clean_mapping['update_time'] = mapping['update_time'].isoformat() if hasattr(mapping['update_time'], 'isoformat') else mapping['update_time']
            
            # 返回结果
            return jsonify({
                'code': 200,
                'message': '成功查询房间映射',
                'data': clean_mapping
            })
        except Exception as e:
            logger.error(f"查询房间映射失败: {str(e)}")
            logger.error(traceback.format_exc())
            
            return jsonify({
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }), 500
        finally:
            if 'manager' in locals() and manager:
                manager.close_db()
    
    @room_mapping_bp.route('/add', methods=['POST'])
    def add_mapping():
        """添加房间映射"""
        try:
            manager = RoomMappingManager()
            if not manager.connect_db():
                return jsonify({
                    'code': 500,
                    'message': '连接数据库失败',
                    'data': None
                }), 500
            
            # 获取请求数据
            data = request.get_json()
            
            # 校验必要字段
            if not data or not isinstance(data, dict):
                return jsonify({
                    'code': 400,
                    'message': '请求数据格式错误',
                    'data': None
                }), 400
            
            for field in ['room_name', 'ip_address', 'stream_url']:
                if field not in data or not data[field]:
                    return jsonify({
                        'code': 400,
                        'message': f'缺少必要字段: {field}',
                        'data': None
                    }), 400
            
            # 添加映射
            success = manager.add_mapping(
                data['room_name'], 
                data['ip_address'], 
                data['stream_url'], 
                data.get('description')
            )
            
            if not success:
                return jsonify({
                    'code': 500,
                    'message': '添加房间映射失败',
                    'data': None
                }), 500
            
            # 返回结果
            return jsonify({
                'code': 200,
                'message': '成功添加房间映射',
                'data': {
                    'room_name': data['room_name'],
                    'ip_address': data['ip_address'],
                    'stream_url': data['stream_url'],
                    'description': data.get('description', '')
                }
            })
        except Exception as e:
            logger.error(f"添加房间映射失败: {str(e)}")
            logger.error(traceback.format_exc())
            
            error_message = str(e)
            if "Duplicate entry" in error_message:
                if "uk_room_name" in error_message:
                    message = "诊室名称已存在"
                elif "uk_ip_address" in error_message:
                    message = "IP地址已存在"
                elif "uk_stream_url" in error_message:
                    message = "流媒体URL已存在"
                else:
                    message = "数据已存在"
                    
                return jsonify({
                    'code': 400,
                    'message': message,
                    'data': None
                }), 400
            
            return jsonify({
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }), 500
        finally:
            if 'manager' in locals() and manager:
                manager.close_db()
    
    @room_mapping_bp.route('/update/<int:id>', methods=['PUT'])
    def update_mapping(id):
        """更新房间映射"""
        try:
            manager = RoomMappingManager()
            if not manager.connect_db():
                return jsonify({
                    'code': 500,
                    'message': '连接数据库失败',
                    'data': None
                }), 500
            
            # 获取请求数据
            data = request.get_json()
            
            # 校验数据
            if not data or not isinstance(data, dict):
                return jsonify({
                    'code': 400,
                    'message': '请求数据格式错误',
                    'data': None
                }), 400
            
            # 更新映射
            success = manager.update_mapping(
                id,
                data.get('room_name'),
                data.get('ip_address'),
                data.get('stream_url'),
                data.get('description'),
                data.get('is_active')
            )
            
            if not success:
                return jsonify({
                    'code': 404,
                    'message': f'未找到ID为 {id} 的房间映射，或更新时发生错误',
                    'data': None
                }), 404
            
            # 获取更新后的映射
            updated_mapping = manager.get_mapping_by_id(id)
            
            # 清理结果，去除敏感信息
            clean_mapping = {
                'id': updated_mapping['id'],
                'room_name': updated_mapping['room_name'],
                'ip_address': updated_mapping['ip_address'],
                'stream_url': updated_mapping['stream_url'],
                'description': updated_mapping['description'],
                'is_active': updated_mapping['is_active'],
                'create_time': updated_mapping['create_time'].isoformat() if hasattr(updated_mapping['create_time'], 'isoformat') else updated_mapping['create_time'],
                'update_time': updated_mapping['update_time'].isoformat() if hasattr(updated_mapping['update_time'], 'isoformat') else updated_mapping['update_time']
            }
            
            # 返回结果
            return jsonify({
                'code': 200,
                'message': '成功更新房间映射',
                'data': clean_mapping
            })
        except Exception as e:
            logger.error(f"更新房间映射失败: {str(e)}")
            logger.error(traceback.format_exc())
            
            error_message = str(e)
            if "Duplicate entry" in error_message:
                if "uk_room_name" in error_message:
                    message = "诊室名称已存在"
                elif "uk_ip_address" in error_message:
                    message = "IP地址已存在"
                elif "uk_stream_url" in error_message:
                    message = "流媒体URL已存在"
                else:
                    message = "数据已存在"
                    
                return jsonify({
                    'code': 400,
                    'message': message,
                    'data': None
                }), 400
            
            return jsonify({
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }), 500
        finally:
            if 'manager' in locals() and manager:
                manager.close_db()
    
    @room_mapping_bp.route('/delete/<int:id>', methods=['DELETE'])
    def delete_mapping(id):
        """删除房间映射（逻辑删除）"""
        try:
            manager = RoomMappingManager()
            if not manager.connect_db():
                return jsonify({
                    'code': 500,
                    'message': '连接数据库失败',
                    'data': None
                }), 500
            
            # 获取映射详情
            mapping = manager.get_mapping_by_id(id)
            if not mapping:
                return jsonify({
                    'code': 404,
                    'message': f'未找到ID为 {id} 的房间映射',
                    'data': None
                }), 404
            
            # 删除映射
            success = manager.delete_mapping(id)
            
            if not success:
                return jsonify({
                    'code': 500,
                    'message': '删除房间映射失败',
                    'data': None
                }), 500
            
            # 返回结果
            return jsonify({
                'code': 200,
                'message': '成功删除房间映射',
                'data': {
                    'id': id
                }
            })
        except Exception as e:
            logger.error(f"删除房间映射失败: {str(e)}")
            logger.error(traceback.format_exc())
            
            return jsonify({
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }), 500
        finally:
            if 'manager' in locals() and manager:
                manager.close_db()
    
    @room_mapping_bp.route('/export', methods=['GET'])
    def export_mappings():
        """导出房间映射为JSON"""
        try:
            # 创建临时文件
            import tempfile
            import os
            
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
            temp_file.close()
            
            # 导出映射
            manager = RoomMappingManager()
            if not manager.connect_db():
                return jsonify({
                    'code': 500,
                    'message': '连接数据库失败',
                    'data': None
                }), 500
            
            success = manager.export_to_json(temp_file.name)
            
            if not success:
                return jsonify({
                    'code': 500,
                    'message': '导出房间映射失败',
                    'data': None
                }), 500
            
            # 读取导出的文件
            with open(temp_file.name, 'r', encoding='utf-8') as f:
                exported_data = json.load(f)
            
            # 删除临时文件
            os.unlink(temp_file.name)
            
            # 返回结果
            return jsonify({
                'code': 200,
                'message': '成功导出房间映射',
                'data': exported_data
            })
        except Exception as e:
            logger.error(f"导出房间映射失败: {str(e)}")
            logger.error(traceback.format_exc())
            
            # 清理临时文件
            if 'temp_file' in locals() and os.path.exists(temp_file.name):
                os.unlink(temp_file.name)
            
            return jsonify({
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'data': None
            }), 500
        finally:
            if 'manager' in locals() and manager:
                manager.close_db()


def register_api(app=None):
    """
    注册房间映射API蓝图
    
    Args:
        app: Flask应用实例
    """
    if not flask_available or not app:
        return False
    
    try:
        app.register_blueprint(room_mapping_bp)
        logger.info("已注册房间映射API蓝图")
        return True
    except Exception as e:
        logger.error(f"注册房间映射API蓝图失败: {str(e)}")
        return False 