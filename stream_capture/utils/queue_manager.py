#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
队列管理工具
-----------
此模块提供用于管理视频流数据队列的工具类。
"""

import logging
import threading
from collections import deque, defaultdict

class QueueManager:
    """
    队列管理器
    
    用于管理视频流数据队列，支持多路视频流并发处理。
    """
    
    def __init__(self, max_size=100):
        """
        初始化队列管理器
        
        Args:
            max_size: 每个队列的最大容量
        """
        self.logger = logging.getLogger('queue_manager')
        self.max_size = max_size
        self.queues = defaultdict(lambda: deque(maxlen=max_size))
        self.locks = defaultdict(threading.Lock)
        self.active_rooms = set()
        
    def add_room(self, room_name):
        """
        添加诊室队列
        
        Args:
            room_name: 诊室名称
        """
        if room_name not in self.active_rooms:
            self.logger.info(f"添加诊室队列: {room_name}")
            self.active_rooms.add(room_name)
            
    def remove_room(self, room_name):
        """
        移除诊室队列
        
        Args:
            room_name: 诊室名称
        """
        if room_name in self.active_rooms:
            self.logger.info(f"移除诊室队列: {room_name}")
            self.active_rooms.remove(room_name)
            with self.locks[room_name]:
                self.queues[room_name].clear()
    
    def put(self, room_name, frame):
        """
        将数据放入队列
        
        Args:
            room_name: 诊室名称
            frame: 视频帧数据
        """
        if room_name in self.active_rooms:
            with self.locks[room_name]:
                self.queues[room_name].append(frame)
                
                # 记录队列状态
                queue_size = len(self.queues[room_name])
                if queue_size >= self.max_size * 0.9:
                    self.logger.warning(f"队列接近容量上限: {room_name}, 当前大小: {queue_size}")
    
    def get(self, room_name):
        """
        从队列获取数据
        
        Args:
            room_name: 诊室名称
            
        Returns:
            视频帧数据，如果队列为空则返回None
        """
        if room_name in self.active_rooms:
            with self.locks[room_name]:
                if not self.queues[room_name]:
                    return None
                return self.queues[room_name].popleft()
        return None
    
    def peek(self, room_name):
        """
        查看队列中的下一个数据，但不移除
        
        Args:
            room_name: 诊室名称
            
        Returns:
            视频帧数据，如果队列为空则返回None
        """
        if room_name in self.active_rooms:
            with self.locks[room_name]:
                if not self.queues[room_name]:
                    return None
                return self.queues[room_name][0]
        return None
    
    def is_empty(self, room_name):
        """
        检查队列是否为空
        
        Args:
            room_name: 诊室名称
            
        Returns:
            bool: 队列是否为空
        """
        if room_name in self.active_rooms:
            with self.locks[room_name]:
                return len(self.queues[room_name]) == 0
        return True
    
    def size(self, room_name):
        """
        获取队列大小
        
        Args:
            room_name: 诊室名称
            
        Returns:
            int: 队列中的项目数
        """
        if room_name in self.active_rooms:
            with self.locks[room_name]:
                return len(self.queues[room_name])
        return 0
    
    def clear(self, room_name):
        """
        清空队列
        
        Args:
            room_name: 诊室名称
        """
        if room_name in self.active_rooms:
            with self.locks[room_name]:
                self.queues[room_name].clear()
                self.logger.info(f"已清空队列: {room_name}")
    
    def get_all_rooms(self):
        """
        获取所有活动诊室
        
        Returns:
            list: 活动诊室列表
        """
        return list(self.active_rooms)
    
    def get_status(self):
        """
        获取所有队列状态
        
        Returns:
            dict: 队列状态字典
        """
        status = {}
        for room_name in self.active_rooms:
            with self.locks[room_name]:
                status[room_name] = {
                    'size': len(self.queues[room_name]),
                    'capacity': self.max_size,
                    'usage_percent': len(self.queues[room_name]) / self.max_size * 100
                }
        return status
