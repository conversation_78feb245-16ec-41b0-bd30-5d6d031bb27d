#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
并发音频处理功能测试脚本
演示如何使用修改后的app_api.py中的并发功能
"""

import os
import sys
import time
from typing import List, Dict

# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app_api import (
    enable_concurrent_processing,
    process_multiple_audios_concurrent,
    process_multiple_recordings_concurrent,
    process_audio,
    logger
)

def test_concurrent_processing():
    """测试并发处理功能"""
    
    # 1. 启用并发模式
    logger.info("=== 测试1: 启用并发模式 ===")
    enable_concurrent_processing(pool_size=2)
    logger.info("并发模式已启用")
    
    # 2. 模拟多个音频任务（这里使用示例路径，实际使用时需要真实文件）
    logger.info("\n=== 测试2: 并发处理多个音频文件 ===")
    audio_tasks = [
        {
            'audio_path': '/path/to/audio1.wav',  # 替换为真实音频路径
            'room_name': '诊室1',
            'task_id': 'task1',
            'split_chars': 10
        },
        {
            'audio_path': '/path/to/audio2.wav',  # 替换为真实音频路径
            'room_name': '诊室2', 
            'task_id': 'task2',
            'split_chars': 10
        },
        {
            'audio_path': '/path/to/audio3.wav',  # 替换为真实音频路径
            'room_name': '诊室3',
            'task_id': 'task3', 
            'split_chars': 10
        }
    ]
    
    # 检查文件是否存在
    existing_tasks = []
    for task in audio_tasks:
        if os.path.exists(task['audio_path']):
            existing_tasks.append(task)
        else:
            logger.warning(f"音频文件不存在，跳过: {task['audio_path']}")
    
    if existing_tasks:
        # 记录开始时间
        start_time = time.time()
        
        # 并发处理
        results = process_multiple_audios_concurrent(existing_tasks, max_workers=3)
        
        # 记录结束时间
        end_time = time.time()
        
        logger.info(f"并发处理完成，耗时: {end_time - start_time:.2f}秒")
        logger.info(f"处理结果: {results}")
        
        # 分析结果
        successful_tasks = sum(results.values())
        total_tasks = len(results)
        logger.info(f"成功处理: {successful_tasks}/{total_tasks} 个任务")
    else:
        logger.warning("没有找到有效的音频文件进行测试")
    
    # 3. 测试录制文件并发处理
    logger.info("\n=== 测试3: 并发处理录制文件 ===")
    recording_tasks = [
        {
            'recording_path': '/path/to/recording1.mp4',  # 替换为真实录制文件路径
            'room_name': '诊室A'
        },
        {
            'recording_path': '/path/to/recording2.mp4',  # 替换为真实录制文件路径
            'room_name': '诊室B'
        }
    ]
    
    # 检查录制文件是否存在
    existing_recordings = []
    for task in recording_tasks:
        if os.path.exists(task['recording_path']):
            existing_recordings.append(task)
        else:
            logger.warning(f"录制文件不存在，跳过: {task['recording_path']}")
    
    if existing_recordings:
        start_time = time.time()
        results = process_multiple_recordings_concurrent(existing_recordings)
        end_time = time.time()
        
        logger.info(f"录制文件并发处理完成，耗时: {end_time - start_time:.2f}秒")
        logger.info(f"处理结果: {results}")
    else:
        logger.warning("没有找到有效的录制文件进行测试")

def test_single_vs_concurrent():
    """比较单线程和并发处理的性能差异"""
    
    # 准备测试数据（需要真实文件）
    test_file = '/path/to/test_audio.wav'  # 替换为真实音频文件路径
    
    if not os.path.exists(test_file):
        logger.warning(f"测试文件不存在: {test_file}")
        logger.info("请修改test_file变量为真实的音频文件路径")
        return
    
    logger.info("\n=== 性能对比测试 ===")
    
    # 1. 单线程处理
    logger.info("1. 单线程处理...")
    start_time = time.time()
    
    # 处理3次相同文件（模拟多个文件）
    for i in range(3):
        result = process_audio(test_file, f'诊室{i+1}', f'task_{i+1}')
        logger.info(f"单线程任务{i+1}完成: {result}")
    
    single_thread_time = time.time() - start_time
    logger.info(f"单线程总耗时: {single_thread_time:.2f}秒")
    
    # 2. 并发处理
    logger.info("\n2. 并发处理...")
    enable_concurrent_processing(pool_size=2)
    
    start_time = time.time()
    
    tasks = [
        {'audio_path': test_file, 'room_name': f'诊室{i+1}', 'task_id': f'concurrent_task_{i+1}'}
        for i in range(3)
    ]
    
    results = process_multiple_audios_concurrent(tasks, max_workers=3)
    concurrent_time = time.time() - start_time
    
    logger.info(f"并发总耗时: {concurrent_time:.2f}秒")
    logger.info(f"性能提升: {((single_thread_time - concurrent_time) / single_thread_time * 100):.1f}%")
    logger.info(f"并发处理结果: {results}")

def main():
    """主函数"""
    logger.info("开始测试并发音频处理功能")
    
    try:
        # 基本并发功能测试
        test_concurrent_processing()
        
        # 性能对比测试（可选，需要真实音频文件）
        # test_single_vs_concurrent()
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    logger.info("测试完成")

if __name__ == "__main__":
    main() 