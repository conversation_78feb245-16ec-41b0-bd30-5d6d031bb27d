import os
import argparse
import threading
import queue
import concurrent.futures
from datetime import timedelta, datetime
from pydub import AudioSegment
import ffmpeg
import logging
from funasr import AutoModel
from typing import List, Dict, Optional
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建队列，用于线程间通信
spk_txt_queue = queue.Queue()
result_queue = queue.Queue()
audio_concat_queue = queue.Queue()

# 支持的音视频格式
SUPPORT_AUDIO_FORMAT = ['.mp3', '.m4a', '.aac', '.ogg', '.wav', '.flac', '.wma', '.aif']
SUPPORT_VIDEO_FORMAT = ['.mp4', '.avi', '.mov', '.mkv']

# 配置ASR模型路径
home_directory = os.path.expanduser("~")
asr_model_path = os.path.join(home_directory, ".cache", "modelscope", "hub", "models", "iic", "speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch")
asr_model_revision = "v2.0.4"
vad_model_path = os.path.join(home_directory, ".cache", "modelscope", "hub", "models", "iic", "speech_fsmn_vad_zh-cn-16k-common-pytorch")
vad_model_revision = "v2.0.4"
punc_model_path = os.path.join(home_directory, ".cache", "modelscope", "hub", "models", "iic", "punc_ct-transformer_zh-cn-common-vocab272727-pytorch")
punc_model_revision = "v2.0.4"
spk_model_path = os.path.join(home_directory, ".cache", "modelscope", "hub", "models", "iic", "speech_campplus_sv_zh-cn_16k-common")
spk_model_revision = "v2.0.4"
ngpu = 1
device = "cuda"
ncpu = 4

# 全局ASR模型实例（向后兼容）
asr_model = None

class ASRModelPool:
    """ASR模型池，支持并发处理"""
    
    def __init__(self, pool_size: int = 2):
        """
        初始化模型池
        
        Args:
            pool_size: 模型池大小，根据GPU内存调整
        """
        self.pool_size = pool_size
        self.models = queue.Queue(maxsize=pool_size)
        self.lock = threading.Lock()
        self._initialized = False
        
    def _create_model(self):
        """创建ASR模型实例"""
        try:
            model = AutoModel(
                model=asr_model_path,
                model_revision=asr_model_revision,
                vad_model=vad_model_path,
                vad_model_revision=vad_model_revision,
                punc_model=punc_model_path,
                punc_model_revision=punc_model_revision,
                spk_model=spk_model_path,
                spk_model_revision=spk_model_revision,
                ngpu=ngpu,
                ncpu=ncpu,
                device=device,
                disable_pbar=True,
                disable_log=True,
                disable_update=True
            )
            logger.info("ASR模型实例创建成功")
            return model
        except Exception as e:
            logger.error(f"创建ASR模型实例失败: {str(e)}")
            raise e
    
    def initialize(self):
        """初始化模型池"""
        with self.lock:
            if self._initialized:
                return
                
            logger.info(f"初始化ASR模型池，大小: {self.pool_size}")
            for i in range(self.pool_size):
                model = self._create_model()
                self.models.put(model)
                logger.info(f"模型 {i+1}/{self.pool_size} 加载完成")
            
            self._initialized = True
            logger.info("ASR模型池初始化完成")
    
    def get_model(self, timeout: int = 30):
        """
        从模型池获取模型实例
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            AutoModel: ASR模型实例
        """
        if not self._initialized:
            self.initialize()
            
        try:
            model = self.models.get(timeout=timeout)
            logger.debug("成功获取模型实例")
            return model
        except queue.Empty:
            logger.error(f"获取模型超时（{timeout}秒）")
            raise TimeoutError("获取ASR模型超时")
    
    def return_model(self, model):
        """将模型实例归还到模型池"""
        try:
            self.models.put(model, timeout=5)
            logger.debug("成功归还模型实例")
        except queue.Full:
            logger.warning("模型池已满，无法归还模型")

# 全局模型池实例
_model_pool = None
_concurrent_enabled = False

# 默认并发配置
DEFAULT_POOL_SIZE = 3
DEFAULT_MAX_WORKERS = 4



def get_default_max_workers():
    """获取默认的最大工作线程数"""
    return int(os.environ.get('AUDIO_SEPARATION_MAX_WORKERS', DEFAULT_MAX_WORKERS))

def enable_concurrent_processing(pool_size: int = 3):
    """
    启用并发处理模式
    
    Args:
        pool_size: 模型池大小，默认为3
    """
    global _model_pool, _concurrent_enabled
    if _model_pool is None:
        _model_pool = ASRModelPool(pool_size)
        _model_pool.initialize()
    _concurrent_enabled = True
    logger.info(f"并发处理模式已启用，模型池大小: {pool_size}")

def get_asr_model():
    """
    获取ASR模型实例，支持单例和模型池两种模式
    如果启用了并发模式，从模型池获取；否则使用单例模式
    
    Returns:
        AutoModel: 已加载的ASR模型实例
    """
    global asr_model, _model_pool, _concurrent_enabled
    
    # 如果启用了并发模式，从模型池获取
    if _concurrent_enabled and _model_pool is not None:
        return _model_pool.get_model()
    
    # 否则使用原来的单例模式（向后兼容）
    if asr_model is None:
        logger.info("首次加载ASR模型...")
        try:
            asr_model = AutoModel(
                model=asr_model_path,
                model_revision=asr_model_revision,
                vad_model=vad_model_path,
                vad_model_revision=vad_model_revision,
                punc_model=punc_model_path,
                punc_model_revision=punc_model_revision,
                spk_model=spk_model_path,
                spk_model_revision=spk_model_revision,
                ngpu=ngpu,
                ncpu=ncpu,
                device=device,
                disable_pbar=True,
                disable_log=True,
                disable_update=True
            )
            logger.info("ASR模型加载成功")
        except Exception as e:
            logger.error(f"初始化ASR模型失败: {str(e)}")
            raise e
    
    return asr_model

def return_asr_model(model):
    """
    归还ASR模型实例（仅在并发模式下有效）
    
    Args:
        model: 要归还的模型实例
    """
    global _model_pool, _concurrent_enabled
    
    if _concurrent_enabled and _model_pool is not None:
        _model_pool.return_model(model)

def to_date(milliseconds):
    """将时间戳转换为SRT格式的时间"""
    time_obj = timedelta(milliseconds=milliseconds)
    return f"{time_obj.seconds // 3600:02d}:{(time_obj.seconds // 60) % 60:02d}:{time_obj.seconds % 60:02d}.{time_obj.microseconds // 1000:03d}"

def to_milliseconds(time_str):
    """将SRT格式的时间转换为毫秒"""
    time_obj = datetime.strptime(time_str, "%H:%M:%S.%f")
    time_delta = time_obj - datetime(1900, 1, 1)
    milliseconds = int(time_delta.total_seconds() * 1000)
    return milliseconds

def write_txt():
    """将说话人的文本写入文件"""
    while not spk_txt_queue.empty():
        item = spk_txt_queue.get()
        spk_txt_file = item['spk_txt_file']
        spk_txt = item['spk_txt']
        spk_start = item['start']
        spk_end = item['end']
        dir_path = os.path.dirname(spk_txt_file)
        os.makedirs(dir_path, exist_ok=True)
        with open(spk_txt_file, 'a', encoding='utf-8') as f:
            f.write(f"{spk_start} --> {spk_end}\n{spk_txt}\n\n")
        spk_txt_queue.task_done()

def audio_concat_worker():
    """合并每个说话人的音频片段"""
    while not audio_concat_queue.empty():
        speaker_audios_tmp = audio_concat_queue.get()
        for spk, audio_segments in speaker_audios_tmp.items():
            if not audio_segments:
                continue
                
            # 合并每个说话人的音频片段
            audio_info = audio_segments[0]
            audio_name = audio_info['audio_name']
            room_name = audio_info.get('room_name', 'unknown')
            
            # 更正输出路径：到项目根目录的SeparatedAudioText目录
            # 获取当前文件所在目录（backend）的上一级目录作为项目根目录
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            output_dir = os.path.join(project_root, 'SeparatedAudioText', room_name, audio_name)
            os.makedirs(output_dir, exist_ok=True)
            
            output_file = os.path.join(output_dir, f"speaker_{spk}.mp3")
            inputs = [seg['file'] for seg in audio_segments]
            
            try:
                if len(inputs) > 0:
                    concat_audio = AudioSegment.from_file(inputs[0])
                    for i in range(1, len(inputs)):
                        concat_audio = concat_audio + AudioSegment.from_file(inputs[i])
                    concat_audio.export(output_file, format="mp3")
                    logger.info(f"已将说话人 {spk} 的音频合并到 {output_file}")
            except Exception as e:
                logger.error(f"合并音频失败: {str(e)}")
        
        audio_concat_queue.task_done()

def process_audio(audio_path, room_name, task_id=None, split_chars=10):
    """处理音频文件，提取说话人并分割音频
    
    Args:
        audio_path: 音频文件路径
        room_name: 诊室名称，用于保存结果
        task_id: 可选，任务ID，默认使用音频文件名
        split_chars: 每个片段的最大字符数
        
    Returns:
        bool: 处理是否成功
    """
    # 尝试自动启用并发模式（仅在首次调用时）
    _maybe_auto_enable_concurrent()
    
    if not os.path.exists(audio_path):
        logger.error(f"音频文件不存在: {audio_path}")
        return False
    
    # 获取模型实例，支持并发和单例模式
    model = None
    try:
        model = get_asr_model()
    except Exception as e:
        logger.error(f"获取ASR模型失败: {str(e)}")
        return False
    
    # 获取音频名称（任务ID）
    if task_id is None:
        task_id = os.path.splitext(os.path.basename(audio_path))[0]
    audio_name = task_id
    _, audio_extension = os.path.splitext(audio_path)
    
    logger.info(f"开始处理音频: {audio_path}, 任务ID: {task_id}, 诊室: {room_name}")
    
    # 存储每个说话人的音频片段
    speaker_audios = {}
    
    try:
        # 音频预处理
        audio_bytes, _ = (
            ffmpeg.input(audio_path, threads=0, hwaccel='cuda')
            .output("-", format="wav", acodec="pcm_s16le", ac=1, ar=16000)
            .run(cmd=["ffmpeg", "-nostdin"], capture_stdout=True, capture_stderr=True)
        )
        
        # 使用ASR模型进行识别
        res = model.generate(input=audio_bytes, batch_size_s=300, is_final=True, sentence_timestamp=True)
        rec_result = res[0]
        asr_result_text = rec_result['text']
        
        if asr_result_text == '':
            logger.warning("没有识别到任何文本")
            return False
        
        # 获取项目根目录
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # 保存完整的识别结果
        output_dir = os.path.join(project_root, 'SeparatedAudioText', room_name, audio_name)
        os.makedirs(output_dir, exist_ok=True)
        
        with open(os.path.join(output_dir, "full_transcript.txt"), "w", encoding="utf-8") as f:
            f.write(asr_result_text)
        
        # 检查句子信息是否存在
        if 'sentence_info' not in rec_result or not rec_result['sentence_info']:
            logger.warning("没有识别到有效的句子信息")
            # 创建一个空的sentences.json文件以表示处理已完成
            import json
            with open(os.path.join(output_dir, "sentences.json"), "w", encoding="utf-8") as f:
                json.dump([], f, ensure_ascii=False, indent=2)
            return True
        
        # 处理每个句子
        sentences = []
        for sentence in rec_result["sentence_info"]:
            start = to_date(sentence["start"])
            end = to_date(sentence["end"])
            if sentences and sentence["spk"] == sentences[-1]["spk"] and len(sentences[-1]["text"]) < int(split_chars):
                sentences[-1]["text"] += "" + sentence["text"]
                sentences[-1]["end"] = end
            else:
                sentences.append(
                    {"text": sentence["text"], "start": start, "end": end, "spk": sentence["spk"]}
                )
        
        # 剪切音频或视频片段
        i = 0
        for stn in sentences:
            stn_txt = stn['text']
            start = stn['start']
            end = stn['end']
            spk = stn['spk']
            
            # 创建保存目录
            spk_dir = os.path.join(output_dir, f"speaker_{spk}")
            os.makedirs(spk_dir, exist_ok=True)
            
            # 保存文本文件
            spk_txt_file = os.path.join(output_dir, f"speaker_{spk}.txt")
            spk_txt_queue.put({
                'spk_txt_file': spk_txt_file, 
                'spk_txt': stn_txt, 
                'start': start, 
                'end': end
            })
            
            # 剪切音频或视频片段
            file_ext = os.path.splitext(audio_path)[-1].lower()
            final_save_file = os.path.join(spk_dir, f"segment_{i}{file_ext}")
            
            try:
                if file_ext in SUPPORT_AUDIO_FORMAT:
                    # 音频切分
                    (
                        ffmpeg.input(audio_path, threads=0, ss=start, to=end)
                        .output(final_save_file)
                        .run(cmd=["ffmpeg", "-nostdin"], 
                             overwrite_output=True, 
                             capture_stdout=True,
                             capture_stderr=True)
                    )
                elif file_ext in SUPPORT_VIDEO_FORMAT:
                    final_save_file = os.path.join(spk_dir, f"segment_{i}.mp4")
                    # 移除对libx264编码器的依赖，直接使用copy复制原始流
                    (
                        ffmpeg.input(audio_path, threads=0, ss=start, to=end)
                        .output(final_save_file, c="copy")
                        .run(cmd=["ffmpeg", "-nostdin"], 
                             overwrite_output=True, 
                             capture_stdout=True,
                             capture_stderr=True)
                    )
                else:
                    logger.warning(f"不支持的文件格式: {file_ext}")
                    continue
                
                # 记录说话人和对应的音频片段，用于合并
                if spk not in speaker_audios:
                    speaker_audios[spk] = []
                
                speaker_audios[spk].append({
                    'file': final_save_file, 
                    'audio_name': audio_name,
                    'room_name': room_name
                })
                
                i += 1
                logger.info(f"成功剪切音频段: {start} - {end}, 保存到 {final_save_file}")
                
            except ffmpeg.Error as e:
                error_message = e.stderr.decode() if hasattr(e, 'stderr') else str(e)
                logger.error(f"剪切音频发生错误: {error_message}")
                # 尝试使用另一种方式切分视频
                try:
                    if file_ext in SUPPORT_VIDEO_FORMAT:
                        # 尝试先提取音频
                        final_save_file = os.path.join(spk_dir, f"segment_{i}.mp3")
                        (
                            ffmpeg.input(audio_path, threads=0, ss=start, to=end)
                            .output(final_save_file, format="mp3")
                            .run(cmd=["ffmpeg", "-nostdin"], 
                                overwrite_output=True, 
                                capture_stdout=True,
                                capture_stderr=True)
                        )
                        
                        # 记录说话人和对应的音频片段，用于合并
                        if spk not in speaker_audios:
                            speaker_audios[spk] = []
                        
                        speaker_audios[spk].append({
                            'file': final_save_file, 
                            'audio_name': audio_name,
                            'room_name': room_name
                        })
                        
                        i += 1
                        logger.info(f"成功提取音频段: {start} - {end}, 保存到 {final_save_file}")
                except ffmpeg.Error as e2:
                    error_message = e2.stderr.decode() if hasattr(e2, 'stderr') else str(e2)
                    logger.error(f"提取音频也失败: {error_message}")
        
        # 保存句子信息到JSON文件，仅保留speaker和text字段
        import json
        
        # 按照时间顺序创建新的简化句子列表
        simplified_sentences = []
        
        # 遍历原始句子，提取需要的字段并重命名
        for sentence in sentences:
            simplified_sentences.append({
                "speaker": sentence["spk"],  # 将spk重命名为speaker
                "text": sentence["text"]
            })
        
        # 导出为JSON文件
        with open(os.path.join(output_dir, "sentences.json"), "w", encoding="utf-8") as f:
            json.dump(simplified_sentences, f, ensure_ascii=False, indent=2)
        
        # 合并每个说话人的音频
        if speaker_audios:
            audio_concat_queue.put(speaker_audios)
        
        logger.info(f"音频 {audio_path} 处理完成")
        write_txt()  # 写入文本文件
        audio_concat_worker()  # 合并音频片段
        
        return True
        
    except Exception as e:
        logger.error(f"处理音频时发生错误: {str(e)}")
        
        # 尝试创建输出目录和空的sentences.json文件
        try:
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            output_dir = os.path.join(project_root, 'SeparatedAudioText', room_name, audio_name)
            os.makedirs(output_dir, exist_ok=True)
            
            # 创建空的sentences.json文件，表示处理已完成但没有内容
            import json
            with open(os.path.join(output_dir, "sentences.json"), "w", encoding="utf-8") as f:
                json.dump([], f, ensure_ascii=False, indent=2)
            
            logger.info(f"创建了空的sentences.json文件，表示音频 {audio_path} 中没有可识别的内容")
        except Exception as inner_e:
            logger.error(f"尝试创建空的sentences.json文件时发生错误: {str(inner_e)}")
        
        return False
    finally:
        # 归还模型到模型池（仅在并发模式下有效）
        if model is not None:
            return_asr_model(model)

# 新增：并发处理多个音频任务
def process_multiple_audios_concurrent(audio_tasks: List[Dict], max_workers: int = None) -> Dict[str, bool]:
    """
    并发处理多个音频文件
    
    Args:
        audio_tasks: 音频任务列表，每个任务包含 {'audio_path', 'room_name', 'task_id', 'split_chars'}
        max_workers: 最大工作线程数，默认为4（可通过环境变量配置）
        
    Returns:
        Dict[str, bool]: 任务ID到处理结果的映射
    """
    # 使用默认配置或环境变量配置
    if max_workers is None:
        max_workers = get_default_max_workers()
    
    logger.info(f"开始并发处理 {len(audio_tasks)} 个音频任务，最大工作线程数: {max_workers}")
    
    # 确保启用并发模式
    if not _concurrent_enabled:
        logger.warning("并发模式未启用，将自动启用并发模式")
        enable_concurrent_processing()
    
    results = {}
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_task = {}
        for task in audio_tasks:
            future = executor.submit(
                process_audio,
                task['audio_path'],
                task['room_name'],
                task.get('task_id'),
                task.get('split_chars', 10)
            )
            task_id = task.get('task_id') or os.path.splitext(os.path.basename(task['audio_path']))[0]
            future_to_task[future] = task_id
        
        # 等待所有任务完成
        for future in concurrent.futures.as_completed(future_to_task):
            task_id = future_to_task[future]
            try:
                result = future.result()
                results[task_id] = result
                if result:
                    logger.info(f"任务 {task_id} 处理成功")
                else:
                    logger.error(f"任务 {task_id} 处理失败")
            except Exception as e:
                logger.error(f"任务 {task_id} 执行异常: {str(e)}")
                results[task_id] = False
    
    logger.info(f"并发处理完成，成功: {sum(results.values())}/{len(results)}")
    return results

# 新增：集成到主程序的处理函数
def process_recording(recording_path, room_name):
    """
    处理录制的视频文件，集成到主程序调用
    
    Args:
        recording_path: 录制视频的完整路径
        room_name: 诊室名称
        
    Returns:
        bool: 处理是否成功，True表示成功，False表示失败
    """
    logger.info(f"开始处理录制视频: {recording_path}, 诊室: {room_name}")
    
    # 确保SeparatedAudioText目录存在
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    os.makedirs(os.path.join(project_root, "SeparatedAudioText"), exist_ok=True)
    
    # 处理音频文件
    result = process_audio(recording_path, room_name)
    if result:
        logger.info(f"视频 {recording_path} 处理成功")
        return True
    else:
        logger.error(f"视频 {recording_path} 处理失败")
        return False

# 新增：批量处理录制文件（支持并发）
def process_multiple_recordings_concurrent(recording_tasks: List[Dict]) -> Dict[str, bool]:
    """
    批量并发处理多个录制文件
    
    Args:
        recording_tasks: 录制任务列表，每个任务包含 {'recording_path', 'room_name'}
        
    Returns:
        Dict[str, bool]: 处理结果映射
    """
    # 转换为音频任务格式
    audio_tasks = []
    for task in recording_tasks:
        audio_task = {
            'audio_path': task['recording_path'],
            'room_name': task['room_name'],
            'task_id': os.path.splitext(os.path.basename(task['recording_path']))[0]
        }
        audio_tasks.append(audio_task)
    
    return process_multiple_audios_concurrent(audio_tasks)

def main():
    """
    命令行入口点
    示例用法: 
      python app_api.py --input 路径名称 --room_name 诊室名称 --task_id 任务ID
      python app_api.py --input 路径名称 --room_name 诊室名称 --concurrent --pool_size 3
    """
    parser = argparse.ArgumentParser(description='医患对话识别系统 - 命令行工具')
    parser.add_argument('--input', required=True, help='输入音频/视频文件路径')
    parser.add_argument('--room_name', default='未知诊室', help='诊室名称')
    parser.add_argument('--task_id', help='任务ID，默认使用文件名')
    parser.add_argument('--split_chars', type=int, default=10, help='每个片段的最大字符数')
    parser.add_argument('--concurrent', action='store_true', help='启用并发处理模式')
    parser.add_argument('--pool_size', type=int, default=DEFAULT_POOL_SIZE, help='模型池大小（仅在并发模式下有效）')
    parser.add_argument('--max_workers', type=int, default=DEFAULT_MAX_WORKERS, help='最大工作线程数（仅在并发模式下有效）')
    
    args = parser.parse_args()
    
    # 如果启用并发模式，初始化模型池
    if args.concurrent:
        logger.info("启用并发处理模式")
        enable_concurrent_processing(pool_size=args.pool_size)
    
    # 启动工作线程
    txt_thread = threading.Thread(target=write_txt)
    txt_thread.daemon = True
    txt_thread.start()
    
    audio_concat_thread = threading.Thread(target=audio_concat_worker)
    audio_concat_thread.daemon = True
    audio_concat_thread.start()
    
    # 处理音频
    success = process_audio(
        audio_path=args.input,
        room_name=args.room_name,
        task_id=args.task_id,
        split_chars=args.split_chars
    )
    
    if success:
        logger.info("处理完成")
        # 确保队列任务完成
        spk_txt_queue.join()
        audio_concat_queue.join()
        return 0
    else:
        logger.error("处理失败")
        return 1

# 模块导入时的初始化状态
_MODULE_AUTO_INIT_DONE = False
_MODULE_AUTO_INIT_LOCK = threading.Lock()

def _maybe_auto_enable_concurrent():
    """
    可能启用并发模式（仅在特定条件下）
    这个函数会在第一次调用相关处理函数时被调用，而不是在模块导入时
    """
    global _MODULE_AUTO_INIT_DONE, _concurrent_enabled
    
    with _MODULE_AUTO_INIT_LOCK:
        # 如果已经初始化过，或者已经手动启用了并发，就不再自动初始化
        if _MODULE_AUTO_INIT_DONE or _concurrent_enabled:
            return
        
        # 标记为已初始化，防止重复
        _MODULE_AUTO_INIT_DONE = True
    
    try:
        # 检查环境变量是否禁用自动并发
        concurrent_env = os.environ.get('AUDIO_SEPARATION_CONCURRENT', 'true').lower()
        if concurrent_env in ['false', '0', 'no', 'off']:
            logger.debug("通过环境变量禁用了自动并发模式")
            return
        
        # 从环境变量获取配置
        pool_size = int(os.environ.get('AUDIO_SEPARATION_POOL_SIZE', DEFAULT_POOL_SIZE))
        
        logger.info(f"自动启用并发处理模式，模型池大小: {pool_size}")
        enable_concurrent_processing(pool_size)
    except Exception as e:
        logger.warning(f"自动启用并发模式失败，将使用单例模式: {str(e)}")

# 添加模块入口点
if __name__ == "__main__":
    exit(main())

"""
并发功能使用示例：

=== 自动并发模式（推荐） ===
模块导入时会自动启用并发模式，使用默认配置：
- 模型池大小: 3
- 最大工作线程数: 4

可通过环境变量调整配置：
export AUDIO_SEPARATION_CONCURRENT="true"     # 启用并发模式
export AUDIO_SEPARATION_POOL_SIZE="3"         # 模型池大小
export AUDIO_SEPARATION_MAX_WORKERS="4"       # 最大工作线程数

=== 手动配置 ===
1. 命令行模式：
   python app_api.py --input audio.wav --room_name "诊室1" --concurrent --pool_size 3

2. 代码中使用：
   
   # 手动启用并发模式（可选，默认已启用）
   enable_concurrent_processing(pool_size=3)
   
   # 直接调用原有接口（自动支持并发）
   result = process_audio('audio.wav', '诊室1')
   
   # 并发处理多个音频文件
   tasks = [
       {'audio_path': 'audio1.wav', 'room_name': '诊室1'},
       {'audio_path': 'audio2.wav', 'room_name': '诊室2'},
       {'audio_path': 'audio3.wav', 'room_name': '诊室3'}
   ]
   
   results = process_multiple_audios_concurrent(tasks)  # 使用默认max_workers=4
   print(f"处理结果: {results}")
   
   # 或者处理录制文件
   recording_tasks = [
       {'recording_path': 'video1.mp4', 'room_name': '诊室1'},
       {'recording_path': 'video2.mp4', 'room_name': '诊室2'}
   ]
   
   results = process_multiple_recordings_concurrent(recording_tasks)
   print(f"录制处理结果: {results}")

=== 配置说明 ===
- 默认配置: pool_size=3, max_workers=4
- 模型池大小建议根据GPU内存调整，每个模型实例约占用2-4GB显存
- 最大工作线程数建议不超过CPU核心数的2倍
- 并发模式会自动管理模型的获取和归还，无需手动处理
- 原有的process_audio和process_recording函数保持向后兼容
- 如需禁用并发：export AUDIO_SEPARATION_CONCURRENT="false"
"""
