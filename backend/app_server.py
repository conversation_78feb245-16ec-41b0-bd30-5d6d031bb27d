import os
import threading
import queue
import uuid
import json
from datetime import timedelta, datetime
from pydub import AudioSegment
import ffmpeg
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
from werkzeug.utils import secure_filename
from funasr import AutoModel

# 创建Flask应用
app = Flask(__name__, static_folder='../web')
CORS(app)  # 启用跨域支持

# 创建队列
spk_txt_queue = queue.Queue()
result_queue = queue.Queue()
audio_concat_queue = queue.Queue()
processing_tasks = {}  # 用于存储任务状态

# 支持的音视频格式
support_audio_format = ['.mp3', '.m4a', '.aac', '.ogg', '.wav', '.flac', '.wma', '.aif']
support_video_format = ['.mp4', '.avi', '.mov', '.mkv']

# 设置上传和输出目录
UPLOAD_FOLDER = '../uploads'
OUTPUT_FOLDER = '../SeparatedAudioText'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# 模型路径设置
home_directory = os.path.expanduser("~")
asr_model_path = os.path.join(home_directory, ".cache", "modelscope", "hub", "models", "iic", "speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch")
asr_model_revision = "v2.0.4"
vad_model_path = os.path.join(home_directory, ".cache", "modelscope", "hub", "models", "iic", "speech_fsmn_vad_zh-cn-16k-common-pytorch")
vad_model_revision = "v2.0.4"
punc_model_path = os.path.join(home_directory, ".cache", "modelscope", "hub", "models", "iic", "punc_ct-transformer_zh-cn-common-vocab272727-pytorch")
punc_model_revision = "v2.0.4"
spk_model_path = os.path.join(home_directory, ".cache", "modelscope", "hub", "models", "iic", "speech_campplus_sv_zh-cn_16k-common")
spk_model_revision = "v2.0.4"
ngpu = 2
device = "cuda"
ncpu = 4

# 初始化ASR模型
print("正在加载模型...")
model = AutoModel(model=asr_model_path,
                  model_revision=asr_model_revision,
                  vad_model=vad_model_path,
                  vad_model_revision=vad_model_revision,
                  punc_model=punc_model_path,
                  punc_model_revision=punc_model_revision,
                  spk_model=spk_model_path,
                  spk_model_revision=spk_model_revision,
                  ngpu=ngpu,
                  ncpu=ncpu,
                  device=device,
                  gpu_list="0,1",
                  disable_pbar=True,
                  disable_log=True,
                  disable_update=True
                  )
print("模型加载完成")

def to_date(milliseconds):
    """将时间戳转换为SRT格式的时间"""
    time_obj = timedelta(milliseconds=milliseconds)
    return f"{time_obj.seconds // 3600:02d}:{(time_obj.seconds // 60) % 60:02d}:{time_obj.seconds % 60:02d}.{time_obj.microseconds // 1000:03d}"

def to_milliseconds(time_str):
    time_obj = datetime.strptime(time_str, "%H:%M:%S.%f")
    time_delta = time_obj - datetime(1900, 1, 1)
    milliseconds = int(time_delta.total_seconds() * 1000)
    return milliseconds

def write_txt_worker():
    while True:
        item = spk_txt_queue.get()
        spk_txt_file = item['spk_txt_file']
        spk_txt = item['spk_txt']
        spk_start = item['start']
        spk_end = item['end']
        dir_path = os.path.dirname(spk_txt_file)
        os.makedirs(dir_path, exist_ok=True)
        with open(spk_txt_file, 'a', encoding='utf-8') as f:
            f.write(f"{spk_start} --> {spk_end}\n{spk_txt}\n\n")
        spk_txt_queue.task_done()

def audio_concat_worker():
    while True:
        item = audio_concat_queue.get()
        task_id = item.get('task_id', 'unknown')
        segments = item.get('segments', [])
        output_file = item.get('output_file')
        speaker = item.get('speaker')
        
        try:
            if not segments:
                continue
                
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            # 合并音频片段
            inputs = [seg['file'] for seg in segments]
            if not inputs:
                continue
                
            concat_audio = AudioSegment.from_file(inputs[0])
            for i in range(1, len(inputs)):
                concat_audio = concat_audio + AudioSegment.from_file(inputs[i])
            concat_audio.export(output_file, format="mp3")
            print(f"已将 {speaker} 的音频合并到 {output_file}")
            
            # 更新任务状态
            if task_id in processing_tasks:
                processing_tasks[task_id]['merged_files'].append({
                    'speaker': speaker,
                    'file': output_file,
                    'relative_path': os.path.relpath(output_file, OUTPUT_FOLDER)
                })
        except Exception as e:
            print(f"合并音频时出错: {e}")
            if task_id in processing_tasks:
                processing_tasks[task_id]['error'] = f"合并音频时出错: {e}"
        finally:
            audio_concat_queue.task_done()

def process_audio(file_path, task_id, split_chars):
    try:
        # 获取原始文件名（不使用secure_filename处理）
        original_filename = os.path.basename(file_path)
        audio_name = os.path.splitext(original_filename)[0]
        
        # 更新任务状态
        processing_tasks[task_id]['status'] = 'processing'
        processing_tasks[task_id]['progress'] = 'preprocessing'
        
        # 音频预处理
        try:
            audio_bytes, _ = (
                ffmpeg.input(file_path, threads=0, hwaccel='cuda')
                .output("-", format="wav", acodec="pcm_s16le", ac=1, ar=16000)
                .run(cmd=["ffmpeg", "-nostdin"], capture_stdout=True, capture_stderr=True)
            )
        except ffmpeg.Error as e:
            raise Exception(f"音频预处理失败: {e.stderr.decode()}")
        
        # 更新进度
        processing_tasks[task_id]['progress'] = 'recognizing'
        
        # 语音识别
        res = model.generate(input=audio_bytes, batch_size_s=300, is_final=True, sentence_timestamp=True)
        rec_result = res[0]
        asr_result_text = rec_result['text']
        
        if not asr_result_text:
            raise Exception("没有识别到语音内容")
        
        # 更新进度
        processing_tasks[task_id]['progress'] = 'splitting'
        
        # 处理句子信息
        sentences = []
        for sentence in rec_result["sentence_info"]:
            start = to_date(sentence["start"])
            end = to_date(sentence["end"])
            if sentences and sentence["spk"] == sentences[-1]["spk"] and len(sentences[-1]["text"]) < int(split_chars):
                sentences[-1]["text"] += "" + sentence["text"]
                sentences[-1]["end"] = end
            else:
                sentences.append(
                    {"text": sentence["text"], "start": start, "end": end, "spk": sentence["spk"]}
                )
        
        # 创建说话人音频字典
        speaker_audios = {}
        
        # 获取所有唯一的说话人ID
        unique_speakers = set(stn['spk'] for stn in sentences)
        
        # 创建说话人ID到speaker名称的映射
        speaker_mapping = {spk: f"speaker{i+1}" for i, spk in enumerate(sorted(unique_speakers))}
        
        # 剪切音频或视频片段
        date = datetime.now().strftime("%Y-%m-%d")
        output_dir = os.path.join(OUTPUT_FOLDER, date, audio_name)
        os.makedirs(output_dir, exist_ok=True)
        
        for i, stn in enumerate(sentences):
            stn_txt = stn['text']
            start = stn['start']
            end = stn['end']
            spk = stn['spk']
            
            # 使用映射获取speaker名称
            speaker_name = speaker_mapping[spk]
            
            # 创建说话人目录
            spk_dir = os.path.join(output_dir, speaker_name)
            os.makedirs(spk_dir, exist_ok=True)
            
            # 获取文件扩展名
            _, file_ext = os.path.splitext(file_path)
            
            # 设置输出文件路径
            if file_ext.lower() in support_audio_format:
                segment_file = os.path.join(spk_dir, f"{i}{file_ext}")
                try:
                    (
                        ffmpeg.input(file_path, threads=0, ss=start, to=end, hwaccel='cuda')
                        .output(segment_file)
                        .run(cmd=["ffmpeg", "-nostdin"], overwrite_output=True, capture_stdout=True, capture_stderr=True)
                    )
                except ffmpeg.Error as e:
                    print(f"剪切音频失败: {e.stderr.decode()}")
                    continue
            elif file_ext.lower() in support_video_format:
                segment_file = os.path.join(spk_dir, f"{i}.mp4")
                try:
                    # 使用复制模式，不重新编码
                    (
                        ffmpeg.input(file_path, threads=0, ss=start, to=end)
                        .output(segment_file, c='copy')
                        .run(cmd=["ffmpeg", "-nostdin"], overwrite_output=True, capture_stdout=True, capture_stderr=True)
                    )
                except ffmpeg.Error as e:
                    print(f"剪切视频失败: {e.stderr.decode()}")
                    continue
            else:
                continue
            
            # 添加到分段列表
            relative_path = os.path.relpath(segment_file, OUTPUT_FOLDER)
            processing_tasks[task_id]['segments'].append({
                'speaker': speaker_name,  # 使用speaker名称
                'start': start,
                'end': end,
                'text': stn_txt,
                'file': segment_file,
                'relative_path': relative_path
            })
            
            # 写入文本文件
            spk_txt_file = os.path.join(output_dir, f'{speaker_name}.txt')  # 使用speaker名称
            spk_txt_queue.put({
                'spk_txt_file': spk_txt_file,
                'spk_txt': stn_txt,
                'start': start,
                'end': end
            })
            
            # 记录说话人和对应的音频片段
            if speaker_name not in speaker_audios:  # 使用speaker名称
                speaker_audios[speaker_name] = []
            speaker_audios[speaker_name].append({
                'file': segment_file,
                'audio_name': audio_name
            })
        
        # 更新进度
        processing_tasks[task_id]['progress'] = 'merging'
        
        # 合并每个说话人的音频片段
        for spk, audio_segments in speaker_audios.items():
            if not audio_segments:
                continue
                
            merged_file = os.path.join(output_dir, f"{spk}.mp3")  # 使用speaker名称
            audio_concat_queue.put({
                'segments': audio_segments,
                'output_file': merged_file,
                'task_id': task_id,
                'speaker': spk
            })
        
        # 更新任务状态
        processing_tasks[task_id]['status'] = 'completed'
        processing_tasks[task_id]['progress'] = None
        
    except Exception as e:
        print(f"处理音频失败: {str(e)}")
        processing_tasks[task_id]['status'] = 'error'
        processing_tasks[task_id]['error'] = str(e)

# 启动工作线程
txt_thread = threading.Thread(target=write_txt_worker)
txt_thread.daemon = True
txt_thread.start()

audio_concat_thread = threading.Thread(target=audio_concat_worker)
audio_concat_thread.daemon = True
audio_concat_thread.start()

# API路由
@app.route('/')
def index():
    return send_from_directory('../web', 'index.html')

@app.route('/<path:path>')
def static_files(path):
    return send_from_directory('../web', path)

@app.route('/api/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({'error': '没有文件'}), 400
        
    files = request.files.getlist('file')
    split_chars = request.form.get('split_chars', '10')
    
    task_id = str(uuid.uuid4())
    results = []
    
    processing_tasks[task_id] = {
        'id': task_id,
        'status': 'uploading',
        'files': [],
        'segments': [],
        'merged_files': [],
        'created_at': datetime.now().isoformat(),
        'split_chars': split_chars
    }
    
    for file in files:
        if file.filename == '':
            continue
            
        # 保留原始文件名，不使用secure_filename
        filename = file.filename
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(file_path)
        
        processing_tasks[task_id]['files'].append({
            'name': filename,
            'path': file_path
        })
        
        results.append({
            'filename': filename,
            'status': 'uploaded'
        })
    
    # 启动处理线程
    for file_info in processing_tasks[task_id]['files']:
        thread = threading.Thread(
            target=process_audio, 
            args=(file_info['path'], task_id, split_chars)
        )
        thread.daemon = True
        thread.start()
    
    return jsonify({
        'task_id': task_id,
        'files': results,
        'message': '文件上传成功，开始处理'
    })

@app.route('/api/tasks/<task_id>', methods=['GET'])
def get_task_status(task_id):
    if task_id not in processing_tasks:
        return jsonify({'error': '任务不存在'}), 404
        
    return jsonify(processing_tasks[task_id])

@app.route('/api/tasks', methods=['GET'])
def get_all_tasks():
    tasks = list(processing_tasks.values())
    tasks.sort(key=lambda x: x.get('created_at', ''), reverse=True)
    return jsonify(tasks)

@app.route('/api/download/<path:filename>', methods=['GET'])
def download_file(filename):
    return send_from_directory(OUTPUT_FOLDER, filename, as_attachment=True)

# 添加修改说话人名称的API
@app.route('/api/tasks/<task_id>/rename-speaker', methods=['POST'])
def rename_speaker(task_id):
    if task_id not in processing_tasks:
        return jsonify({'error': '任务不存在'}), 404
    
    data = request.json
    original_name = data.get('original_name')
    new_name = data.get('new_name')
    
    if not original_name or not new_name:
        return jsonify({'error': '参数不完整'}), 400
    
    task = processing_tasks[task_id]
    
    try:
        # 1. 更新内存中的任务数据
        # 更新分段中的说话人名称
        for segment in task['segments']:
            if segment['speaker'] == original_name:
                segment['speaker'] = new_name
        
        # 更新合并文件中的说话人名称
        for merged_file in task['merged_files']:
            if merged_file['speaker'] == original_name:
                merged_file['speaker'] = new_name
                
                # 2. 重命名实际文件
                old_file = merged_file['file']
                new_file = os.path.join(os.path.dirname(old_file), f"{new_name}.mp3")
                
                if os.path.exists(old_file):
                    os.rename(old_file, new_file)
                    merged_file['file'] = new_file
                    merged_file['relative_path'] = os.path.relpath(new_file, OUTPUT_FOLDER)
        
        # 3. 重命名文本文件
        for segment in task['segments']:
            if segment['speaker'] == new_name:
                output_dir = os.path.dirname(os.path.dirname(segment['file']))
                old_txt_file = os.path.join(output_dir, f"{original_name}.txt")
                new_txt_file = os.path.join(output_dir, f"{new_name}.txt")
                
                if os.path.exists(old_txt_file):
                    os.rename(old_txt_file, new_txt_file)
                    break
        
        # 4. 重命名文件夹
        for segment in task['segments']:
            if segment['speaker'] == new_name:
                old_dir = os.path.dirname(segment['file'])
                new_dir = os.path.join(os.path.dirname(old_dir), new_name)
                
                if os.path.exists(old_dir) and old_dir != new_dir:
                    os.rename(old_dir, new_dir)
                    
                    # 更新所有相关段的文件路径
                    for seg in task['segments']:
                        if seg['speaker'] == new_name:
                            old_file = seg['file']
                            new_file = old_file.replace(old_dir, new_dir)
                            seg['file'] = new_file
                            seg['relative_path'] = os.path.relpath(new_file, OUTPUT_FOLDER)
                    
                    break
        
        return jsonify({'success': True, 'message': '修改成功'})
    
    except Exception as e:
        return jsonify({'error': f'修改失败: {str(e)}'}), 500

# 添加修改文本内容的API
@app.route('/api/tasks/<task_id>/edit-text', methods=['POST'])
def edit_text(task_id):
    if task_id not in processing_tasks:
        return jsonify({'error': '任务不存在'}), 404
    
    data = request.json
    segment_id = int(data.get('segment_id'))
    new_text = data.get('new_text')
    
    if segment_id is None or not new_text:
        return jsonify({'error': '参数不完整'}), 400
    
    task = processing_tasks[task_id]
    
    try:
        # 确保segment_id有效
        if segment_id < 0 or segment_id >= len(task['segments']):
            return jsonify({'error': '无效的段落ID'}), 400
        
        segment = task['segments'][segment_id]
        speaker = segment['speaker']
        old_text = segment['text']
        
        # 1. 更新内存中的任务数据
        segment['text'] = new_text
        
        # 2. 更新文本文件
        output_dir = os.path.dirname(os.path.dirname(segment['file']))
        txt_file = os.path.join(output_dir, f"{speaker}.txt")
        
        if os.path.exists(txt_file):
            # 读取文件内容
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换文本内容
            updated_content = content.replace(f"{segment['start']} --> {segment['end']}\n{old_text}", 
                                             f"{segment['start']} --> {segment['end']}\n{new_text}")
            
            # 写回文件
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write(updated_content)
        
        return jsonify({'success': True, 'message': '修改成功'})
    
    except Exception as e:
        return jsonify({'error': f'修改失败: {str(e)}'}), 500

if __name__ == '__main__':
    print("服务器版本启动中...")
    app.run(host='0.0.0.0', port=5055, threaded=True) 