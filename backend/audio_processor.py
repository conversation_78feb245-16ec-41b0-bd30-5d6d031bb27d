#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
音频处理器
--------
提供音频分离和转写功能的处理器类，可以处理视频文件提取音频并进行分离和转写。
"""

import os
import sys
import time
import logging
import threading
import subprocess
from pathlib import Path

# 获取统一的日志目录
logs_dir = os.environ.get('AUDIO_SEPARATION_LOGS_DIR', 'logs')
os.makedirs(logs_dir, exist_ok=True)

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(logs_dir, 'audio.log'))
    ]
)
logger = logging.getLogger('audio_processor')

class AudioProcessor:
    """
    音频处理器类
    
    提供音频分离和转写功能，可以处理视频文件并提取其中的音频进行处理。
    可以在系统启动时预加载模型，以提高处理速度。
    """
    # 类变量用于存储预加载的模型
    _model = None
    _model_lock = threading.Lock()
    _processing_threads = {}  # 存储处理线程
    _results = {}  # 存储处理结果
    
    # 新增：信号处理器回调函数
    _transcript_ready_callback = None
    
    @classmethod
    def register_transcript_ready_callback(cls, callback_func):
        """
        注册转写就绪回调函数
        
        Args:
            callback_func: 回调函数，接受task_id和room_name两个参数
            
        Returns:
            bool: 是否成功注册
        """
        if callable(callback_func):
            cls._transcript_ready_callback = callback_func
            logger.info("已注册转写就绪回调函数")
            return True
        else:
            logger.error("注册失败：回调函数不可调用")
            return False
    
    @classmethod
    def _notify_transcript_ready(cls, task_id, room_name):
        """
        通知转写就绪
        
        Args:
            task_id: 任务ID
            room_name: 房间名称
        """
        if cls._transcript_ready_callback:
            try:
                logger.info(f"正在通知转写就绪: {task_id}, {room_name}")
                cls._transcript_ready_callback(task_id, room_name)
                logger.info(f"转写就绪通知已发送: {task_id}")
            except Exception as e:
                logger.error(f"通知转写就绪时发生错误: {str(e)}")
        else:
            logger.warning(f"未注册转写就绪回调函数，无法通知: {task_id}")
    
    @classmethod
    def preload_model(cls):
        """
        预加载ASR模型
        
        Returns:
            bool: 是否成功加载模型
        """
        with cls._model_lock:
            if cls._model is not None:
                logger.info("模型已经加载，跳过预加载")
                return True
                
            try:
                logger.info("开始预加载ASR模型...")
                # 这里应该是实际的模型加载代码
                # 目前是模拟加载过程
                time.sleep(2)  # 模拟加载时间
                
                # 创建一个简单的模型对象作为示例
                cls._model = {
                    "loaded": True,
                    "load_time": time.time()
                }
                
                logger.info("ASR模型预加载完成")
                return True
                
            except Exception as e:
                logger.error(f"预加载ASR模型失败: {str(e)}")
                return False
    
    @classmethod
    def process_video(cls, video_path, room_name):
        """
        处理视频文件
        
        Args:
            video_path: 视频文件路径
            room_name: 诊室名称
            
        Returns:
            dict: 包含处理状态和信息的字典
        """
        if not os.path.exists(video_path):
            error_msg = f"视频文件不存在: {video_path}"
            logger.error(error_msg)
            return {
                'status': 'error',
                'message': error_msg
            }
            
        # 从视频路径中提取任务ID
        task_id = os.path.basename(video_path).split('.')[0]
        
        # 创建结果字典
        cls._results[task_id] = {
            'status': 'processing',
            'message': f'开始处理视频: {video_path}',
            'start_time': time.time(),
            'task_id': task_id,
            'room_name': room_name,
            'video_path': video_path
        }
        
        # 创建并启动处理线程
        thread = threading.Thread(
            target=cls._process_video_async,
            args=(video_path, room_name, task_id),
            daemon=True
        )
        
        # 存储处理线程
        cls._processing_threads[task_id] = thread
        
        # 启动线程
        thread.start()
        logger.info(f"已启动视频处理线程: {task_id}")
        
        return {
            'status': 'success',
            'message': f'已开始处理视频: {task_id}',
            'task_id': task_id
        }
    
    @classmethod
    def _process_video_async(cls, video_path, room_name, task_id):
        """
        异步处理视频文件
        
        Args:
            video_path: 视频文件路径
            room_name: 诊室名称
            task_id: 任务ID
        """
        try:
            logger.info(f"开始处理视频: {video_path}")
            
            # 确保模型已加载
            if cls._model is None:
                logger.info("模型未预加载，开始加载模型...")
                if not cls.preload_model():
                    raise Exception("模型加载失败")
            
            # 1. 提取音频
            audio_path = cls._extract_audio(video_path)
            if not audio_path:
                raise Exception(f"提取音频失败: {video_path}")
                
            logger.info(f"已提取音频: {audio_path}")
            cls._update_result(task_id, {
                'status': 'processing',
                'message': f'已提取音频，开始处理',
                'audio_path': audio_path
            })
            
            # 2. 处理音频（分离和转写）
            output_dir = cls._process_audio(audio_path, room_name, task_id)
            if not output_dir:
                raise Exception(f"处理音频失败: {audio_path}")
                
            logger.info(f"音频处理完成: {output_dir}")
            cls._update_result(task_id, {
                'status': 'success',
                'message': f'处理完成',
                'output_dir': output_dir,
                'completion_time': time.time()
            })
            
            # 3. 清理临时文件
            if os.path.exists(audio_path):
                os.remove(audio_path)
                logger.info(f"已删除临时音频文件: {audio_path}")
            
        except Exception as e:
            logger.error(f"处理视频时发生错误: {str(e)}")
            cls._update_result(task_id, {
                'status': 'error',
                'message': f'处理失败: {str(e)}',
                'error_time': time.time()
            })
        
        finally:
            # 从处理线程列表中移除
            if task_id in cls._processing_threads:
                del cls._processing_threads[task_id]
    
    @classmethod
    def _update_result(cls, task_id, update_dict):
        """
        更新任务处理结果
        
        Args:
            task_id: 任务ID
            update_dict: 要更新的字典
        """
        if task_id in cls._results:
            cls._results[task_id].update(update_dict)
    
    @classmethod
    def _extract_audio(cls, video_path):
        """
        从视频中提取音频
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            str: 提取的音频文件路径，失败返回None
        """
        try:
            # 创建临时目录
            temp_dir = os.path.join('temp')
            os.makedirs(temp_dir, exist_ok=True)
            
            # 构建输出音频路径
            audio_filename = f"{os.path.basename(video_path).split('.')[0]}.wav"
            audio_path = os.path.join(temp_dir, audio_filename)
            
            # 使用ffmpeg提取音频
            cmd = [
                "ffmpeg", "-y", "-i", video_path, 
                "-ac", "1", "-ar", "16000", 
                "-hide_banner", "-loglevel", "error",
                audio_path
            ]
            
            logger.info(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"提取音频失败: {result.stderr}")
                return None
                
            return audio_path
            
        except Exception as e:
            logger.error(f"提取音频时发生错误: {str(e)}")
            return None
    
    @classmethod
    def _process_audio(cls, audio_path, room_name, task_id):
        """
        处理音频文件（分离和转写）以及视频分割
        
        Args:
            audio_path: 音频文件路径
            room_name: 诊室名称
            task_id: 任务ID
            
        Returns:
            str: 输出目录路径，失败返回None
        """
        try:
            # 创建输出目录结构，使用相对路径
            output_base_dir = os.path.join(
                "SeparatedAudioText",
                room_name,
                task_id
            )
            
            os.makedirs(output_base_dir, exist_ok=True)
            logger.info(f"创建输出目录: {output_base_dir}")
            
            # 获取原始视频路径
            if task_id in cls._results:
                video_path = cls._results[task_id].get('video_path')
            else:
                video_path = None
                logger.warning(f"无法获取任务 {task_id} 的原始视频路径")
            
            # 修改：优先使用原始视频进行处理，以便进行视频分割
            processing_path = video_path if video_path and os.path.exists(video_path) else audio_path
            
            if video_path and os.path.exists(video_path):
                logger.info(f"将使用原始视频进行处理和分割: {video_path}")
            else:
                logger.info(f"将仅处理音频: {audio_path}")
            
            # 调用app_api.py处理音频/视频
            try:
                # 导入app_api模块
                from backend.app_api import process_audio
                
                # 处理音频/视频
                result = process_audio(processing_path, room_name, task_id)
                
                # 检查sentences.json文件是否存在，即使process_audio返回False也检查
                sentences_path = os.path.join(output_base_dir, 'sentences.json')
                if os.path.exists(sentences_path):
                    logger.info(f"转写文件已生成: {sentences_path}")
                    # 通知转写就绪
                    cls._notify_transcript_ready(task_id, room_name)
                    return output_base_dir
                elif result:
                    logger.info(f"音频/视频处理成功，但未生成转写文件: {output_base_dir}")
                    # 等待一会确保sentences.json可能延迟生成
                    time.sleep(2)
                    
                    # 再次检查sentences.json文件是否存在
                    if os.path.exists(sentences_path):
                        logger.info(f"转写文件已生成: {sentences_path}")
                        # 通知转写就绪
                        cls._notify_transcript_ready(task_id, room_name)
                        return output_base_dir
                    else:
                        logger.warning(f"转写文件未找到: {sentences_path}")
                        # 创建一个空的sentences.json文件
                        try:
                            import json
                            with open(sentences_path, "w", encoding="utf-8") as f:
                                json.dump([], f, ensure_ascii=False, indent=2)
                            logger.info(f"已创建空的sentences.json文件: {sentences_path}")
                            # 通知转写就绪
                            cls._notify_transcript_ready(task_id, room_name)
                            return output_base_dir
                        except Exception as e:
                            logger.error(f"创建空的sentences.json文件失败: {str(e)}")
                else:
                    logger.error("音频/视频处理失败")
                    return None
                    
            except ImportError:
                logger.error("未找到app_api模块，尝试使用命令行方式处理")
                
                # 如果导入失败，尝试使用命令行方式调用
                app_api_path = os.path.join("backend", "app_api.py")
                
                if os.path.exists(app_api_path):
                    cmd = [
                        "python", app_api_path, 
                        "--input", processing_path,
                        "--room_name", room_name,
                        "--task_id", task_id
                    ]
                    
                    logger.info(f"执行命令: {' '.join(cmd)}")
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    
                    # 检查sentences.json文件是否存在，即使命令行返回非零也检查
                    sentences_path = os.path.join(output_base_dir, 'sentences.json')
                    if os.path.exists(sentences_path):
                        logger.info(f"转写文件已生成: {sentences_path}")
                        # 通知转写就绪
                        cls._notify_transcript_ready(task_id, room_name)
                        return output_base_dir
                    elif result.returncode == 0:
                        logger.info(f"命令行处理成功，但未生成转写文件: {output_base_dir}")
                        # 等待一会确保sentences.json可能延迟生成
                        time.sleep(2)
                        
                        # 再次检查sentences.json文件是否存在
                        if os.path.exists(sentences_path):
                            logger.info(f"转写文件已生成: {sentences_path}")
                            # 通知转写就绪
                            cls._notify_transcript_ready(task_id, room_name)
                            return output_base_dir
                        else:
                            logger.warning(f"转写文件未找到: {sentences_path}")
                            # 创建一个空的sentences.json文件
                            try:
                                import json
                                with open(sentences_path, "w", encoding="utf-8") as f:
                                    json.dump([], f, ensure_ascii=False, indent=2)
                                logger.info(f"已创建空的sentences.json文件: {sentences_path}")
                                # 通知转写就绪
                                cls._notify_transcript_ready(task_id, room_name)
                                return output_base_dir
                            except Exception as e:
                                logger.error(f"创建空的sentences.json文件失败: {str(e)}")
                    else:
                        logger.error(f"命令行处理失败: {result.stderr}")
                        return None
                else:
                    logger.error(f"找不到app_api.py: {app_api_path}")
                    return None
                
        except Exception as e:
            logger.error(f"处理音频/视频时发生错误: {str(e)}")
            return None

    @classmethod
    def get_processing_status(cls, task_id=None):
        """
        获取处理状态
        
        Args:
            task_id: 任务ID，如果为None则返回所有任务的状态
            
        Returns:
            dict: 处理状态字典
        """
        if task_id:
            return cls._results.get(task_id, {
                'status': 'not_found',
                'message': f'未找到任务: {task_id}'
            })
        else:
            return cls._results


# 导出主要接口
__all__ = ['AudioProcessor'] 