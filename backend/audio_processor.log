2025-03-26 17:30:10,914 - main - INFO - 使用配置文件: stream_capture/config/room_stream_mapping.json
2025-03-26 17:30:10,914 - main - INFO - 正在初始化视频流捕获服务...
2025-03-26 17:30:10,914 - main - INFO - 开始预加载ASR模型...
2025-03-26 17:30:10,914 - main - INFO - 正在启动API服务，监听地址: 0.0.0.0:5000
2025-03-26 17:30:10,915 - audio_processor - INFO - 开始预加载ASR模型...
2025-03-26 17:30:10,915 - signal_handler - INFO - 成功加载配置文件，房间映射: ['402诊室', '笔记本电脑']
2025-03-26 17:30:10,915 - signal_handler - INFO - 音频处理模块已加载，将自动处理录制的视频
2025-03-26 17:30:10,915 - signal_handler - INFO - Dify Agent客户端已加载，将自动发送转写结果给Agent
2025-03-26 17:30:10,915 - main - INFO - 服务已启动，按 CTRL+C 停止服务
2025-03-26 17:30:10,920 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-03-26 17:30:10,920 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-03-26 17:30:12,917 - audio_processor - INFO - ASR模型预加载完成
2025-03-26 17:30:12,917 - main - INFO - ASR模型预加载成功
2025-03-26 17:30:48,342 - signal_api - INFO - 接收到开始信号: {"patient_name": "张三", "patient_id": "110101199001011234", "task_id": "TASK2023110100001", "room_id": "402诊室"}
2025-03-26 17:30:48,343 - stream_saver.TASK2023110100001.mp4 - INFO - 开始录制视频: https://livell.cdutcm.edu.cn/live/f2ya.live.flv -> stream_capture/recordings/402诊室/TASK2023110100001.mp4
2025-03-26 17:30:48,346 - stream_saver_manager - INFO - 已启动视频录制: TASK2023110100001, 房间: 402诊室, 患者: 张三, 保存路径: stream_capture/recordings/402诊室/TASK2023110100001.mp4
2025-03-26 17:30:48,348 - werkzeug - INFO - ************** - - [26/Mar/2025 17:30:48] "POST /api/signal/start HTTP/1.1" 200 -
2025-03-26 17:31:01,344 - signal_api - INFO - 接收到停止信号: {"task_id": "TASK2023110100001"}
2025-03-26 17:31:01,344 - stream_saver.TASK2023110100001.mp4 - INFO - 正在停止视频录制: stream_capture/recordings/402诊室/TASK2023110100001.mp4
2025-03-26 17:31:01,463 - stream_saver.TASK2023110100001.mp4 - INFO - 已停止视频录制，文件已保存: stream_capture/recordings/402诊室/TASK2023110100001.mp4
2025-03-26 17:31:01,463 - stream_saver_manager - INFO - 已停止视频录制: TASK2023110100001, 持续时间: 0:00:13.116755, 保存路径: stream_capture/recordings/402诊室/TASK2023110100001.mp4
2025-03-26 17:31:01,463 - signal_handler - INFO - 已启动后台线程处理任务 TASK2023110100001 的录制视频
2025-03-26 17:31:01,463 - signal_handler - INFO - 找到录制的视频文件: stream_capture/recordings/402诊室/TASK2023110100001.mp4
2025-03-26 17:31:01,464 - werkzeug - INFO - ************** - - [26/Mar/2025 17:31:01] "POST /api/signal/stop HTTP/1.1" 200 -
2025-03-26 17:31:01,464 - signal_handler - INFO - 开始处理录制的视频: TASK2023110100001
2025-03-26 17:31:01,465 - audio_processor - INFO - 开始处理视频: stream_capture/recordings/402诊室/TASK2023110100001.mp4
2025-03-26 17:31:01,465 - audio_processor - INFO - 已启动视频处理线程: TASK2023110100001
2025-03-26 17:31:01,465 - signal_handler - INFO - 视频处理成功，任务ID: TASK2023110100001
2025-03-26 17:31:01,465 - audio_processor - INFO - 执行命令: ffmpeg -y -i stream_capture/recordings/402诊室/TASK2023110100001.mp4 -ac 1 -ar 16000 -hide_banner -loglevel error temp/TASK2023110100001.wav
2025-03-26 17:31:01,466 - signal_handler - WARNING - 未找到转写结果文件: SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-26 17:31:01,595 - audio_processor - INFO - 已提取音频: temp/TASK2023110100001.wav
2025-03-26 17:31:01,596 - audio_processor - INFO - 创建输出目录: SeparatedAudioText/402诊室/TASK2023110100001
2025-03-26 17:31:06,056 - backend.app_api - INFO - 首次加载ASR模型...
2025-03-26 17:31:06,056 - root - INFO - download models from model hub: ms
2025-03-26 17:31:08,167 - root - INFO - Loading pretrained params from /root/.cache/modelscope/hub/models/iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch/model.pt
2025-03-26 17:31:08,173 - root - INFO - ckpt: /root/.cache/modelscope/hub/models/iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch/model.pt
2025-03-26 17:31:08,936 - root - INFO - scope_map: ['module.', 'None']
2025-03-26 17:31:08,937 - root - INFO - excludes: None
2025-03-26 17:31:09,020 - root - INFO - Loading ckpt: /root/.cache/modelscope/hub/models/iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch/model.pt, status: <All keys matched successfully>
2025-03-26 17:31:09,451 - root - INFO - Building VAD model.
2025-03-26 17:31:09,451 - root - INFO - download models from model hub: ms
2025-03-26 17:31:09,472 - root - INFO - Loading pretrained params from /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch/model.pt
2025-03-26 17:31:09,472 - root - INFO - ckpt: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch/model.pt
2025-03-26 17:31:09,476 - root - INFO - scope_map: ['module.', 'None']
2025-03-26 17:31:09,477 - root - INFO - excludes: None
2025-03-26 17:31:09,478 - root - INFO - Loading ckpt: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch/model.pt, status: <All keys matched successfully>
2025-03-26 17:31:09,480 - root - INFO - Building punc model.
2025-03-26 17:31:09,480 - root - INFO - download models from model hub: ms
2025-03-26 17:31:10,150 - root - INFO - Loading pretrained params from /root/.cache/modelscope/hub/models/iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch/model.pt
2025-03-26 17:31:10,151 - root - INFO - ckpt: /root/.cache/modelscope/hub/models/iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch/model.pt
2025-03-26 17:31:10,344 - root - INFO - scope_map: ['module.', 'None']
2025-03-26 17:31:10,344 - root - INFO - excludes: None
2025-03-26 17:31:10,361 - root - INFO - Loading ckpt: /root/.cache/modelscope/hub/models/iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch/model.pt, status: <All keys matched successfully>
2025-03-26 17:31:10,459 - root - INFO - Building SPK model.
2025-03-26 17:31:10,460 - root - INFO - download models from model hub: ms
2025-03-26 17:31:10,593 - root - INFO - Loading pretrained params from /root/.cache/modelscope/hub/models/iic/speech_campplus_sv_zh-cn_16k-common/campplus_cn_common.bin
2025-03-26 17:31:10,597 - root - INFO - ckpt: /root/.cache/modelscope/hub/models/iic/speech_campplus_sv_zh-cn_16k-common/campplus_cn_common.bin
2025-03-26 17:31:10,851 - root - INFO - scope_map: ['module.', 'None']
2025-03-26 17:31:10,852 - root - INFO - excludes: None
2025-03-26 17:31:10,873 - root - INFO - Loading ckpt: /root/.cache/modelscope/hub/models/iic/speech_campplus_sv_zh-cn_16k-common/campplus_cn_common.bin, status: <All keys matched successfully>
2025-03-26 17:31:10,905 - backend.app_api - INFO - ASR模型加载成功
2025-03-26 17:31:10,906 - backend.app_api - INFO - 开始处理音频: temp/TASK2023110100001.wav, 任务ID: TASK2023110100001, 诊室: 402诊室
2025-03-26 17:31:12,689 - backend.app_api - INFO - 成功剪切音频段: 00:00:06.680 - 00:00:10.820, 保存到 /data/AudioSeparation/SeparatedAudioText/402诊室/TASK2023110100001/speaker_0/segment_0.wav
2025-03-26 17:31:12,815 - backend.app_api - INFO - 成功剪切音频段: 00:00:10.900 - 00:00:13.675, 保存到 /data/AudioSeparation/SeparatedAudioText/402诊室/TASK2023110100001/speaker_0/segment_1.wav
2025-03-26 17:31:12,816 - backend.app_api - INFO - 音频 temp/TASK2023110100001.wav 处理完成
2025-03-26 17:31:13,000 - backend.app_api - INFO - 已将说话人 0 的音频合并到 /data/AudioSeparation/SeparatedAudioText/402诊室/TASK2023110100001/speaker_0.mp3
2025-03-26 17:31:13,001 - audio_processor - INFO - 音频处理成功: SeparatedAudioText/402诊室/TASK2023110100001
2025-03-26 17:31:13,001 - audio_processor - INFO - 音频处理完成: SeparatedAudioText/402诊室/TASK2023110100001
2025-03-26 17:31:13,001 - audio_processor - INFO - 已删除临时音频文件: temp/TASK2023110100001.wav
2025-03-26 17:34:48,311 - main - INFO - 接收到信号: 15，准备关闭服务...
2025-03-26 17:34:48,582 - main - INFO - 正在关闭服务...
2025-03-26 17:34:49,584 - main - INFO - 服务已关闭
2025-03-27 11:50:47,441 - main - INFO - 使用配置文件: stream_capture/config/room_stream_mapping.json
2025-03-27 11:50:47,441 - main - INFO - 正在初始化视频流捕获服务...
2025-03-27 11:50:47,441 - main - INFO - 开始预加载ASR模型...
2025-03-27 11:50:47,441 - main - INFO - 正在启动API服务，监听地址: 0.0.0.0:5000
2025-03-27 11:50:47,442 - audio_processor - INFO - 开始预加载ASR模型...
2025-03-27 11:50:47,442 - signal_handler - INFO - 成功加载配置文件，房间映射: ['402诊室', '笔记本电脑']
2025-03-27 11:50:47,442 - signal_handler - INFO - 音频处理模块已加载，将自动处理录制的视频
2025-03-27 11:50:47,442 - signal_handler - INFO - Dify Agent客户端已加载，将自动发送转写结果给Agent
2025-03-27 11:50:47,442 - main - INFO - 服务已启动，按 CTRL+C 停止服务
2025-03-27 11:50:47,444 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-03-27 11:50:47,444 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-03-27 11:50:49,444 - audio_processor - INFO - ASR模型预加载完成
2025-03-27 11:50:49,444 - main - INFO - ASR模型预加载成功
2025-03-27 12:19:34,910 - signal_api - INFO - 接收到开始信号: {"patient_name": "张三", "patient_id": "110101199001011234", "task_id": "TASK20231101000011", "room_id": "A402诊室"}
2025-03-27 12:19:34,910 - stream_saver_manager - ERROR - 房间不存在于映射中: A402诊室
2025-03-27 12:19:34,911 - werkzeug - INFO - ************* - - [27/Mar/2025 12:19:34] "[31m[1mPOST /api/signal/start HTTP/1.1[0m" 400 -
2025-03-27 13:14:52,461 - main - INFO - 接收到信号: 15，准备关闭服务...
2025-03-27 13:14:52,776 - main - INFO - 正在关闭服务...
2025-03-27 13:14:53,777 - main - INFO - 服务已关闭
2025-03-27 13:14:56,702 - main - INFO - 使用配置文件: stream_capture/config/room_stream_mapping.json
2025-03-27 13:14:56,702 - main - INFO - 正在初始化视频流捕获服务...
2025-03-27 13:14:56,702 - main - INFO - 开始预加载ASR模型...
2025-03-27 13:14:56,702 - main - INFO - 正在启动API服务，监听地址: 0.0.0.0:5000
2025-03-27 13:14:56,703 - audio_processor - INFO - 开始预加载ASR模型...
2025-03-27 13:14:56,703 - signal_handler - INFO - 成功加载配置文件，房间映射: ['A402诊室', '笔记本电脑']
2025-03-27 13:14:56,703 - signal_handler - INFO - 音频处理模块已加载，将自动处理录制的视频
2025-03-27 13:14:56,703 - signal_handler - INFO - Dify Agent客户端已加载，将自动发送转写结果给Agent
2025-03-27 13:14:56,703 - main - INFO - 服务已启动，按 CTRL+C 停止服务
2025-03-27 13:14:56,706 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-03-27 13:14:56,706 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-03-27 13:14:58,705 - audio_processor - INFO - ASR模型预加载完成
2025-03-27 13:14:58,705 - main - INFO - ASR模型预加载成功
2025-03-27 13:15:44,514 - signal_api - INFO - 接收到开始信号: {"patient_name": "张三", "patient_id": "110101199001011234", "task_id": "TASK2023110100001", "room_id": "A402诊室"}
2025-03-27 13:15:44,514 - stream_saver.TASK2023110100001.mp4 - INFO - 开始录制视频: https://livell.cdutcm.edu.cn/live/f2ya.live.flv -> stream_capture/recordings/A402诊室/TASK2023110100001.mp4
2025-03-27 13:15:44,517 - stream_saver_manager - INFO - 已启动视频录制: TASK2023110100001, 房间: A402诊室, 患者: 张三, 保存路径: stream_capture/recordings/A402诊室/TASK2023110100001.mp4
2025-03-27 13:15:44,519 - werkzeug - INFO - ************** - - [27/Mar/2025 13:15:44] "POST /api/signal/start HTTP/1.1" 200 -
2025-03-27 13:16:22,128 - signal_api - INFO - 接收到停止信号: {"task_id": "TASK2023110100001"}
2025-03-27 13:16:22,128 - stream_saver.TASK2023110100001.mp4 - INFO - 正在停止视频录制: stream_capture/recordings/A402诊室/TASK2023110100001.mp4
2025-03-27 13:16:22,235 - stream_saver.TASK2023110100001.mp4 - INFO - 已停止视频录制，文件已保存: stream_capture/recordings/A402诊室/TASK2023110100001.mp4
2025-03-27 13:16:22,236 - stream_saver_manager - INFO - 已停止视频录制: TASK2023110100001, 持续时间: 0:00:37.718133, 保存路径: stream_capture/recordings/A402诊室/TASK2023110100001.mp4
2025-03-27 13:16:22,236 - signal_handler - INFO - 已启动后台线程处理任务 TASK2023110100001 的录制视频
2025-03-27 13:16:22,236 - signal_handler - INFO - 找到录制的视频文件: stream_capture/recordings/A402诊室/TASK2023110100001.mp4
2025-03-27 13:16:22,237 - werkzeug - INFO - ************** - - [27/Mar/2025 13:16:22] "POST /api/signal/stop HTTP/1.1" 200 -
2025-03-27 13:16:22,237 - signal_handler - INFO - 开始处理录制的视频: TASK2023110100001
2025-03-27 13:16:22,238 - audio_processor - INFO - 开始处理视频: stream_capture/recordings/A402诊室/TASK2023110100001.mp4
2025-03-27 13:16:22,238 - audio_processor - INFO - 已启动视频处理线程: TASK2023110100001
2025-03-27 13:16:22,238 - audio_processor - INFO - 执行命令: ffmpeg -y -i stream_capture/recordings/A402诊室/TASK2023110100001.mp4 -ac 1 -ar 16000 -hide_banner -loglevel error temp/TASK2023110100001.wav
2025-03-27 13:16:22,239 - signal_handler - INFO - 视频处理成功，任务ID: TASK2023110100001
2025-03-27 13:16:22,239 - signal_handler - WARNING - 未找到转写结果文件: SeparatedAudioText/A402诊室/TASK2023110100001/sentences.json
2025-03-27 13:16:22,333 - audio_processor - INFO - 已提取音频: temp/TASK2023110100001.wav
2025-03-27 13:16:22,333 - audio_processor - INFO - 创建输出目录: SeparatedAudioText/A402诊室/TASK2023110100001
2025-03-27 13:16:26,700 - backend.app_api - INFO - 首次加载ASR模型...
2025-03-27 13:16:26,700 - root - INFO - download models from model hub: ms
2025-03-27 13:16:28,819 - root - INFO - Loading pretrained params from /root/.cache/modelscope/hub/models/iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch/model.pt
2025-03-27 13:16:28,825 - root - INFO - ckpt: /root/.cache/modelscope/hub/models/iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch/model.pt
2025-03-27 13:16:29,560 - root - INFO - scope_map: ['module.', 'None']
2025-03-27 13:16:29,560 - root - INFO - excludes: None
2025-03-27 13:16:29,642 - root - INFO - Loading ckpt: /root/.cache/modelscope/hub/models/iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch/model.pt, status: <All keys matched successfully>
2025-03-27 13:16:30,123 - root - INFO - Building VAD model.
2025-03-27 13:16:30,123 - root - INFO - download models from model hub: ms
2025-03-27 13:16:30,141 - root - INFO - Loading pretrained params from /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch/model.pt
2025-03-27 13:16:30,141 - root - INFO - ckpt: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch/model.pt
2025-03-27 13:16:30,144 - root - INFO - scope_map: ['module.', 'None']
2025-03-27 13:16:30,144 - root - INFO - excludes: None
2025-03-27 13:16:30,145 - root - INFO - Loading ckpt: /root/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch/model.pt, status: <All keys matched successfully>
2025-03-27 13:16:30,147 - root - INFO - Building punc model.
2025-03-27 13:16:30,147 - root - INFO - download models from model hub: ms
2025-03-27 13:16:30,784 - root - INFO - Loading pretrained params from /root/.cache/modelscope/hub/models/iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch/model.pt
2025-03-27 13:16:30,784 - root - INFO - ckpt: /root/.cache/modelscope/hub/models/iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch/model.pt
2025-03-27 13:16:30,976 - root - INFO - scope_map: ['module.', 'None']
2025-03-27 13:16:30,976 - root - INFO - excludes: None
2025-03-27 13:16:30,992 - root - INFO - Loading ckpt: /root/.cache/modelscope/hub/models/iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch/model.pt, status: <All keys matched successfully>
2025-03-27 13:16:31,089 - root - INFO - Building SPK model.
2025-03-27 13:16:31,089 - root - INFO - download models from model hub: ms
2025-03-27 13:16:31,221 - root - INFO - Loading pretrained params from /root/.cache/modelscope/hub/models/iic/speech_campplus_sv_zh-cn_16k-common/campplus_cn_common.bin
2025-03-27 13:16:31,225 - root - INFO - ckpt: /root/.cache/modelscope/hub/models/iic/speech_campplus_sv_zh-cn_16k-common/campplus_cn_common.bin
2025-03-27 13:16:31,436 - root - INFO - scope_map: ['module.', 'None']
2025-03-27 13:16:31,436 - root - INFO - excludes: None
2025-03-27 13:16:31,457 - root - INFO - Loading ckpt: /root/.cache/modelscope/hub/models/iic/speech_campplus_sv_zh-cn_16k-common/campplus_cn_common.bin, status: <All keys matched successfully>
2025-03-27 13:16:31,491 - backend.app_api - INFO - ASR模型加载成功
2025-03-27 13:16:31,491 - backend.app_api - INFO - 开始处理音频: temp/TASK2023110100001.wav, 任务ID: TASK2023110100001, 诊室: A402诊室
2025-03-27 13:16:32,322 - root - INFO - decoding, utt: rand_key_2yW4Acq9GFz6Y, empty speech
2025-03-27 13:16:32,322 - backend.app_api - WARNING - 没有识别到任何文本
2025-03-27 13:16:32,322 - audio_processor - ERROR - 音频处理失败
2025-03-27 13:16:32,322 - audio_processor - ERROR - 处理视频时发生错误: 处理音频失败: temp/TASK2023110100001.wav
2025-03-27 13:55:30,679 - main - INFO - 接收到信号: 15，准备关闭服务...
2025-03-27 13:55:31,035 - main - INFO - 正在关闭服务...
2025-03-27 13:55:32,036 - main - INFO - 服务已关闭
