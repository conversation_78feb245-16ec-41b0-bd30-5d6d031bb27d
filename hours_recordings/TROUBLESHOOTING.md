# 视频录制脚本故障排除指南

## 原始脚本中的问题

在对比测试录制脚本(test_record.py)和原始自动录制脚本(auto_record.py)后，我们发现了以下可能导致录制失败的问题：

1. **错误处理不完善**
   - 原脚本仅检查HTTP状态码，但未检查API返回的JSON中的错误码
   - 缺少对API返回内容的详细解析

2. **日志记录不充分**
   - 没有记录完整的API请求和响应内容
   - 缺少关键操作的详细日志信息

3. **流可用性验证缺失**
   - 未验证流是否实际可用
   - 未记录成功录制的流的数量和比例

4. **系统信息记录不足**
   - 未记录脚本运行环境信息
   - 未定期记录系统时间确认

## 已完成的修复

1. **增强了错误处理**
   - 添加了API返回内容的JSON解析
   - 检查API返回的错误码和消息

2. **改进了日志记录**
   - 记录完整的API请求URL和参数
   - 记录详细的响应内容和状态
   - 添加了统计信息(成功/失败数量)

3. **添加了辅助功能**
   - 创建了专用的API请求发送函数
   - 添加了定期系统状态记录
   - 记录脚本启动环境信息

4. **提供了测试工具**
   - test_record.py: 快速测试10秒录制功能
   - debug_auto_record.py: 增强版定时录制脚本，带有更详细的日志

## 使用新脚本

我们提供了以下改进的脚本：

1. **test_record.py**
   - 短时间测试录制功能(10秒)
   - 详细记录API调用过程
   - 使用方法: `python test_record.py`

2. **debug_auto_record.py**
   - 调试版自动录制脚本
   - 包含更详细的日志和错误处理
   - 使用方法与原脚本相同: `python debug_auto_record.py`

3. **修复后的auto_record.py**
   - 原脚本的改进版本
   - 包含必要的错误处理和日志记录
   - 使用方法不变

## 常见问题

1. **录制未成功启动**
   - 检查流是否在推送
   - 查看响应内容中的错误信息
   - 确认API参数是否正确

2. **录制未成功停止**
   - 检查流是否仍然可用
   - 查看响应内容中的错误信息
   - 可能需要手动停止

3. **定时任务未执行**
   - 检查系统时间是否正确
   - 确保脚本持续运行
   - 查看日志文件中的错误信息

## 日志文件

- auto_record.log: 主脚本日志
- debug_auto_record.log: 调试版脚本日志

定期检查日志文件，了解录制状态和可能的错误。 