#!/bin/bash

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# 停止正在运行的录制脚本
PID=$(pgrep -f "python3 $SCRIPT_DIR/auto_record.py" || pgrep -f "python $SCRIPT_DIR/auto_record.py")

if [ -z "$PID" ]; then
    echo "自动录制脚本未运行"
    exit 0
fi

echo "正在停止自动录制脚本 (PID: $PID)..."
kill $PID

# 检查是否成功停止
sleep 2
if ps -p $PID > /dev/null; then
    echo "无法停止脚本，尝试强制停止..."
    kill -9 $PID
    sleep 1
fi

# 最终检查
if ps -p $PID > /dev/null; then
    echo "无法停止自动录制脚本，请手动终止进程"
    exit 1
else
    echo "自动录制脚本已停止"
    
    # 如果当前时间在8点到18点之间，询问是否要手动停止录制
    HOUR=$(date +%H)
    if [ $HOUR -ge 8 ] && [ $HOUR -lt 18 ]; then
        echo "当前是工作时间，您可能需要手动停止正在进行的录制"
        read -p "是否要停止所有正在录制的流? (y/n): " STOP_RECORDING
        if [[ "$STOP_RECORDING" == "y" || "$STOP_RECORDING" == "Y" ]]; then
            python3 -c "
import sys, os
sys.path.append('$SCRIPT_DIR')
try:
    from auto_record import stop_recording
    print('正在停止所有录制...')
    stop_recording()
    print('录制已停止')
except Exception as e:
    print(f'停止录制时出错: {e}')
"
        fi
    fi
fi 