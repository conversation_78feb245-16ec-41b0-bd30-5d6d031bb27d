#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import logging
import os
import time
from datetime import datetime

import requests
import schedule

# 配置日志
log_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(log_dir, "auto_record.log")
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file, encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# API相关参数
API_BASE_URL = "http://*************:8999/index/api"
API_PARAMS = {
    "secret": "z500PrnbB66d1txYMDyTVqHn4qFA41g8",
    "type": "1",
    "vhost": "__defaultVhost__",
    "app": "live"
}


def load_streams():
    """从stream.json文件加载推流码"""
    try:
        json_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stream.json")
        logger.info(f"从 {json_path} 加载推流码")
        
        with open(json_path, "r", encoding="utf-8") as f:
            data = json.load(f)
            
        streams = [item["stream"] for item in data if "stream" in item]
        logger.info(f"成功加载 {len(streams)} 个推流码: {streams}")
        return streams
    except Exception as e:
        logger.error(f"加载推流码失败: {e}")
        return []


def send_api_request(action, stream):
    """发送API请求并详细记录结果"""
    params = API_PARAMS.copy()
    params["stream"] = stream
    
    url = f"{API_BASE_URL}/{action}"
    full_url = f"{url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}"
    logger.info(f"发送请求: {full_url}")
    
    try:
        response = requests.get(url, params=params, timeout=10)
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get("code") == 0:
                    logger.info(f"服务器确认{action}操作成功")
                    return True
                else:
                    logger.error(f"服务器返回错误: {result.get('code')} - {result.get('msg', '无错误信息')}")
                    return False
            except json.JSONDecodeError:
                logger.warning(f"无法解析响应为JSON: {response.text}")
                return True  # 即使无法解析但状态码是200，仍然认为可能成功
        else:
            logger.error(f"请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"发送请求时出错: {e}")
        return False


def start_recording():
    """开始录制所有流"""
    logger.info("开始录制任务执行")
    streams = load_streams()
    
    if not streams:
        logger.error("没有找到推流码，无法开始录制")
        return

    success_count = 0
    for stream in streams:
        if send_api_request("startRecord", stream):
            success_count += 1
    
    logger.info(f"成功开始录制 {success_count}/{len(streams)} 个流")


def stop_recording():
    """停止录制所有流"""
    logger.info("停止录制任务执行")
    streams = load_streams()
    
    if not streams:
        logger.error("没有找到推流码，无法停止录制")
        return
        
    success_count = 0
    for stream in streams:
        if send_api_request("stopRecord", stream):
            success_count += 1
    
    logger.info(f"成功停止录制 {success_count}/{len(streams)} 个流")


def main():
    logger.info("自动录制脚本启动")
    logger.info(f"脚本路径: {os.path.abspath(__file__)}")
    current_time = datetime.now()
    logger.info(f"当前系统时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 设置定时任务
    schedule.every().day.at("08:00").do(start_recording)
    schedule.every().day.at("18:00").do(stop_recording)
    
    logger.info("定时任务已设置：每天 08:00 开始录制，18:00 停止录制")
    
    # 根据当前时间执行一次相应操作
    current_hour = current_time.hour
    if 8 <= current_hour < 18:
        logger.info(f"脚本启动时间在8点至18点之间（当前{current_hour}点），立即执行开始录制")
        start_recording()
    else:
        logger.info(f"脚本启动时间在非工作时间（当前{current_hour}点），不执行录制")
    
    # 运行循环
    iteration = 0
    while True:
        try:
            # 每小时记录一次运行状态
            if iteration % 60 == 0:  # 每小时记录一次
                logger.info(f"定时任务运行中... 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
            iteration += 1
        except Exception as e:
            logger.error(f"运行定时任务时发生错误: {e}")
            time.sleep(60)  # 发生错误后等待一分钟再继续


if __name__ == "__main__":
    main() 