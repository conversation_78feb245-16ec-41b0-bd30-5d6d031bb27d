# 自动录制脚本

这个脚本用于自动在指定时间开始和停止录制视频流。

## 功能

- 每天早上8:00自动开始录制
- 每天下午18:00（6:00 PM）自动停止录制
- 支持多个流同时录制
- 自动记录日志

## 文件说明

- `auto_record.py`: 主脚本文件（已修复版本）
- `stream.json`: 存储推流码的配置文件
- `requirements.txt`: 依赖库列表
- `start_recorder.sh`: 启动录制的Shell脚本
- `stop_recorder.sh`: 停止录制的Shell脚本
- `test_record.py`: **新增** 测试录制功能的脚本（录制10秒）
- `debug_auto_record.py`: **新增** 调试版本的自动录制脚本
- `TROUBLESHOOTING.md`: **新增** 故障排除指南

## 安装

1. 安装Python依赖:

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法一：使用Shell脚本（推荐）

1. 启动录制脚本:

```bash
./start_recorder.sh
```

2. 停止录制脚本:

```bash
./stop_recorder.sh
```

### 方法二：直接使用Python

1. 确保`stream.json`文件中包含了需要录制的推流码
2. 运行脚本:

```bash
python auto_record.py
```

3. 让脚本在后台运行(Linux):

```bash
nohup python auto_record.py > /dev/null 2>&1 &
```

### 测试录制功能

如需快速测试录制功能是否正常工作，可以运行测试脚本：

```bash
python test_record.py
```

此脚本将启动录制，等待10秒后停止录制，并提供详细的操作日志。

### 使用调试版脚本

如果需要更详细的日志和更好的错误处理，可以使用调试版脚本：

```bash
python debug_auto_record.py
```

或者后台运行：

```bash
nohup python debug_auto_record.py > /dev/null 2>&1 &
```

## 故障排除

如果遇到录制问题，请参考`TROUBLESHOOTING.md`文件，其中包含了常见问题的解决方法和对已修复问题的说明。

## 日志

脚本运行日志将保存在同目录下的日志文件中：

- `auto_record.log`: 主脚本日志
- `debug_auto_record.log`: 调试版脚本日志

## 自定义配置

如需修改录制的时间，请编辑相应脚本文件中的以下行:

```python
schedule.every().day.at("08:00").do(start_recording)  # 开始录制时间
schedule.every().day.at("18:00").do(stop_recording)   # 停止录制时间
``` 