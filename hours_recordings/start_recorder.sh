#!/bin/bash

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3, 请安装Python3后再试"
    exit 1
fi

# 检查依赖是否安装
python3 -c "import requests, schedule" &> /dev/null
if [ $? -ne 0 ]; then
    echo "正在安装依赖..."
    pip install -r requirements.txt
fi

# 检查是否已运行
PID=$(pgrep -f "python3 $SCRIPT_DIR/auto_record.py" || pgrep -f "python $SCRIPT_DIR/auto_record.py")
if [ ! -z "$PID" ]; then
    echo "自动录制脚本已在运行中 (PID: $PID)"
    exit 0
fi

# 启动脚本
echo "正在启动自动录制脚本..."
nohup python3 "$SCRIPT_DIR/auto_record.py" > /dev/null 2>&1 &

# 检查是否成功启动
sleep 2
PID=$(pgrep -f "python3 $SCRIPT_DIR/auto_record.py" || pgrep -f "python $SCRIPT_DIR/auto_record.py")
if [ ! -z "$PID" ]; then
    echo "自动录制脚本已成功启动 (PID: $PID)"
else
    echo "自动录制脚本启动失败，请检查日志文件"
fi 