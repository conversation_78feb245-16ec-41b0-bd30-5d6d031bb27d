2025-03-27 18:20:53,953 - INFO - 自动录制脚本启动
2025-03-27 18:20:53,953 - INFO - 脚本路径: /data/AudioSeparation/hours_recordings/auto_record.py
2025-03-27 18:20:53,953 - INFO - 当前系统时间: 2025-03-27 18:20:53
2025-03-27 18:20:53,954 - INFO - 定时任务已设置：每天 08:00 开始录制，18:00 停止录制
2025-03-27 18:20:53,954 - INFO - 脚本启动时间在非工作时间（当前18点），不执行录制
2025-03-27 18:20:53,954 - INFO - 定时任务运行中... 当前时间: 2025-03-27 18:20:53
2025-03-27 19:20:57,200 - INFO - 定时任务运行中... 当前时间: 2025-03-27 19:20:57
2025-03-27 20:21:00,128 - INFO - 定时任务运行中... 当前时间: 2025-03-27 20:21:00
2025-03-27 21:21:03,240 - INFO - 定时任务运行中... 当前时间: 2025-03-27 21:21:03
2025-03-27 22:21:06,164 - INFO - 定时任务运行中... 当前时间: 2025-03-27 22:21:06
2025-03-27 23:21:08,965 - INFO - 定时任务运行中... 当前时间: 2025-03-27 23:21:08
2025-03-28 00:21:12,200 - INFO - 定时任务运行中... 当前时间: 2025-03-28 00:21:12
2025-03-28 01:21:15,401 - INFO - 定时任务运行中... 当前时间: 2025-03-28 01:21:15
2025-03-28 02:21:18,281 - INFO - 定时任务运行中... 当前时间: 2025-03-28 02:21:18
2025-03-28 03:21:21,282 - INFO - 定时任务运行中... 当前时间: 2025-03-28 03:21:21
2025-03-28 04:21:24,345 - INFO - 定时任务运行中... 当前时间: 2025-03-28 04:21:24
2025-03-28 05:21:27,180 - INFO - 定时任务运行中... 当前时间: 2025-03-28 05:21:27
2025-03-28 06:21:29,779 - INFO - 定时任务运行中... 当前时间: 2025-03-28 06:21:29
2025-03-28 07:21:32,803 - INFO - 定时任务运行中... 当前时间: 2025-03-28 07:21:32
2025-03-28 08:00:34,701 - INFO - 开始录制任务执行
2025-03-28 08:00:34,701 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-03-28 08:00:34,702 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-03-28 08:00:34,702 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-03-28 08:00:34,708 - INFO - 响应状态码: 200
2025-03-28 08:00:34,709 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-03-28 08:00:34,709 - INFO - 服务器确认startRecord操作成功
2025-03-28 08:00:34,709 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-03-28 08:00:34,712 - INFO - 响应状态码: 200
2025-03-28 08:00:34,712 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-03-28 08:00:34,712 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-03-28 08:00:34,713 - INFO - 成功开始录制 1/2 个流
2025-03-28 08:21:35,530 - INFO - 定时任务运行中... 当前时间: 2025-03-28 08:21:35
2025-03-28 09:21:38,399 - INFO - 定时任务运行中... 当前时间: 2025-03-28 09:21:38
2025-03-28 10:21:41,437 - INFO - 定时任务运行中... 当前时间: 2025-03-28 10:21:41
2025-03-28 11:21:44,265 - INFO - 定时任务运行中... 当前时间: 2025-03-28 11:21:44
2025-03-28 12:21:47,116 - INFO - 定时任务运行中... 当前时间: 2025-03-28 12:21:47
2025-03-28 13:21:49,912 - INFO - 定时任务运行中... 当前时间: 2025-03-28 13:21:49
2025-03-28 14:21:52,841 - INFO - 定时任务运行中... 当前时间: 2025-03-28 14:21:52
2025-03-28 15:21:55,636 - INFO - 定时任务运行中... 当前时间: 2025-03-28 15:21:55
2025-03-28 16:21:58,589 - INFO - 定时任务运行中... 当前时间: 2025-03-28 16:21:58
2025-03-28 17:22:01,675 - INFO - 定时任务运行中... 当前时间: 2025-03-28 17:22:01
2025-03-28 18:00:03,638 - INFO - 停止录制任务执行
2025-03-28 18:00:03,639 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-03-28 18:00:03,639 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-03-28 18:00:03,639 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-03-28 18:00:03,644 - INFO - 响应状态码: 200
2025-03-28 18:00:03,644 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-03-28 18:00:03,645 - INFO - 服务器确认stopRecord操作成功
2025-03-28 18:00:03,645 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-03-28 18:00:03,649 - INFO - 响应状态码: 200
2025-03-28 18:00:03,650 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-03-28 18:00:03,650 - INFO - 服务器确认stopRecord操作成功
2025-03-28 18:00:03,650 - INFO - 成功停止录制 2/2 个流
2025-03-28 18:22:04,685 - INFO - 定时任务运行中... 当前时间: 2025-03-28 18:22:04
2025-03-28 19:22:07,935 - INFO - 定时任务运行中... 当前时间: 2025-03-28 19:22:07
2025-03-28 20:22:10,952 - INFO - 定时任务运行中... 当前时间: 2025-03-28 20:22:10
2025-03-28 21:22:13,603 - INFO - 定时任务运行中... 当前时间: 2025-03-28 21:22:13
2025-03-28 22:22:16,731 - INFO - 定时任务运行中... 当前时间: 2025-03-28 22:22:16
2025-03-28 23:22:19,569 - INFO - 定时任务运行中... 当前时间: 2025-03-28 23:22:19
2025-03-29 00:22:22,465 - INFO - 定时任务运行中... 当前时间: 2025-03-29 00:22:22
2025-03-29 01:22:25,298 - INFO - 定时任务运行中... 当前时间: 2025-03-29 01:22:25
2025-03-29 02:22:28,195 - INFO - 定时任务运行中... 当前时间: 2025-03-29 02:22:28
2025-03-29 03:22:30,700 - INFO - 定时任务运行中... 当前时间: 2025-03-29 03:22:30
2025-03-29 04:22:33,679 - INFO - 定时任务运行中... 当前时间: 2025-03-29 04:22:33
2025-03-29 05:22:36,477 - INFO - 定时任务运行中... 当前时间: 2025-03-29 05:22:36
2025-03-29 06:22:39,365 - INFO - 定时任务运行中... 当前时间: 2025-03-29 06:22:39
2025-03-29 07:22:42,116 - INFO - 定时任务运行中... 当前时间: 2025-03-29 07:22:42
2025-03-29 08:00:44,007 - INFO - 开始录制任务执行
2025-03-29 08:00:44,007 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-03-29 08:00:44,007 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-03-29 08:00:44,007 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-03-29 08:00:44,013 - INFO - 响应状态码: 200
2025-03-29 08:00:44,013 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-03-29 08:00:44,013 - INFO - 服务器确认startRecord操作成功
2025-03-29 08:00:44,013 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-03-29 08:00:44,017 - INFO - 响应状态码: 200
2025-03-29 08:00:44,017 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-03-29 08:00:44,017 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-03-29 08:00:44,018 - INFO - 成功开始录制 1/2 个流
2025-03-29 08:22:45,081 - INFO - 定时任务运行中... 当前时间: 2025-03-29 08:22:45
2025-03-29 09:22:47,733 - INFO - 定时任务运行中... 当前时间: 2025-03-29 09:22:47
2025-03-29 10:22:50,693 - INFO - 定时任务运行中... 当前时间: 2025-03-29 10:22:50
2025-03-29 11:22:53,573 - INFO - 定时任务运行中... 当前时间: 2025-03-29 11:22:53
2025-03-29 12:22:56,540 - INFO - 定时任务运行中... 当前时间: 2025-03-29 12:22:56
2025-03-29 13:22:59,538 - INFO - 定时任务运行中... 当前时间: 2025-03-29 13:22:59
2025-03-29 14:23:02,181 - INFO - 定时任务运行中... 当前时间: 2025-03-29 14:23:02
2025-03-29 15:23:04,907 - INFO - 定时任务运行中... 当前时间: 2025-03-29 15:23:04
2025-03-29 16:23:08,154 - INFO - 定时任务运行中... 当前时间: 2025-03-29 16:23:08
2025-03-29 17:23:11,153 - INFO - 定时任务运行中... 当前时间: 2025-03-29 17:23:11
2025-03-29 18:00:12,816 - INFO - 停止录制任务执行
2025-03-29 18:00:12,816 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-03-29 18:00:12,816 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-03-29 18:00:12,816 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-03-29 18:00:12,821 - INFO - 响应状态码: 200
2025-03-29 18:00:12,821 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-03-29 18:00:12,821 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-03-29 18:00:12,821 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-03-29 18:00:12,825 - INFO - 响应状态码: 200
2025-03-29 18:00:12,825 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-03-29 18:00:12,826 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-03-29 18:00:12,826 - INFO - 成功停止录制 0/2 个流
2025-03-29 18:23:13,991 - INFO - 定时任务运行中... 当前时间: 2025-03-29 18:23:13
2025-03-29 19:23:16,652 - INFO - 定时任务运行中... 当前时间: 2025-03-29 19:23:16
2025-03-29 20:23:19,529 - INFO - 定时任务运行中... 当前时间: 2025-03-29 20:23:19
2025-03-29 21:23:22,381 - INFO - 定时任务运行中... 当前时间: 2025-03-29 21:23:22
2025-03-29 22:23:25,312 - INFO - 定时任务运行中... 当前时间: 2025-03-29 22:23:25
2025-03-29 23:23:28,196 - INFO - 定时任务运行中... 当前时间: 2025-03-29 23:23:28
2025-03-30 00:23:31,190 - INFO - 定时任务运行中... 当前时间: 2025-03-30 00:23:31
2025-03-30 01:23:34,089 - INFO - 定时任务运行中... 当前时间: 2025-03-30 01:23:34
2025-03-30 02:23:37,073 - INFO - 定时任务运行中... 当前时间: 2025-03-30 02:23:37
2025-03-30 03:23:39,973 - INFO - 定时任务运行中... 当前时间: 2025-03-30 03:23:39
2025-03-30 04:23:42,829 - INFO - 定时任务运行中... 当前时间: 2025-03-30 04:23:42
2025-03-30 05:23:45,521 - INFO - 定时任务运行中... 当前时间: 2025-03-30 05:23:45
2025-03-30 06:23:48,554 - INFO - 定时任务运行中... 当前时间: 2025-03-30 06:23:48
2025-03-30 07:23:51,805 - INFO - 定时任务运行中... 当前时间: 2025-03-30 07:23:51
2025-03-30 08:00:53,880 - INFO - 开始录制任务执行
2025-03-30 08:00:53,880 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-03-30 08:00:53,881 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-03-30 08:00:53,881 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-03-30 08:00:53,885 - INFO - 响应状态码: 200
2025-03-30 08:00:53,885 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-03-30 08:00:53,886 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-03-30 08:00:53,886 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-03-30 08:00:53,890 - INFO - 响应状态码: 200
2025-03-30 08:00:53,890 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-03-30 08:00:53,890 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-03-30 08:00:53,890 - INFO - 成功开始录制 0/2 个流
2025-03-30 08:23:55,006 - INFO - 定时任务运行中... 当前时间: 2025-03-30 08:23:55
2025-03-30 09:23:58,106 - INFO - 定时任务运行中... 当前时间: 2025-03-30 09:23:58
2025-03-30 10:24:00,916 - INFO - 定时任务运行中... 当前时间: 2025-03-30 10:24:00
2025-03-30 11:24:03,727 - INFO - 定时任务运行中... 当前时间: 2025-03-30 11:24:03
2025-03-30 12:24:06,705 - INFO - 定时任务运行中... 当前时间: 2025-03-30 12:24:06
2025-03-30 13:24:09,681 - INFO - 定时任务运行中... 当前时间: 2025-03-30 13:24:09
2025-03-30 14:24:12,624 - INFO - 定时任务运行中... 当前时间: 2025-03-30 14:24:12
2025-03-30 15:24:15,477 - INFO - 定时任务运行中... 当前时间: 2025-03-30 15:24:15
2025-03-30 16:24:18,198 - INFO - 定时任务运行中... 当前时间: 2025-03-30 16:24:18
2025-03-30 17:24:21,002 - INFO - 定时任务运行中... 当前时间: 2025-03-30 17:24:21
2025-03-30 18:00:22,709 - INFO - 停止录制任务执行
2025-03-30 18:00:22,710 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-03-30 18:00:22,710 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-03-30 18:00:22,710 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-03-30 18:00:22,714 - INFO - 响应状态码: 200
2025-03-30 18:00:22,715 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-03-30 18:00:22,715 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-03-30 18:00:22,715 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-03-30 18:00:22,720 - INFO - 响应状态码: 200
2025-03-30 18:00:22,720 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-03-30 18:00:22,720 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-03-30 18:00:22,720 - INFO - 成功停止录制 0/2 个流
2025-03-30 18:24:23,757 - INFO - 定时任务运行中... 当前时间: 2025-03-30 18:24:23
2025-03-30 19:24:26,275 - INFO - 定时任务运行中... 当前时间: 2025-03-30 19:24:26
2025-03-30 20:24:28,903 - INFO - 定时任务运行中... 当前时间: 2025-03-30 20:24:28
2025-03-30 21:24:32,024 - INFO - 定时任务运行中... 当前时间: 2025-03-30 21:24:32
2025-03-30 22:24:34,885 - INFO - 定时任务运行中... 当前时间: 2025-03-30 22:24:34
2025-03-30 23:24:37,929 - INFO - 定时任务运行中... 当前时间: 2025-03-30 23:24:37
2025-03-31 00:24:40,771 - INFO - 定时任务运行中... 当前时间: 2025-03-31 00:24:40
2025-03-31 01:24:43,846 - INFO - 定时任务运行中... 当前时间: 2025-03-31 01:24:43
2025-03-31 02:24:46,793 - INFO - 定时任务运行中... 当前时间: 2025-03-31 02:24:46
2025-03-31 03:24:49,949 - INFO - 定时任务运行中... 当前时间: 2025-03-31 03:24:49
2025-03-31 04:24:53,070 - INFO - 定时任务运行中... 当前时间: 2025-03-31 04:24:53
2025-03-31 05:24:55,846 - INFO - 定时任务运行中... 当前时间: 2025-03-31 05:24:55
2025-03-31 06:24:58,820 - INFO - 定时任务运行中... 当前时间: 2025-03-31 06:24:58
2025-03-31 07:25:01,702 - INFO - 定时任务运行中... 当前时间: 2025-03-31 07:25:01
2025-03-31 08:00:03,529 - INFO - 开始录制任务执行
2025-03-31 08:00:03,530 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-03-31 08:00:03,530 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-03-31 08:00:03,530 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-03-31 08:00:03,535 - INFO - 响应状态码: 200
2025-03-31 08:00:03,535 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-03-31 08:00:03,535 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-03-31 08:00:03,535 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-03-31 08:00:03,540 - INFO - 响应状态码: 200
2025-03-31 08:00:03,540 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-03-31 08:00:03,540 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-03-31 08:00:03,540 - INFO - 成功开始录制 0/2 个流
2025-03-31 08:25:04,767 - INFO - 定时任务运行中... 当前时间: 2025-03-31 08:25:04
2025-03-31 09:25:07,395 - INFO - 定时任务运行中... 当前时间: 2025-03-31 09:25:07
2025-03-31 10:25:10,264 - INFO - 定时任务运行中... 当前时间: 2025-03-31 10:25:10
2025-03-31 11:25:13,129 - INFO - 定时任务运行中... 当前时间: 2025-03-31 11:25:13
2025-03-31 12:25:16,161 - INFO - 定时任务运行中... 当前时间: 2025-03-31 12:25:16
2025-03-31 13:25:19,142 - INFO - 定时任务运行中... 当前时间: 2025-03-31 13:25:19
2025-03-31 14:25:21,659 - INFO - 定时任务运行中... 当前时间: 2025-03-31 14:25:21
2025-03-31 15:25:24,661 - INFO - 定时任务运行中... 当前时间: 2025-03-31 15:25:24
2025-03-31 16:25:27,625 - INFO - 定时任务运行中... 当前时间: 2025-03-31 16:25:27
2025-03-31 17:25:30,252 - INFO - 定时任务运行中... 当前时间: 2025-03-31 17:25:30
2025-03-31 18:00:32,163 - INFO - 停止录制任务执行
2025-03-31 18:00:32,164 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-03-31 18:00:32,164 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-03-31 18:00:32,164 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-03-31 18:00:32,169 - INFO - 响应状态码: 200
2025-03-31 18:00:32,169 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-03-31 18:00:32,169 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-03-31 18:00:32,169 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-03-31 18:00:32,173 - INFO - 响应状态码: 200
2025-03-31 18:00:32,173 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-03-31 18:00:32,173 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-03-31 18:00:32,174 - INFO - 成功停止录制 0/2 个流
2025-03-31 18:25:33,390 - INFO - 定时任务运行中... 当前时间: 2025-03-31 18:25:33
2025-03-31 19:25:36,498 - INFO - 定时任务运行中... 当前时间: 2025-03-31 19:25:36
2025-03-31 20:25:39,752 - INFO - 定时任务运行中... 当前时间: 2025-03-31 20:25:39
2025-03-31 21:25:42,749 - INFO - 定时任务运行中... 当前时间: 2025-03-31 21:25:42
2025-03-31 22:25:45,696 - INFO - 定时任务运行中... 当前时间: 2025-03-31 22:25:45
2025-03-31 23:25:48,578 - INFO - 定时任务运行中... 当前时间: 2025-03-31 23:25:48
2025-04-01 00:25:51,667 - INFO - 定时任务运行中... 当前时间: 2025-04-01 00:25:51
2025-04-01 01:25:54,745 - INFO - 定时任务运行中... 当前时间: 2025-04-01 01:25:54
2025-04-01 02:25:57,756 - INFO - 定时任务运行中... 当前时间: 2025-04-01 02:25:57
2025-04-01 03:26:00,300 - INFO - 定时任务运行中... 当前时间: 2025-04-01 03:26:00
2025-04-01 04:26:03,455 - INFO - 定时任务运行中... 当前时间: 2025-04-01 04:26:03
2025-04-01 05:26:06,246 - INFO - 定时任务运行中... 当前时间: 2025-04-01 05:26:06
2025-04-01 06:26:09,440 - INFO - 定时任务运行中... 当前时间: 2025-04-01 06:26:09
2025-04-01 07:26:12,017 - INFO - 定时任务运行中... 当前时间: 2025-04-01 07:26:12
2025-04-01 08:00:13,649 - INFO - 开始录制任务执行
2025-04-01 08:00:13,650 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-01 08:00:13,650 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-04-01 08:00:13,650 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-01 08:00:13,655 - INFO - 响应状态码: 200
2025-04-01 08:00:13,655 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-01 08:00:13,655 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-01 08:00:13,655 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-01 08:00:13,659 - INFO - 响应状态码: 200
2025-04-01 08:00:13,659 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-01 08:00:13,659 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-01 08:00:13,659 - INFO - 成功开始录制 0/2 个流
2025-04-01 08:26:14,864 - INFO - 定时任务运行中... 当前时间: 2025-04-01 08:26:14
2025-04-01 09:26:17,584 - INFO - 定时任务运行中... 当前时间: 2025-04-01 09:26:17
2025-04-01 10:26:20,621 - INFO - 定时任务运行中... 当前时间: 2025-04-01 10:26:20
2025-04-01 11:26:23,648 - INFO - 定时任务运行中... 当前时间: 2025-04-01 11:26:23
2025-04-01 12:26:26,442 - INFO - 定时任务运行中... 当前时间: 2025-04-01 12:26:26
2025-04-01 13:26:29,489 - INFO - 定时任务运行中... 当前时间: 2025-04-01 13:26:29
2025-04-01 14:26:32,162 - INFO - 定时任务运行中... 当前时间: 2025-04-01 14:26:32
2025-04-01 15:26:35,013 - INFO - 定时任务运行中... 当前时间: 2025-04-01 15:26:35
2025-04-01 16:26:38,069 - INFO - 定时任务运行中... 当前时间: 2025-04-01 16:26:38
2025-04-01 17:26:41,156 - INFO - 定时任务运行中... 当前时间: 2025-04-01 17:26:41
2025-04-01 18:00:43,090 - INFO - 停止录制任务执行
2025-04-01 18:00:43,090 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-01 18:00:43,090 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-04-01 18:00:43,090 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-01 18:00:43,095 - INFO - 响应状态码: 200
2025-04-01 18:00:43,095 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-01 18:00:43,095 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-01 18:00:43,096 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-01 18:00:43,100 - INFO - 响应状态码: 200
2025-04-01 18:00:43,100 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-01 18:00:43,100 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-01 18:00:43,100 - INFO - 成功停止录制 0/2 个流
2025-04-01 18:26:44,558 - INFO - 定时任务运行中... 当前时间: 2025-04-01 18:26:44
2025-04-01 19:26:47,693 - INFO - 定时任务运行中... 当前时间: 2025-04-01 19:26:47
2025-04-01 20:26:50,720 - INFO - 定时任务运行中... 当前时间: 2025-04-01 20:26:50
2025-04-01 21:26:53,396 - INFO - 定时任务运行中... 当前时间: 2025-04-01 21:26:53
2025-04-01 22:26:56,395 - INFO - 定时任务运行中... 当前时间: 2025-04-01 22:26:56
2025-04-01 23:26:59,066 - INFO - 定时任务运行中... 当前时间: 2025-04-01 23:26:59
2025-04-02 00:27:02,122 - INFO - 定时任务运行中... 当前时间: 2025-04-02 00:27:02
2025-04-02 01:27:04,802 - INFO - 定时任务运行中... 当前时间: 2025-04-02 01:27:04
2025-04-02 02:27:07,768 - INFO - 定时任务运行中... 当前时间: 2025-04-02 02:27:07
2025-04-02 03:27:10,585 - INFO - 定时任务运行中... 当前时间: 2025-04-02 03:27:10
2025-04-02 04:27:13,748 - INFO - 定时任务运行中... 当前时间: 2025-04-02 04:27:13
2025-04-02 05:27:16,589 - INFO - 定时任务运行中... 当前时间: 2025-04-02 05:27:16
2025-04-02 06:27:19,435 - INFO - 定时任务运行中... 当前时间: 2025-04-02 06:27:19
2025-04-02 07:27:22,178 - INFO - 定时任务运行中... 当前时间: 2025-04-02 07:27:22
2025-04-02 08:00:23,729 - INFO - 开始录制任务执行
2025-04-02 08:00:23,729 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-02 08:00:23,730 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-04-02 08:00:23,730 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-02 08:00:23,734 - INFO - 响应状态码: 200
2025-04-02 08:00:23,735 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-02 08:00:23,735 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-02 08:00:23,735 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-02 08:00:23,739 - INFO - 响应状态码: 200
2025-04-02 08:00:23,739 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-02 08:00:23,739 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-02 08:00:23,739 - INFO - 成功开始录制 0/2 个流
2025-04-02 08:27:24,960 - INFO - 定时任务运行中... 当前时间: 2025-04-02 08:27:24
2025-04-02 09:27:27,507 - INFO - 定时任务运行中... 当前时间: 2025-04-02 09:27:27
2025-04-02 10:27:30,510 - INFO - 定时任务运行中... 当前时间: 2025-04-02 10:27:30
2025-04-02 11:27:33,261 - INFO - 定时任务运行中... 当前时间: 2025-04-02 11:27:33
2025-04-02 12:27:36,492 - INFO - 定时任务运行中... 当前时间: 2025-04-02 12:27:36
2025-04-02 13:27:39,137 - INFO - 定时任务运行中... 当前时间: 2025-04-02 13:27:39
2025-04-02 14:27:41,929 - INFO - 定时任务运行中... 当前时间: 2025-04-02 14:27:41
2025-04-02 15:27:44,350 - INFO - 定时任务运行中... 当前时间: 2025-04-02 15:27:44
2025-04-02 16:27:47,239 - INFO - 定时任务运行中... 当前时间: 2025-04-02 16:27:47
2025-04-02 17:27:50,365 - INFO - 定时任务运行中... 当前时间: 2025-04-02 17:27:50
2025-04-02 18:00:51,825 - INFO - 停止录制任务执行
2025-04-02 18:00:51,826 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-02 18:00:51,826 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-04-02 18:00:51,826 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-02 18:00:51,829 - INFO - 响应状态码: 200
2025-04-02 18:00:51,829 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-02 18:00:51,830 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-02 18:00:51,830 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-02 18:00:51,833 - INFO - 响应状态码: 200
2025-04-02 18:00:51,833 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-02 18:00:51,833 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-02 18:00:51,833 - INFO - 成功停止录制 0/2 个流
2025-04-02 18:27:53,314 - INFO - 定时任务运行中... 当前时间: 2025-04-02 18:27:53
2025-04-02 19:27:56,380 - INFO - 定时任务运行中... 当前时间: 2025-04-02 19:27:56
2025-04-02 20:27:59,462 - INFO - 定时任务运行中... 当前时间: 2025-04-02 20:27:59
2025-04-02 21:28:02,480 - INFO - 定时任务运行中... 当前时间: 2025-04-02 21:28:02
2025-04-02 22:28:05,459 - INFO - 定时任务运行中... 当前时间: 2025-04-02 22:28:05
2025-04-02 23:28:08,282 - INFO - 定时任务运行中... 当前时间: 2025-04-02 23:28:08
2025-04-03 00:28:10,975 - INFO - 定时任务运行中... 当前时间: 2025-04-03 00:28:10
2025-04-03 01:28:13,995 - INFO - 定时任务运行中... 当前时间: 2025-04-03 01:28:13
2025-04-03 02:28:16,607 - INFO - 定时任务运行中... 当前时间: 2025-04-03 02:28:16
2025-04-03 03:28:19,021 - INFO - 定时任务运行中... 当前时间: 2025-04-03 03:28:19
2025-04-03 04:28:22,029 - INFO - 定时任务运行中... 当前时间: 2025-04-03 04:28:22
2025-04-03 05:28:24,358 - INFO - 定时任务运行中... 当前时间: 2025-04-03 05:28:24
2025-04-03 06:28:27,365 - INFO - 定时任务运行中... 当前时间: 2025-04-03 06:28:27
2025-04-03 07:28:30,038 - INFO - 定时任务运行中... 当前时间: 2025-04-03 07:28:30
2025-04-03 08:00:31,574 - INFO - 开始录制任务执行
2025-04-03 08:00:31,574 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-03 08:00:31,574 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-04-03 08:00:31,574 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-03 08:00:31,579 - INFO - 响应状态码: 200
2025-04-03 08:00:31,579 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-03 08:00:31,579 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-03 08:00:31,579 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-03 08:00:31,583 - INFO - 响应状态码: 200
2025-04-03 08:00:31,583 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-03 08:00:31,583 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-03 08:00:31,584 - INFO - 成功开始录制 0/2 个流
2025-04-03 08:28:32,970 - INFO - 定时任务运行中... 当前时间: 2025-04-03 08:28:32
2025-04-03 09:28:35,865 - INFO - 定时任务运行中... 当前时间: 2025-04-03 09:28:35
2025-04-03 10:28:38,648 - INFO - 定时任务运行中... 当前时间: 2025-04-03 10:28:38
2025-04-03 11:28:41,552 - INFO - 定时任务运行中... 当前时间: 2025-04-03 11:28:41
2025-04-03 12:28:44,492 - INFO - 定时任务运行中... 当前时间: 2025-04-03 12:28:44
2025-04-03 13:28:47,534 - INFO - 定时任务运行中... 当前时间: 2025-04-03 13:28:47
2025-04-03 14:28:50,474 - INFO - 定时任务运行中... 当前时间: 2025-04-03 14:28:50
2025-04-03 15:28:53,064 - INFO - 定时任务运行中... 当前时间: 2025-04-03 15:28:53
2025-04-03 16:28:56,081 - INFO - 定时任务运行中... 当前时间: 2025-04-03 16:28:56
2025-04-03 17:28:58,720 - INFO - 定时任务运行中... 当前时间: 2025-04-03 17:28:58
2025-04-03 18:00:00,354 - INFO - 停止录制任务执行
2025-04-03 18:00:00,354 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-03 18:00:00,354 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-04-03 18:00:00,355 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-03 18:00:00,359 - INFO - 响应状态码: 200
2025-04-03 18:00:00,359 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-03 18:00:00,359 - INFO - 服务器确认stopRecord操作成功
2025-04-03 18:00:00,360 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-03 18:00:00,364 - INFO - 响应状态码: 200
2025-04-03 18:00:00,364 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-03 18:00:00,364 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-03 18:00:00,364 - INFO - 成功停止录制 1/2 个流
2025-04-03 18:29:01,737 - INFO - 定时任务运行中... 当前时间: 2025-04-03 18:29:01
2025-04-03 19:29:04,948 - INFO - 定时任务运行中... 当前时间: 2025-04-03 19:29:04
2025-04-03 20:29:07,925 - INFO - 定时任务运行中... 当前时间: 2025-04-03 20:29:07
2025-04-03 21:29:10,755 - INFO - 定时任务运行中... 当前时间: 2025-04-03 21:29:10
2025-04-03 22:29:13,924 - INFO - 定时任务运行中... 当前时间: 2025-04-03 22:29:13
2025-04-03 23:29:16,693 - INFO - 定时任务运行中... 当前时间: 2025-04-03 23:29:16
2025-04-04 00:29:19,801 - INFO - 定时任务运行中... 当前时间: 2025-04-04 00:29:19
2025-04-04 01:29:22,368 - INFO - 定时任务运行中... 当前时间: 2025-04-04 01:29:22
2025-04-04 02:29:25,374 - INFO - 定时任务运行中... 当前时间: 2025-04-04 02:29:25
2025-04-04 03:29:28,457 - INFO - 定时任务运行中... 当前时间: 2025-04-04 03:29:28
2025-04-04 04:29:31,596 - INFO - 定时任务运行中... 当前时间: 2025-04-04 04:29:31
2025-04-04 05:29:34,528 - INFO - 定时任务运行中... 当前时间: 2025-04-04 05:29:34
2025-04-04 06:29:37,380 - INFO - 定时任务运行中... 当前时间: 2025-04-04 06:29:37
2025-04-04 07:29:40,333 - INFO - 定时任务运行中... 当前时间: 2025-04-04 07:29:40
2025-04-04 08:00:41,752 - INFO - 开始录制任务执行
2025-04-04 08:00:41,753 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-04 08:00:41,753 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-04-04 08:00:41,753 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-04 08:00:41,758 - INFO - 响应状态码: 200
2025-04-04 08:00:41,758 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-04 08:00:41,758 - INFO - 服务器确认startRecord操作成功
2025-04-04 08:00:41,758 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-04 08:00:41,762 - INFO - 响应状态码: 200
2025-04-04 08:00:41,762 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-04 08:00:41,762 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-04 08:00:41,763 - INFO - 成功开始录制 1/2 个流
2025-04-04 08:29:43,292 - INFO - 定时任务运行中... 当前时间: 2025-04-04 08:29:43
2025-04-04 09:29:46,026 - INFO - 定时任务运行中... 当前时间: 2025-04-04 09:29:46
2025-04-04 10:29:49,131 - INFO - 定时任务运行中... 当前时间: 2025-04-04 10:29:49
2025-04-04 11:29:51,998 - INFO - 定时任务运行中... 当前时间: 2025-04-04 11:29:51
2025-04-04 12:29:54,874 - INFO - 定时任务运行中... 当前时间: 2025-04-04 12:29:54
2025-04-04 13:29:57,965 - INFO - 定时任务运行中... 当前时间: 2025-04-04 13:29:57
2025-04-04 14:30:01,323 - INFO - 定时任务运行中... 当前时间: 2025-04-04 14:30:01
2025-04-04 15:30:04,347 - INFO - 定时任务运行中... 当前时间: 2025-04-04 15:30:04
2025-04-04 16:30:07,536 - INFO - 定时任务运行中... 当前时间: 2025-04-04 16:30:07
2025-04-04 17:30:10,371 - INFO - 定时任务运行中... 当前时间: 2025-04-04 17:30:10
2025-04-04 18:00:11,758 - INFO - 停止录制任务执行
2025-04-04 18:00:11,758 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-04 18:00:11,758 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-04-04 18:00:11,758 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-04 18:00:11,763 - INFO - 响应状态码: 200
2025-04-04 18:00:11,763 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-04 18:00:11,763 - INFO - 服务器确认stopRecord操作成功
2025-04-04 18:00:11,764 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-04 18:00:11,768 - INFO - 响应状态码: 200
2025-04-04 18:00:11,768 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-04 18:00:11,768 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-04 18:00:11,768 - INFO - 成功停止录制 1/2 个流
2025-04-04 18:30:13,201 - INFO - 定时任务运行中... 当前时间: 2025-04-04 18:30:13
2025-04-04 19:30:16,177 - INFO - 定时任务运行中... 当前时间: 2025-04-04 19:30:16
2025-04-04 20:30:19,065 - INFO - 定时任务运行中... 当前时间: 2025-04-04 20:30:19
2025-04-04 21:30:22,048 - INFO - 定时任务运行中... 当前时间: 2025-04-04 21:30:22
2025-04-04 22:30:24,952 - INFO - 定时任务运行中... 当前时间: 2025-04-04 22:30:24
2025-04-04 23:30:28,067 - INFO - 定时任务运行中... 当前时间: 2025-04-04 23:30:28
2025-04-05 00:30:31,187 - INFO - 定时任务运行中... 当前时间: 2025-04-05 00:30:31
2025-04-05 01:30:34,035 - INFO - 定时任务运行中... 当前时间: 2025-04-05 01:30:34
2025-04-05 02:30:36,833 - INFO - 定时任务运行中... 当前时间: 2025-04-05 02:30:36
2025-04-05 03:30:39,856 - INFO - 定时任务运行中... 当前时间: 2025-04-05 03:30:39
2025-04-05 04:30:42,714 - INFO - 定时任务运行中... 当前时间: 2025-04-05 04:30:42
2025-04-05 05:30:45,692 - INFO - 定时任务运行中... 当前时间: 2025-04-05 05:30:45
2025-04-05 06:30:48,736 - INFO - 定时任务运行中... 当前时间: 2025-04-05 06:30:48
2025-04-05 07:30:51,810 - INFO - 定时任务运行中... 当前时间: 2025-04-05 07:30:51
2025-04-05 08:00:53,383 - INFO - 开始录制任务执行
2025-04-05 08:00:53,384 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-05 08:00:53,384 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-04-05 08:00:53,384 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-05 08:00:53,389 - INFO - 响应状态码: 200
2025-04-05 08:00:53,389 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-05 08:00:53,389 - INFO - 服务器确认startRecord操作成功
2025-04-05 08:00:53,389 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-05 08:00:53,393 - INFO - 响应状态码: 200
2025-04-05 08:00:53,394 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-05 08:00:53,394 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-05 08:00:53,394 - INFO - 成功开始录制 1/2 个流
2025-04-05 08:30:55,066 - INFO - 定时任务运行中... 当前时间: 2025-04-05 08:30:55
2025-04-05 09:30:58,202 - INFO - 定时任务运行中... 当前时间: 2025-04-05 09:30:58
2025-04-05 10:31:01,047 - INFO - 定时任务运行中... 当前时间: 2025-04-05 10:31:01
2025-04-05 11:31:04,052 - INFO - 定时任务运行中... 当前时间: 2025-04-05 11:31:04
2025-04-05 12:31:06,935 - INFO - 定时任务运行中... 当前时间: 2025-04-05 12:31:06
2025-04-05 13:31:10,122 - INFO - 定时任务运行中... 当前时间: 2025-04-05 13:31:10
2025-04-05 14:31:13,054 - INFO - 定时任务运行中... 当前时间: 2025-04-05 14:31:13
2025-04-05 15:31:16,105 - INFO - 定时任务运行中... 当前时间: 2025-04-05 15:31:16
2025-04-05 16:31:19,174 - INFO - 定时任务运行中... 当前时间: 2025-04-05 16:31:19
2025-04-05 17:31:21,916 - INFO - 定时任务运行中... 当前时间: 2025-04-05 17:31:21
2025-04-05 18:00:23,059 - INFO - 停止录制任务执行
2025-04-05 18:00:23,060 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-05 18:00:23,060 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-04-05 18:00:23,060 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-05 18:00:23,065 - INFO - 响应状态码: 200
2025-04-05 18:00:23,065 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-05 18:00:23,065 - INFO - 服务器确认stopRecord操作成功
2025-04-05 18:00:23,065 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-05 18:00:23,069 - INFO - 响应状态码: 200
2025-04-05 18:00:23,069 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-05 18:00:23,070 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-05 18:00:23,070 - INFO - 成功停止录制 1/2 个流
2025-04-05 18:31:24,489 - INFO - 定时任务运行中... 当前时间: 2025-04-05 18:31:24
2025-04-05 19:31:27,373 - INFO - 定时任务运行中... 当前时间: 2025-04-05 19:31:27
2025-04-05 20:31:30,364 - INFO - 定时任务运行中... 当前时间: 2025-04-05 20:31:30
2025-04-05 21:31:33,532 - INFO - 定时任务运行中... 当前时间: 2025-04-05 21:31:33
2025-04-05 22:31:36,372 - INFO - 定时任务运行中... 当前时间: 2025-04-05 22:31:36
2025-04-05 23:31:39,388 - INFO - 定时任务运行中... 当前时间: 2025-04-05 23:31:39
2025-04-06 00:31:42,573 - INFO - 定时任务运行中... 当前时间: 2025-04-06 00:31:42
2025-04-06 01:31:45,473 - INFO - 定时任务运行中... 当前时间: 2025-04-06 01:31:45
2025-04-06 02:31:48,567 - INFO - 定时任务运行中... 当前时间: 2025-04-06 02:31:48
2025-04-06 03:31:51,618 - INFO - 定时任务运行中... 当前时间: 2025-04-06 03:31:51
2025-04-06 04:31:54,666 - INFO - 定时任务运行中... 当前时间: 2025-04-06 04:31:54
2025-04-06 05:31:57,375 - INFO - 定时任务运行中... 当前时间: 2025-04-06 05:31:57
2025-04-06 06:32:00,014 - INFO - 定时任务运行中... 当前时间: 2025-04-06 06:32:00
2025-04-06 07:32:03,230 - INFO - 定时任务运行中... 当前时间: 2025-04-06 07:32:03
2025-04-06 08:00:04,479 - INFO - 开始录制任务执行
2025-04-06 08:00:04,480 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-06 08:00:04,480 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-04-06 08:00:04,480 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-06 08:00:04,485 - INFO - 响应状态码: 200
2025-04-06 08:00:04,485 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-06 08:00:04,485 - INFO - 服务器确认startRecord操作成功
2025-04-06 08:00:04,485 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-06 08:00:04,489 - INFO - 响应状态码: 200
2025-04-06 08:00:04,490 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-06 08:00:04,490 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-06 08:00:04,490 - INFO - 成功开始录制 1/2 个流
2025-04-06 08:32:05,896 - INFO - 定时任务运行中... 当前时间: 2025-04-06 08:32:05
2025-04-06 09:32:08,814 - INFO - 定时任务运行中... 当前时间: 2025-04-06 09:32:08
2025-04-06 10:32:11,565 - INFO - 定时任务运行中... 当前时间: 2025-04-06 10:32:11
2025-04-06 11:32:14,542 - INFO - 定时任务运行中... 当前时间: 2025-04-06 11:32:14
2025-04-06 12:32:17,253 - INFO - 定时任务运行中... 当前时间: 2025-04-06 12:32:17
2025-04-06 13:32:20,266 - INFO - 定时任务运行中... 当前时间: 2025-04-06 13:32:20
2025-04-06 14:32:22,966 - INFO - 定时任务运行中... 当前时间: 2025-04-06 14:32:22
2025-04-06 15:32:25,611 - INFO - 定时任务运行中... 当前时间: 2025-04-06 15:32:25
2025-04-06 16:32:28,401 - INFO - 定时任务运行中... 当前时间: 2025-04-06 16:32:28
2025-04-06 17:32:31,294 - INFO - 定时任务运行中... 当前时间: 2025-04-06 17:32:31
2025-04-06 18:00:32,595 - INFO - 停止录制任务执行
2025-04-06 18:00:32,595 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-06 18:00:32,595 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-04-06 18:00:32,595 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-06 18:00:32,600 - INFO - 响应状态码: 200
2025-04-06 18:00:32,600 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-06 18:00:32,601 - INFO - 服务器确认stopRecord操作成功
2025-04-06 18:00:32,601 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-06 18:00:32,605 - INFO - 响应状态码: 200
2025-04-06 18:00:32,605 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-06 18:00:32,605 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-06 18:00:32,606 - INFO - 成功停止录制 1/2 个流
2025-04-06 18:32:34,281 - INFO - 定时任务运行中... 当前时间: 2025-04-06 18:32:34
2025-04-06 19:32:37,482 - INFO - 定时任务运行中... 当前时间: 2025-04-06 19:32:37
2025-04-06 20:32:40,560 - INFO - 定时任务运行中... 当前时间: 2025-04-06 20:32:40
2025-04-06 21:32:43,693 - INFO - 定时任务运行中... 当前时间: 2025-04-06 21:32:43
2025-04-06 22:32:46,701 - INFO - 定时任务运行中... 当前时间: 2025-04-06 22:32:46
2025-04-06 23:32:49,746 - INFO - 定时任务运行中... 当前时间: 2025-04-06 23:32:49
2025-04-07 00:32:52,379 - INFO - 定时任务运行中... 当前时间: 2025-04-07 00:32:52
2025-04-07 01:32:55,482 - INFO - 定时任务运行中... 当前时间: 2025-04-07 01:32:55
2025-04-07 02:32:58,312 - INFO - 定时任务运行中... 当前时间: 2025-04-07 02:32:58
2025-04-07 03:33:01,017 - INFO - 定时任务运行中... 当前时间: 2025-04-07 03:33:01
2025-04-07 04:33:04,082 - INFO - 定时任务运行中... 当前时间: 2025-04-07 04:33:04
2025-04-07 05:33:06,874 - INFO - 定时任务运行中... 当前时间: 2025-04-07 05:33:06
2025-04-07 06:33:09,577 - INFO - 定时任务运行中... 当前时间: 2025-04-07 06:33:09
2025-04-07 07:33:12,307 - INFO - 定时任务运行中... 当前时间: 2025-04-07 07:33:12
2025-04-07 08:00:13,614 - INFO - 开始录制任务执行
2025-04-07 08:00:13,614 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-07 08:00:13,614 - INFO - 成功加载 2 个推流码: ['f2ya', 'efy1a']
2025-04-07 08:00:13,615 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-07 08:00:13,619 - INFO - 响应状态码: 200
2025-04-07 08:00:13,619 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-07 08:00:13,620 - INFO - 服务器确认startRecord操作成功
2025-04-07 08:00:13,620 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-07 08:00:13,624 - INFO - 响应状态码: 200
2025-04-07 08:00:13,624 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-07 08:00:13,624 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-07 08:00:13,624 - INFO - 成功开始录制 1/2 个流
2025-04-07 08:33:15,249 - INFO - 定时任务运行中... 当前时间: 2025-04-07 08:33:15
2025-04-07 09:33:17,950 - INFO - 定时任务运行中... 当前时间: 2025-04-07 09:33:17
2025-04-07 09:43:03,694 - INFO - 停止录制任务执行
2025-04-07 09:43:03,694 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-07 09:43:03,694 - INFO - 成功加载 3 个推流码: ['f2ya', 'efy1a', 'f2yb']
2025-04-07 09:43:03,694 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-07 09:43:03,697 - INFO - 响应状态码: 200
2025-04-07 09:43:03,697 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-07 09:43:03,698 - INFO - 服务器确认stopRecord操作成功
2025-04-07 09:43:03,698 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-07 09:43:03,700 - INFO - 响应状态码: 200
2025-04-07 09:43:03,700 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-07 09:43:03,700 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-07 09:43:03,700 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2yb
2025-04-07 09:43:03,703 - INFO - 响应状态码: 200
2025-04-07 09:43:03,703 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-07 09:43:03,703 - INFO - 服务器确认stopRecord操作成功
2025-04-07 09:43:03,703 - INFO - 成功停止录制 2/3 个流
2025-04-07 09:43:07,687 - INFO - 自动录制脚本启动
2025-04-07 09:43:07,687 - INFO - 脚本路径: /data/AudioSeparation/hours_recordings/auto_record.py
2025-04-07 09:43:07,687 - INFO - 当前系统时间: 2025-04-07 09:43:07
2025-04-07 09:43:07,688 - INFO - 定时任务已设置：每天 08:00 开始录制，18:00 停止录制
2025-04-07 09:43:07,688 - INFO - 脚本启动时间在8点至18点之间（当前9点），立即执行开始录制
2025-04-07 09:43:07,688 - INFO - 开始录制任务执行
2025-04-07 09:43:07,688 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-07 09:43:07,688 - INFO - 成功加载 3 个推流码: ['f2ya', 'efy1a', 'f2yb']
2025-04-07 09:43:07,688 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-07 09:43:07,692 - INFO - 响应状态码: 200
2025-04-07 09:43:07,692 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-07 09:43:07,692 - INFO - 服务器确认startRecord操作成功
2025-04-07 09:43:07,692 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-07 09:43:07,695 - INFO - 响应状态码: 200
2025-04-07 09:43:07,695 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-07 09:43:07,695 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-07 09:43:07,695 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2yb
2025-04-07 09:43:07,697 - INFO - 响应状态码: 200
2025-04-07 09:43:07,698 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-07 09:43:07,698 - INFO - 服务器确认startRecord操作成功
2025-04-07 09:43:07,698 - INFO - 成功开始录制 2/3 个流
2025-04-07 09:43:07,698 - INFO - 定时任务运行中... 当前时间: 2025-04-07 09:43:07
2025-04-07 10:43:10,704 - INFO - 定时任务运行中... 当前时间: 2025-04-07 10:43:10
2025-04-07 11:43:13,580 - INFO - 定时任务运行中... 当前时间: 2025-04-07 11:43:13
2025-04-07 12:43:16,414 - INFO - 定时任务运行中... 当前时间: 2025-04-07 12:43:16
2025-04-07 13:43:19,195 - INFO - 定时任务运行中... 当前时间: 2025-04-07 13:43:19
2025-04-07 14:43:21,905 - INFO - 定时任务运行中... 当前时间: 2025-04-07 14:43:21
2025-04-07 15:43:24,901 - INFO - 定时任务运行中... 当前时间: 2025-04-07 15:43:24
2025-04-07 16:43:27,881 - INFO - 定时任务运行中... 当前时间: 2025-04-07 16:43:27
2025-04-07 17:43:30,624 - INFO - 定时任务运行中... 当前时间: 2025-04-07 17:43:30
2025-04-07 18:00:31,392 - INFO - 停止录制任务执行
2025-04-07 18:00:31,392 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-07 18:00:31,392 - INFO - 成功加载 3 个推流码: ['f2ya', 'efy1a', 'f2yb']
2025-04-07 18:00:31,392 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-07 18:00:31,397 - INFO - 响应状态码: 200
2025-04-07 18:00:31,397 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-07 18:00:31,397 - INFO - 服务器确认stopRecord操作成功
2025-04-07 18:00:31,398 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-07 18:00:31,402 - INFO - 响应状态码: 200
2025-04-07 18:00:31,402 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-07 18:00:31,402 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-07 18:00:31,402 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2yb
2025-04-07 18:00:31,406 - INFO - 响应状态码: 200
2025-04-07 18:00:31,406 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-07 18:00:31,406 - INFO - 服务器确认stopRecord操作成功
2025-04-07 18:00:31,406 - INFO - 成功停止录制 2/3 个流
2025-04-07 18:43:33,425 - INFO - 定时任务运行中... 当前时间: 2025-04-07 18:43:33
2025-04-07 19:43:36,284 - INFO - 定时任务运行中... 当前时间: 2025-04-07 19:43:36
2025-04-07 20:43:39,233 - INFO - 定时任务运行中... 当前时间: 2025-04-07 20:43:39
2025-04-07 21:43:42,289 - INFO - 定时任务运行中... 当前时间: 2025-04-07 21:43:42
2025-04-07 22:43:45,165 - INFO - 定时任务运行中... 当前时间: 2025-04-07 22:43:45
2025-04-07 23:43:48,158 - INFO - 定时任务运行中... 当前时间: 2025-04-07 23:43:48
2025-04-08 00:43:51,066 - INFO - 定时任务运行中... 当前时间: 2025-04-08 00:43:51
2025-04-08 01:43:53,964 - INFO - 定时任务运行中... 当前时间: 2025-04-08 01:43:53
2025-04-08 02:43:56,868 - INFO - 定时任务运行中... 当前时间: 2025-04-08 02:43:56
2025-04-08 03:43:59,803 - INFO - 定时任务运行中... 当前时间: 2025-04-08 03:43:59
2025-04-08 04:44:02,760 - INFO - 定时任务运行中... 当前时间: 2025-04-08 04:44:02
2025-04-08 05:44:05,734 - INFO - 定时任务运行中... 当前时间: 2025-04-08 05:44:05
2025-04-08 06:44:08,767 - INFO - 定时任务运行中... 当前时间: 2025-04-08 06:44:08
2025-04-08 07:44:11,796 - INFO - 定时任务运行中... 当前时间: 2025-04-08 07:44:11
2025-04-08 08:00:12,548 - INFO - 开始录制任务执行
2025-04-08 08:00:12,549 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-08 08:00:12,549 - INFO - 成功加载 3 个推流码: ['f2ya', 'efy1a', 'f2yb']
2025-04-08 08:00:12,549 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-08 08:00:12,551 - INFO - 响应状态码: 200
2025-04-08 08:00:12,551 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-08 08:00:12,551 - INFO - 服务器确认startRecord操作成功
2025-04-08 08:00:12,551 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-08 08:00:12,553 - INFO - 响应状态码: 200
2025-04-08 08:00:12,553 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-08 08:00:12,553 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-08 08:00:12,554 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2yb
2025-04-08 08:00:12,556 - INFO - 响应状态码: 200
2025-04-08 08:00:12,556 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-08 08:00:12,556 - INFO - 服务器确认startRecord操作成功
2025-04-08 08:00:12,556 - INFO - 成功开始录制 2/3 个流
2025-04-08 08:44:14,370 - INFO - 定时任务运行中... 当前时间: 2025-04-08 08:44:14
2025-04-08 09:44:17,461 - INFO - 定时任务运行中... 当前时间: 2025-04-08 09:44:17
2025-04-08 10:44:20,336 - INFO - 定时任务运行中... 当前时间: 2025-04-08 10:44:20
2025-04-08 11:44:23,407 - INFO - 定时任务运行中... 当前时间: 2025-04-08 11:44:23
2025-04-08 12:44:26,228 - INFO - 定时任务运行中... 当前时间: 2025-04-08 12:44:26
2025-04-08 13:44:29,214 - INFO - 定时任务运行中... 当前时间: 2025-04-08 13:44:29
2025-04-08 14:44:32,311 - INFO - 定时任务运行中... 当前时间: 2025-04-08 14:44:32
2025-04-08 15:44:35,249 - INFO - 定时任务运行中... 当前时间: 2025-04-08 15:44:35
2025-04-08 16:44:38,153 - INFO - 定时任务运行中... 当前时间: 2025-04-08 16:44:38
2025-04-08 17:44:40,729 - INFO - 定时任务运行中... 当前时间: 2025-04-08 17:44:40
2025-04-08 18:00:41,463 - INFO - 停止录制任务执行
2025-04-08 18:00:41,463 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-08 18:00:41,463 - INFO - 成功加载 3 个推流码: ['f2ya', 'efy1a', 'f2yb']
2025-04-08 18:00:41,463 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-08 18:00:41,468 - INFO - 响应状态码: 200
2025-04-08 18:00:41,468 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-08 18:00:41,469 - INFO - 服务器确认stopRecord操作成功
2025-04-08 18:00:41,469 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-08 18:00:41,473 - INFO - 响应状态码: 200
2025-04-08 18:00:41,473 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-08 18:00:41,473 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-08 18:00:41,473 - INFO - 发送请求: http://*************:8999/index/api/stopRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2yb
2025-04-08 18:00:41,477 - INFO - 响应状态码: 200
2025-04-08 18:00:41,477 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-08 18:00:41,477 - INFO - 服务器确认stopRecord操作成功
2025-04-08 18:00:41,477 - INFO - 成功停止录制 2/3 个流
2025-04-08 18:44:43,590 - INFO - 定时任务运行中... 当前时间: 2025-04-08 18:44:43
2025-04-08 19:44:46,607 - INFO - 定时任务运行中... 当前时间: 2025-04-08 19:44:46
2025-04-08 20:44:49,741 - INFO - 定时任务运行中... 当前时间: 2025-04-08 20:44:49
2025-04-08 21:44:52,494 - INFO - 定时任务运行中... 当前时间: 2025-04-08 21:44:52
2025-04-08 22:44:55,720 - INFO - 定时任务运行中... 当前时间: 2025-04-08 22:44:55
2025-04-08 23:44:58,453 - INFO - 定时任务运行中... 当前时间: 2025-04-08 23:44:58
2025-04-09 00:45:00,951 - INFO - 定时任务运行中... 当前时间: 2025-04-09 00:45:00
2025-04-09 01:45:03,965 - INFO - 定时任务运行中... 当前时间: 2025-04-09 01:45:03
2025-04-09 02:45:06,709 - INFO - 定时任务运行中... 当前时间: 2025-04-09 02:45:06
2025-04-09 03:45:09,916 - INFO - 定时任务运行中... 当前时间: 2025-04-09 03:45:09
2025-04-09 04:45:12,832 - INFO - 定时任务运行中... 当前时间: 2025-04-09 04:45:12
2025-04-09 05:45:15,405 - INFO - 定时任务运行中... 当前时间: 2025-04-09 05:45:15
2025-04-09 06:45:18,222 - INFO - 定时任务运行中... 当前时间: 2025-04-09 06:45:18
2025-04-09 07:45:21,060 - INFO - 定时任务运行中... 当前时间: 2025-04-09 07:45:21
2025-04-09 08:00:21,852 - INFO - 开始录制任务执行
2025-04-09 08:00:21,852 - INFO - 从 /data/AudioSeparation/hours_recordings/stream.json 加载推流码
2025-04-09 08:00:21,852 - INFO - 成功加载 3 个推流码: ['f2ya', 'efy1a', 'f2yb']
2025-04-09 08:00:21,852 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2ya
2025-04-09 08:00:21,875 - INFO - 响应状态码: 200
2025-04-09 08:00:21,875 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-09 08:00:21,875 - INFO - 服务器确认startRecord操作成功
2025-04-09 08:00:21,875 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=efy1a
2025-04-09 08:00:21,877 - INFO - 响应状态码: 200
2025-04-09 08:00:21,878 - INFO - 响应内容: {
	"code" : -500,
	"msg" : "can not find the stream"
}

2025-04-09 08:00:21,878 - ERROR - 服务器返回错误: -500 - can not find the stream
2025-04-09 08:00:21,878 - INFO - 发送请求: http://*************:8999/index/api/startRecord?secret=z500PrnbB66d1txYMDyTVqHn4qFA41g8&type=1&vhost=__defaultVhost__&app=live&stream=f2yb
2025-04-09 08:00:21,880 - INFO - 响应状态码: 200
2025-04-09 08:00:21,880 - INFO - 响应内容: {
	"code" : 0,
	"msg" : "success",
	"result" : true
}

2025-04-09 08:00:21,880 - INFO - 服务器确认startRecord操作成功
2025-04-09 08:00:21,880 - INFO - 成功开始录制 2/3 个流
2025-04-09 08:45:23,851 - INFO - 定时任务运行中... 当前时间: 2025-04-09 08:45:23
2025-04-09 09:45:26,377 - INFO - 定时任务运行中... 当前时间: 2025-04-09 09:45:26
2025-04-09 10:45:29,029 - INFO - 定时任务运行中... 当前时间: 2025-04-09 10:45:29
2025-04-09 11:45:31,621 - INFO - 定时任务运行中... 当前时间: 2025-04-09 11:45:31
2025-04-09 12:45:34,257 - INFO - 定时任务运行中... 当前时间: 2025-04-09 12:45:34
2025-04-09 13:45:37,022 - INFO - 定时任务运行中... 当前时间: 2025-04-09 13:45:37
2025-04-09 14:45:39,732 - INFO - 定时任务运行中... 当前时间: 2025-04-09 14:45:39
2025-04-09 15:45:42,582 - INFO - 定时任务运行中... 当前时间: 2025-04-09 15:45:42
