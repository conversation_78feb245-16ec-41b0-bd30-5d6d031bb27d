#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import logging
import os
import time
from datetime import datetime

import requests
import schedule

# 配置日志
log_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(log_dir, "debug_auto_record.log")
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file, encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# API相关参数
API_BASE_URL = "http://*************:8999/index/api"
API_PARAMS = {
    "secret": "z500PrnbB66d1txYMDyTVqHn4qFA41g8",
    "type": "1",
    "vhost": "__defaultVhost__",
    "app": "live"
}


def load_streams():
    """从stream.json文件加载推流码"""
    try:
        json_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stream.json")
        logger.info(f"尝试从 {json_path} 加载推流码")
        
        with open(json_path, "r", encoding="utf-8") as f:
            data = json.load(f)
            
        if not data or not isinstance(data, list):
            logger.error(f"无效的stream.json格式: {data}")
            return []
            
        streams = [item.get("stream") for item in data if item.get("stream")]
        logger.info(f"成功加载 {len(streams)} 个推流码: {streams}")
        return streams
    except FileNotFoundError:
        logger.error(f"stream.json文件不存在: {json_path}")
        return []
    except json.JSONDecodeError as e:
        logger.error(f"无法解析stream.json: {e}")
        return []
    except Exception as e:
        logger.error(f"加载推流码失败: {e}")
        return []


def send_api_request(action, stream):
    """发送API请求并详细记录结果"""
    params = API_PARAMS.copy()
    params["stream"] = stream
    
    url = f"{API_BASE_URL}/{action}"
    logger.info(f"发送{action}请求: {url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}")
    
    try:
        response = requests.get(url, params=params, timeout=10)
        logger.info(f"{action}响应状态码: {response.status_code}")
        logger.info(f"{action}响应内容: {response.text}")
        
        if response.status_code == 200:
            # 尝试解析JSON响应
            try:
                result = response.json()
                if result.get("code") == 0:
                    logger.info(f"服务器确认{action}成功")
                    return True
                else:
                    logger.error(f"服务器返回错误码: {result.get('code')}, 消息: {result.get('msg', '无消息')}")
                    return False
            except json.JSONDecodeError:
                logger.warning(f"无法解析服务器响应为JSON格式: {response.text}")
                # 如果无法解析但状态码是200，仍然认为可能成功
                return True
        else:
            logger.error(f"{action}失败: HTTP状态码 {response.status_code}")
            return False
    except requests.RequestException as e:
        logger.error(f"发送{action}请求时发生网络错误: {e}")
        return False
    except Exception as e:
        logger.error(f"发送{action}请求时发生未知错误: {e}")
        return False


def start_recording():
    """开始录制所有流"""
    logger.info("开始录制任务执行")
    streams = load_streams()
    
    if not streams:
        logger.error("没有找到推流码，无法开始录制")
        return False
    
    success_count = 0
    for stream in streams:
        if send_api_request("startRecord", stream):
            logger.info(f"成功开始录制流: {stream}")
            success_count += 1
        else:
            logger.error(f"开始录制流 {stream} 失败")
    
    if success_count > 0:
        logger.info(f"共成功开始录制 {success_count}/{len(streams)} 个流")
        return True
    else:
        logger.error("没有任何流成功开始录制")
        return False


def stop_recording():
    """停止录制所有流"""
    logger.info("停止录制任务执行")
    streams = load_streams()
    
    if not streams:
        logger.error("没有找到推流码，无法停止录制")
        return False
    
    success_count = 0
    for stream in streams:
        if send_api_request("stopRecord", stream):
            logger.info(f"成功停止录制流: {stream}")
            success_count += 1
        else:
            logger.error(f"停止录制流 {stream} 失败")
    
    if success_count > 0:
        logger.info(f"共成功停止录制 {success_count}/{len(streams)} 个流")
        return True
    else:
        logger.error("没有任何流成功停止录制")
        return False


def check_system_time():
    """检查系统时间是否正确"""
    current_time = datetime.now()
    logger.info(f"当前系统时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    return current_time


def main():
    logger.info("===== 调试版自动录制脚本启动 =====")
    logger.info(f"脚本路径: {os.path.abspath(__file__)}")
    
    # 检查系统时间
    current_time = check_system_time()
    
    # 设置定时任务
    schedule.every().day.at("08:00").do(start_recording)
    schedule.every().day.at("18:00").do(stop_recording)
    
    logger.info("定时任务已设置：每天 08:00 开始录制，18:00 停止录制")
    
    # 根据当前时间执行一次相应操作
    current_hour = current_time.hour
    if 8 <= current_hour < 18:
        logger.info(f"脚本启动时间在8点至18点之间（当前{current_hour}点），执行开始录制")
        start_recording()
    else:
        logger.info(f"脚本启动时间在非工作时间（当前{current_hour}点），不执行录制")
    
    # 运行循环
    logger.info("开始定时任务循环...")
    iteration = 0
    
    while True:
        try:
            # 每小时记录一次当前状态
            if iteration % 60 == 0:
                logger.info(f"定时任务运行中... 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
            iteration += 1
        except Exception as e:
            logger.error(f"运行定时任务时发生错误: {e}")
            time.sleep(60)  # 发生错误后等待一分钟再继续


if __name__ == "__main__":
    main() 