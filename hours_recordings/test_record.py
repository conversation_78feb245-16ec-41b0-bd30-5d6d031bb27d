#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import logging
import os
import time
from datetime import datetime

import requests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# API相关参数
API_BASE_URL = "http://*************:8999/index/api"
API_PARAMS = {
    "secret": "z500PrnbB66d1txYMDyTVqHn4qFA41g8",
    "type": "1",
    "vhost": "__defaultVhost__",
    "app": "live"
}

# 测试用的流名称
TEST_STREAM = "f2ya"
# 录制时长（秒）
RECORD_DURATION = 10


def test_recording():
    """测试10秒钟的录制"""
    params = API_PARAMS.copy()
    params["stream"] = TEST_STREAM
    
    # 开始录制
    logger.info(f"开始测试录制流 {TEST_STREAM}，持续 {RECORD_DURATION} 秒")
    start_url = f"{API_BASE_URL}/startRecord"
    
    try:
        logger.info(f"发送开始录制请求: {start_url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}")
        response = requests.get(start_url, params=params, timeout=10)
        
        # 输出完整的响应内容
        logger.info(f"开始录制响应状态码: {response.status_code}")
        logger.info(f"开始录制响应内容: {response.text}")
        
        if response.status_code == 200:
            logger.info(f"成功启动录制流 {TEST_STREAM}")
            
            # 尝试解析JSON响应
            try:
                result = response.json()
                if result.get("code") == 0:
                    logger.info("服务器确认录制已开始")
                else:
                    logger.warning(f"服务器返回错误码: {result.get('code')}, 消息: {result.get('msg', '无消息')}")
            except json.JSONDecodeError:
                logger.warning("无法解析服务器响应为JSON格式")
        else:
            logger.error(f"启动录制失败: HTTP状态码 {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"发送开始录制请求时发生错误: {e}")
        return False
    
    # 等待指定的录制时间
    logger.info(f"等待 {RECORD_DURATION} 秒...")
    for i in range(RECORD_DURATION):
        time.sleep(1)
        if (i + 1) % 5 == 0 or i == 0:  # 每5秒或第一秒时显示进度
            logger.info(f"已录制 {i + 1} 秒，剩余 {RECORD_DURATION - i - 1} 秒")
    
    # 停止录制
    stop_url = f"{API_BASE_URL}/stopRecord"
    
    try:
        logger.info(f"发送停止录制请求: {stop_url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}")
        response = requests.get(stop_url, params=params, timeout=10)
        
        # 输出完整的响应内容
        logger.info(f"停止录制响应状态码: {response.status_code}")
        logger.info(f"停止录制响应内容: {response.text}")
        
        if response.status_code == 200:
            logger.info(f"成功停止录制流 {TEST_STREAM}")
            
            # 尝试解析JSON响应
            try:
                result = response.json()
                if result.get("code") == 0:
                    logger.info("服务器确认录制已停止")
                else:
                    logger.warning(f"服务器返回错误码: {result.get('code')}, 消息: {result.get('msg', '无消息')}")
            except json.JSONDecodeError:
                logger.warning("无法解析服务器响应为JSON格式")
            
            return True
        else:
            logger.error(f"停止录制失败: HTTP状态码 {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"发送停止录制请求时发生错误: {e}")
        return False


if __name__ == "__main__":
    logger.info("=== 开始录制测试脚本 ===")
    
    success = test_recording()
    
    if success:
        logger.info("=== 录制测试完成，请检查数据库确认录制是否成功 ===")
    else:
        logger.error("=== 录制测试失败，请检查日志了解详细错误 ===") 