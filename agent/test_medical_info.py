#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
医疗信息整合测试脚本
-----------------
用于测试接收医疗信息和将其整合到转写结果中的功能
"""

import os
import sys
import json
import time
import logging
import argparse
import requests
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).resolve().parent.parent))

# 导入所需模块
from stream_capture.core.signal.signal_handler import SignalHandler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_medical_info.log')
    ]
)
logger = logging.getLogger('test_medical_info')

def send_start_signal(task_id, mac_address, medical_info=None, api_url="http://localhost:5000"):
    """
    发送带医疗信息的开始信号
    
    Args:
        task_id: 任务ID
        mac_address: MAC地址
        medical_info: 医疗信息字典(可选)
        api_url: API服务器URL
        
    Returns:
        dict: 响应内容
    """
    try:
        # 构造请求数据
        data = {
            "task_id": task_id,
            "mac_address": mac_address
        }
        
        # 添加医疗信息字段
        if medical_info:
            for key, value in medical_info.items():
                data[key] = value
        
        # 发送请求
        url = f"{api_url}/api/signal/start"
        logger.info(f"发送开始信号到 {url}，数据: {json.dumps(data, ensure_ascii=False)}")
        
        response = requests.post(
            url,
            json=data,
            headers={"Content-Type": "application/json"}
        )
        
        # 解析响应
        if response.status_code == 200:
            result = response.json()
            logger.info(f"收到成功响应: {json.dumps(result, ensure_ascii=False)}")
            return result
        else:
            logger.error(f"请求失败，状态码: {response.status_code}, 响应: {response.text}")
            return None
            
    except Exception as e:
        logger.error(f"发送开始信号时发生错误: {str(e)}")
        return None

def create_test_data(task_id, room_name, with_medical_info=True):
    """
    创建测试数据，包括目录结构和文件
    
    Args:
        task_id: 任务ID
        room_name: 房间名称
        with_medical_info: 是否创建医疗信息文件
        
    Returns:
        tuple: (sentences_path, medical_info_path)
    """
    try:
        # 创建测试目录
        test_dir = os.path.join("SeparatedAudioText", room_name, task_id)
        os.makedirs(test_dir, exist_ok=True)
        logger.info(f"创建测试目录: {test_dir}")
        
        # 创建测试sentences.json
        sentences_path = os.path.join(test_dir, "sentences.json")
        test_sentences = [
            {"speaker": 0, "text": "医生您好，我是来看头痛的。"},
            {"speaker": 1, "text": "你好，请问头痛持续多久了？"},
            {"speaker": 0, "text": "大概一周左右，主要是前额部疼痛。"},
            {"speaker": 1, "text": "有没有做过什么检查或服用药物？"},
            {"speaker": 0, "text": "没有，但我有高血压病史。"}
        ]
        
        with open(sentences_path, 'w', encoding='utf-8') as f:
            json.dump(test_sentences, f, ensure_ascii=False, indent=2)
        logger.info(f"创建测试sentences.json: {sentences_path}")
        
        # 创建测试医疗信息
        medical_info_path = os.path.join(test_dir, "medical_info.json")
        if with_medical_info:
            test_medical_info = {
                "患者主诉": "头痛伴眼部胀痛1周",
                "现病史": "患者1周前开始出现头痛，夜间伴有眼部胀痛，未进行任何治疗及检查。",
                "既往史": "既往有高血压病史，否认糖尿病、心脏病、肝炎、结核病等病史",
                "过敏史": "否认药物、食物过敏史"
            }
            
            with open(medical_info_path, 'w', encoding='utf-8') as f:
                json.dump(test_medical_info, f, ensure_ascii=False, indent=2)
            logger.info(f"创建测试medical_info.json: {medical_info_path}")
        
        return sentences_path, medical_info_path
    
    except Exception as e:
        logger.error(f"创建测试数据时发生错误: {str(e)}")
        return None, None

def test_merge_function(task_id="TEST_MEDICAL_INFO", room_name="测试房间"):
    """
    测试合并医疗信息功能
    
    Args:
        task_id: 测试任务ID
        room_name: 测试房间名称
        
    Returns:
        bool: 测试是否成功
    """
    try:
        # 创建测试数据
        sentences_path, medical_info_path = create_test_data(task_id, room_name)
        
        if not sentences_path or not os.path.exists(sentences_path):
            logger.error("创建测试数据失败，无法继续测试")
            return False
            
        # 初始化SignalHandler
        signal_handler = SignalHandler()
        
        # 测试目录
        transcript_dir = os.path.join("SeparatedAudioText", room_name, task_id)
        
        # 调用合并方法
        logger.info("调用_load_and_merge_medical_info方法")
        merged_data = signal_handler._load_and_merge_medical_info(sentences_path, transcript_dir)
        
        # 验证结果
        merged_path = os.path.join(transcript_dir, "sentences_with_medical_info.json")
        if os.path.exists(merged_path):
            with open(merged_path, 'r', encoding='utf-8') as f:
                saved_data = json.load(f)
                
            # 检查结果是否包含医疗信息
            if len(saved_data) > 0 and isinstance(saved_data[0], dict):
                first_item = saved_data[0]
                if "患者主诉" in first_item and "现病史" in first_item:
                    logger.info("测试成功：合并后的数据包含医疗信息")
                    
                    # 打印合并后的结构
                    logger.info(f"合并后的数据结构: {json.dumps(saved_data[:2], ensure_ascii=False, indent=2)}")
                    
                    return True
                else:
                    logger.error("测试失败：合并后的数据不包含医疗信息")
            else:
                logger.error(f"测试失败：合并后的数据结构不正确: {json.dumps(saved_data[:2], ensure_ascii=False)}")
        else:
            logger.error(f"测试失败：未找到合并后的数据文件: {merged_path}")
            
        return False
        
    except Exception as e:
        logger.error(f"测试合并功能时发生错误: {str(e)}")
        return False

def test_with_real_api(api_url="http://localhost:5000", mac_address="70:85:c4:61:66:78"):
    """
    使用真实API进行端到端测试
    
    Args:
        api_url: API服务器URL
        mac_address: MAC地址
        
    Returns:
        bool: 测试是否成功
    """
    try:
        # 生成任务ID
        task_id = f"TEST_API_{int(time.time())}"
        
        # 准备医疗信息
        medical_info = {
            "chief_complaint": "头痛伴眼部胀痛1周",
            "present_illness": "患者1周前开始出现头痛，夜间伴有眼部胀痛，未进行任何治疗及检查。",
            "past_medical_history": "既往有高血压病史，否认糖尿病、心脏病、肝炎、结核病等病史",
            "allergic_history": "否认药物、食物过敏史"
        }
        
        # 发送开始信号
        result = send_start_signal(task_id, mac_address, medical_info, api_url)
        
        if not result or result.get('status') != 'success':
            logger.error("发送开始信号失败，无法继续测试")
            return False
            
        room_name = result.get('room_name', '未知诊室')
        logger.info(f"成功获取房间名称: {room_name}")
        
        # 等待信号处理完成
        time.sleep(1)
        
        # 检查医疗信息是否已保存
        medical_info_path = os.path.join("SeparatedAudioText", room_name, task_id, "medical_info.json")
        
        if os.path.exists(medical_info_path):
            with open(medical_info_path, 'r', encoding='utf-8') as f:
                saved_info = json.load(f)
                
            logger.info(f"成功读取保存的医疗信息: {json.dumps(saved_info, ensure_ascii=False)}")
            return True
        else:
            logger.error(f"未找到保存的医疗信息: {medical_info_path}")
            return False
            
    except Exception as e:
        logger.error(f"进行端到端测试时发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试医疗信息整合功能')
    parser.add_argument('--real-api', action='store_true', help='是否使用真实API进行测试')
    parser.add_argument('--api-url', type=str, default='http://localhost:5000', help='API服务器URL')
    parser.add_argument('--mac', type=str, default='70:85:c4:61:66:78', help='MAC地址')
    
    args = parser.parse_args()
    
    logger.info("开始测试医疗信息整合功能")
    
    # 测试合并功能
    if test_merge_function():
        logger.info("合并功能测试成功")
    else:
        logger.error("合并功能测试失败")
    
    # 如果要测试真实API
    if args.real_api:
        logger.info(f"开始使用真实API测试，URL: {args.api_url}")
        if test_with_real_api(args.api_url, args.mac):
            logger.info("真实API测试成功")
        else:
            logger.error("真实API测试失败")
    
    logger.info("测试完成")

if __name__ == "__main__":
    main() 