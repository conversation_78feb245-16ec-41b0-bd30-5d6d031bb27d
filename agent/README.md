# 医疗对话Agent集成模块

该模块提供了将医疗对话转写结果发送到Dify Agent进行分析的功能。

## 功能特点

- 自动读取sentences.json文件中的转写结果
- 格式化转写数据，便于Agent理解
- 支持流式响应和阻塞式响应
- 支持会话保持，允许多轮对话
- 自动保存Agent响应结果
- 提供命令行工具进行测试

## 组件说明

该模块包含以下主要组件：

1. **DifyAgentClient**: Agent客户端核心类，提供与Dify API交互的功能
2. **信号处理器集成**: 在视频录制结束后自动将转写结果发送给Agent
3. **测试工具**: 提供命令行工具用于测试和调试Agent集成

## 响应格式与保存

Agent的响应将被保存为两种格式的文件：

1. **`task_id.json`** - 仅包含Agent的纯回复内容：
   - 如果Agent返回的是JSON格式的内容，将保持其JSON结构不变
   - 如果Agent返回的是纯文本内容，则直接以文本格式保存
   - 该文件不包含任何API元数据，如`conversation_id`等
   - 推荐优先使用此文件获取Agent分析结果

2. **`agent_response.json`** - 包含完整API响应：
   - 包含`conversation_id`、`answer`等所有API返回的字段
   - 保留此文件是为了向后兼容和调试目的

示例：
```
# JSON格式响应 - task_id.json
{
  "summary": "患者反映头痛症状",
  "keypoints": ["需要进一步检查", "建议服用止痛药"]
}

# 纯文本响应 - task_id.json
患者反映头痛症状，需要进一步检查，建议服用止痛药。
```

## 工作流程

1. 视频流录制完成后，系统自动提取音频并进行转写
2. 转写结果以JSON形式保存在`SeparatedAudioText/诊室名称/任务ID/sentences.json`
3. 信号处理器检测到转写完成后，自动调用DifyAgentClient发送数据
4. Agent返回的分析结果同时保存到两个文件：
   - `task_id.json`：以任务ID命名的文件（推荐使用）
   - `agent_response.json`：兼容版本的文件
5. 整个过程无需人工干预，完全自动化
6. 如果转写尚未完成，系统会自动延迟并最多重试3次

## 使用方法

### 自动处理

当整个系统正常运行时，Agent处理将在视频录制结束后自动进行，无需手动干预。

### 手动测试

您可以使用测试工具手动测试Agent集成:

1. 列出所有可用的sentences.json文件：

```bash
python agent/test_agent.py --list
```

2. 测试最新的转写结果：

```bash
python agent/test_agent.py --latest
```

3. 测试特定的转写文件：

```bash
python agent/test_agent.py --input SeparatedAudioText/402诊室/20230815-1/sentences.json
```

4. 测试所有转写文件：

```bash
python agent/test_agent.py --all
```

5. 使用流式响应模式：

```bash
python agent/test_agent.py --input <文件路径> --mode streaming
```

6. 保存Agent响应结果：

```bash
python agent/test_agent.py --input <文件路径> --save
```

### 自定义Dify API

如果您需要使用不同的Dify API端点或API密钥：

```bash
python agent/test_agent.py --input <文件路径> --api-url <API_URL> --api-key <API_KEY>
```

## 配置选项

默认配置已经设置为使用本地部署的Dify服务：

- API URL: http://10.200.146.213:5023/v1
- API Key: app-hpL6A5CNAR5HdSf2sO6yvEhn

如需修改这些值，可以：

1. 在调用测试工具时使用参数指定
2. 修改`agent/dify_client.py`文件中的默认值

**注意**：Agent仅支持流式(streaming)响应模式，不支持阻塞(blocking)模式

## 故障排除

如果Agent集成不能正常工作，请检查：

1. Dify服务是否正常运行和可访问
2. 网络连接是否正常
3. API密钥是否有效
4. 查看日志文件`agent/agent.log`和`agent/test_agent.log`以获取更多信息

## 开发说明

如需扩展Agent功能，可以修改以下文件：

- `agent/dify_client.py`: Agent客户端实现
- `stream_capture/core/signal/signal_handler.py`: 信号处理器中的自动处理部分
- `agent/test_agent.py`: 测试工具

## 示例：创建测试转写文件

如果您想创建一个测试用的转写文件，可以使用以下命令：

```bash
mkdir -p SeparatedAudioText/测试诊室/TEST001
cat > SeparatedAudioText/测试诊室/TEST001/sentences.json << EOF
[
  {
    "speaker": 0,
    "text": "医生，我最近老是感觉胸闷气短，尤其是上楼梯的时候，有时候晚上睡觉也会憋醒。"
  },
  {
    "speaker": 1,
    "text": "您平时有高血压、糖尿病或者心脏病史吗？"
  },
  {
    "speaker": 0,
    "text": "我有高血压十年了，一直在吃药控制，最近血压一直在140/90左右。"
  },
  {
    "speaker": 1,
    "text": "您最近有没有感觉胸痛，或者出汗多、乏力的情况？"
  },
  {
    "speaker": 0,
    "text": "有时候会胸痛，感觉像是压了块石头一样，出汗倒是不明显。"
  },
  {
    "speaker": 1,
    "text": "我们先给您做个心电图和胸片，排查一下心脏方面的问题，建议您近期复查血压和血脂。"
  }
]
EOF
```

然后使用以下命令测试：

```bash
python agent/test_agent.py --input SeparatedAudioText/测试诊室/TEST001/sentences.json
``` 