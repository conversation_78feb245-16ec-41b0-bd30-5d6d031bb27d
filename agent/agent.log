2025-03-26 17:33:59,728 - test_agent - INFO - 测试最新的文件: /data/AudioSeparation/SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-26 17:33:59,728 - test_agent - INFO - 测试文件: /data/AudioSeparation/SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-26 17:33:59,728 - dify_agent - INFO - 成功读取文件: /data/AudioSeparation/SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-26 17:33:59,728 - dify_agent - INFO - 发送请求到: http://10.200.146.213:5023/v1/chat-messages
2025-03-26 17:33:59,750 - dify_agent - ERROR - 发送请求时发生错误: 400 Client Error: BAD REQUEST for url: http://10.200.146.213:5023/v1/chat-messages
2025-03-26 17:33:59,751 - test_agent - ERROR - 从Agent获取响应失败
2025-03-27 10:06:56,546 - test_agent - INFO - 测试最新的文件: /data/AudioSeparation/SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-27 10:06:56,546 - test_agent - INFO - 测试文件: /data/AudioSeparation/SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-27 10:06:56,546 - dify_agent - INFO - 成功读取文件: /data/AudioSeparation/SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-27 10:06:56,546 - dify_agent - INFO - 发送请求到: http://10.200.146.213/v1/chat-messages
2025-03-27 10:06:56,547 - dify_agent - INFO - 请求头: {"Authorization": "Bearer app-hpL6A5CNAR5HdSf2sO6yvEhn", "Content-Type": "application/json"}
2025-03-27 10:06:56,547 - dify_agent - INFO - 请求体: {"query": "医患对话转写内容:\n\n说话人0 ( --> ): 今天天气怎么样嘛，我感觉我的肚子有点不舒服，\n说话人0 ( --> ): 我的头还有点晕，是不是发烧来了医生？\n", "inputs": {}, "response_mode": "blocking", "user": "system"}
2025-03-27 10:06:56,552 - dify_agent - INFO - 响应状态码: 404
2025-03-27 10:06:56,552 - dify_agent - INFO - 响应头: {'date': 'Thu, 27 Mar 2025 02:06:56 GMT', 'server': 'uvicorn', 'content-length': '22', 'content-type': 'application/json'}
2025-03-27 10:06:56,552 - dify_agent - INFO - 响应内容(JSON): {"detail": "Not Found"}...
2025-03-27 10:06:56,552 - dify_agent - ERROR - 请求失败，状态码: 404, 响应内容: {"detail":"Not Found"}
2025-03-27 10:06:56,552 - test_agent - ERROR - 从Agent获取响应失败
2025-03-27 10:19:25,678 - test_agent - INFO - 测试文件: SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-27 10:19:25,678 - dify_agent - INFO - 成功读取文件: SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-27 10:19:25,678 - dify_agent - INFO - 发送请求到: http://10.200.146.213/v1/chat-messages
2025-03-27 10:19:25,678 - dify_agent - INFO - 请求头: {"Content-Type": "application/json"}
2025-03-27 10:19:25,679 - dify_agent - INFO - 请求体: {"query": "医患对话转写内容:\n\n说话人0 ( --> ): 今天天气怎么样嘛，我感觉我的肚子有点不舒服，\n说话人0 ( --> ): 我的头还有点晕，是不是发烧来了医生？\n", "inputs": {}, "response_mode": "streaming", "user": "system"}
2025-03-27 10:19:25,684 - dify_agent - INFO - 响应状态码: 404
2025-03-27 10:19:25,685 - dify_agent - ERROR - 请求失败，状态码: 404, 响应内容: {"detail":"Not Found"}
2025-03-27 10:19:25,685 - test_agent - ERROR - 从Agent获取响应失败
2025-03-27 10:20:05,399 - test_agent - INFO - 测试文件: SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-27 10:20:05,399 - dify_agent - INFO - 成功读取文件: SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-27 10:20:05,400 - dify_agent - INFO - 发送请求到: http://10.200.146.213/v1/chat-messages
2025-03-27 10:20:05,400 - dify_agent - INFO - 请求头: {"Content-Type": "application/json"}
2025-03-27 10:20:05,400 - dify_agent - INFO - 请求体: {"query": "医患对话转写内容:\n\n说话人0 ( --> ): 今天天气怎么样嘛，我感觉我的肚子有点不舒服，\n说话人0 ( --> ): 我的头还有点晕，是不是发烧来了医生？\n", "inputs": {}, "response_mode": "streaming", "user": "system"}
2025-03-27 10:20:05,404 - dify_agent - INFO - 响应状态码: 404
2025-03-27 10:20:05,404 - dify_agent - ERROR - 请求失败，状态码: 404, 响应内容: {"detail":"Not Found"}
2025-03-27 10:20:05,404 - test_agent - ERROR - 从Agent获取响应失败
2025-03-27 10:20:59,700 - test_agent - INFO - 测试文件: SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-27 10:20:59,700 - dify_agent - INFO - 成功读取文件: SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-27 10:20:59,700 - dify_agent - INFO - 发送请求到: http://10.200.146.213:5023/v1/chat-messages
2025-03-27 10:20:59,700 - dify_agent - INFO - 请求头: {"Content-Type": "application/json"}
2025-03-27 10:20:59,700 - dify_agent - INFO - 请求体: {"query": "医患对话转写内容:\n\n说话人0 ( --> ): 今天天气怎么样嘛，我感觉我的肚子有点不舒服，\n说话人0 ( --> ): 我的头还有点晕，是不是发烧来了医生？\n", "inputs": {}, "response_mode": "streaming", "user": "system"}
2025-03-27 10:21:05,716 - dify_agent - INFO - 响应状态码: 200
2025-03-27 10:21:05,716 - dify_agent - INFO - 已保存会话ID: 0306bf0e-76a0-4c9d-9d88-f2297a0a55f0
2025-03-27 10:21:21,399 - dify_agent - INFO - 元数据: {'usage': {'prompt_tokens': 663, 'prompt_unit_price': '2', 'prompt_price_unit': '0.000001', 'prompt_price': '0.001326', 'completion_tokens': 351, 'completion_unit_price': '8', 'completion_price_unit': '0.000001', 'completion_price': '0.002808', 'total_tokens': 1014, 'total_price': '0.004134', 'currency': 'RMB', 'latency': 21.554829576052725}}
2025-03-27 10:21:21,399 - dify_agent - INFO - 收到完整回答: {
  "主诉": "头晕伴腹部不适1天",
  "现病史": "患者今日无明显诱因出现头晕，伴腹部不适，自疑发热，具体体温未测。无恶心呕吐、腹泻等伴随症状。",
  "既往史": "患者未提供",
  "体格检查": {
    "体重": "未记录",
    "体温": "需测量",
    "脉搏": "未记录",
    "舒张压": "未记录",
    "收缩压": "未记录",
    "呼吸": "未记录",
    "血糖": "未记录",
    "舌苔": "未记录",
    "脉象": "未记录"
  },
  "中医诊断及证型": "待查-需结合四诊",
  "西医诊断": "头晕待查",
  "病位": "头部、腹部",
  "病性": "虚实待辨",
  "治法方剂": "需完善四诊后确定",
  "西药": "暂不适用",
  "建议": [
    "建议立即测量体温、血压",
    "完善血常规检查",
    "头晕持续不缓解需排除前庭性眩晕"
  ],
  "医师签名": ""
}

注：
1. 主诉合并了头晕和腹部不适症状，持续时间根据"今天"推断为1天
2. 现病史中特别标注"自疑发热"以区分患者主观感受与客观体征
3. 体格检查中体温标注"需测量"以提示必要检查项目
4. 建议栏强调基础生命体征测量的紧迫性
5. 中医诊断暂无法确定证型，体现严谨性
2025-03-27 10:21:21,399 - test_agent - INFO - 成功从Agent获取响应
2025-03-27 10:51:17,365 - test_agent - ERROR - 文件不存在: SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-27 10:51:37,453 - test_agent - ERROR - 文件不存在: SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-27 10:51:58,794 - test_agent - INFO - 测试文件: /data/AudioSeparation/SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-27 10:51:58,795 - dify_agent - INFO - 成功读取文件: /data/AudioSeparation/SeparatedAudioText/402诊室/TASK2023110100001/sentences.json
2025-03-27 10:51:58,795 - dify_agent - INFO - 发送请求到: http://10.200.146.213:5023/v1/chat-messages
2025-03-27 10:51:58,795 - dify_agent - INFO - 请求头: {"Content-Type": "application/json"}
2025-03-27 10:51:58,795 - dify_agent - INFO - 请求体: {"query": "医患对话转写内容:\n\n说话人0 ( --> ): 今天天气怎么样嘛，我感觉我的肚子有点不舒服，\n说话人0 ( --> ): 我的头还有点晕，是不是发烧来了医生？\n", "inputs": {}, "response_mode": "streaming", "user": "system"}
2025-03-27 10:52:03,304 - dify_agent - INFO - 响应状态码: 200
2025-03-27 10:52:03,305 - dify_agent - INFO - 已保存会话ID: db3c34e3-2c77-4ff1-a606-e70978aa2796
2025-03-27 10:52:18,990 - dify_agent - INFO - 元数据: {'usage': {'prompt_tokens': 1071, 'prompt_unit_price': '2', 'prompt_price_unit': '0.000001', 'prompt_price': '0.002142', 'completion_tokens': 346, 'completion_unit_price': '8', 'completion_price_unit': '0.000001', 'completion_price': '0.002768', 'total_tokens': 1417, 'total_price': '0.00491', 'currency': 'RMB', 'latency': 20.054907370824367}}
2025-03-27 10:52:18,990 - dify_agent - INFO - 收到完整回答: ```json
{
  "患者主诉": "头晕伴腹部不适1天",
  "现病史": "患者1天前无明显诱因出现头晕，伴腹部不适，具体性质未描述，自测体温未提及，无其他伴随症状描述。",
  "既往史": "患者未提供",
  "个人史": "患者未提供",
  "月经史": "",
  "过敏记录": "患者未提供",
  "辅助检查": "未记录",
  "预问症状": "需确认：1.腹痛具体性质（胀痛/绞痛/隐痛）2.是否实际测量体温3.有无恶心呕吐等消化道症状",
  "中医四诊": "主诉信息不足，暂缺舌脉资料。头晕可能对应肝阳上亢或气血不足证，需结合具体体征判断",
  "诊疗意见": "建议：1.监测体温2.完善血压测量3.腹痛性质问诊",
  "中医诊断及证型": "待查：眩晕病-待定证型",
  "西医诊断": "待查：1.头晕待查2.腹痛待查",
  "血糖类型": "",
  "血糖值": "",
  "确认病位": "头部、腹部（待精确定位）",
  "确认病性": "虚实待辨",
  "临床检验": "建议血常规、血糖检测",
  "临床检查": "建议血压测量、腹部触诊",
  "治法方剂": "待诊断明确后拟定",
  "西药": "暂不推荐用药",
  "临床治疗": "建议进一步问诊查体"
}
```
2025-03-27 10:52:18,991 - test_agent - INFO - 成功从Agent获取响应
