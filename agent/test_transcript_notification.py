#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
转写通知测试脚本
-----------
用于测试sentences.json文件生成后的通知机制
"""

import os
import sys
import json
import logging
import time
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).resolve().parent.parent))

# 导入模块
from backend import AudioProcessor
from stream_capture.core.signal.signal_handler import SignalHandler

# 获取统一的日志目录
logs_dir = os.environ.get('AUDIO_SEPARATION_LOGS_DIR', 'logs')
os.makedirs(logs_dir, exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(logs_dir, 'test_notification.log'))
    ]
)
logger = logging.getLogger('test_notification')

def prepare_test_environment():
    """
    准备测试环境，创建测试所需的目录和文件
    
    Returns:
        tuple: (task_id, room_name, transcript_dir)
    """
    # 测试参数
    room_name = "测试诊室"
    task_id = f"TEST_{int(time.time())}"
    
    # 创建测试目录
    transcript_dir = os.path.join("SeparatedAudioText", room_name, task_id)
    os.makedirs(transcript_dir, exist_ok=True)
    
    logger.info(f"创建测试目录: {transcript_dir}")
    
    return task_id, room_name, transcript_dir

def test_notification_system():
    """
    测试通知系统
    """
    try:
        # 准备测试环境
        task_id, room_name, transcript_dir = prepare_test_environment()
        
        # 初始化信号处理器
        logger.info("初始化信号处理器")
        signal_handler = SignalHandler()
        
        # 注册回调函数
        logger.info("注册转写就绪回调函数")
        def callback(task_id, room_name):
            logger.info(f"回调函数被调用: {task_id}, {room_name}")
            signal_handler.handle_transcript_ready(task_id, room_name)
        
        AudioProcessor.register_transcript_ready_callback(callback)
        
        # 创建测试转写文件
        sentences_path = os.path.join(transcript_dir, "sentences.json")
        test_sentences = [
            {"speaker": 0, "text": "这是测试对话的第一句。"},
            {"speaker": 1, "text": "这是医生的回复。"},
            {"speaker": 0, "text": "这是患者的第二句话。"},
            {"speaker": 1, "text": "好的，我明白了。"}
        ]
        
        logger.info(f"创建测试转写文件: {sentences_path}")
        with open(sentences_path, "w", encoding="utf-8") as f:
            json.dump(test_sentences, f, ensure_ascii=False, indent=2)
        
        # 模拟音频处理完成后的通知
        logger.info("模拟通知转写就绪")
        AudioProcessor._notify_transcript_ready(task_id, room_name)
        
        # 等待一会，确保处理完成
        time.sleep(3)
        
        # 检查是否生成了Agent响应文件
        agent_response_path = os.path.join(transcript_dir, f"{task_id}.json")
        compat_path = os.path.join(transcript_dir, "agent_response.json")
        
        if os.path.exists(agent_response_path):
            logger.info(f"Agent响应文件已生成: {agent_response_path}")
            with open(agent_response_path, "r", encoding="utf-8") as f:
                content = f.read()
                logger.info(f"响应内容: {content[:100]}...")
        else:
            logger.error(f"Agent响应文件未生成: {agent_response_path}")
        
        if os.path.exists(compat_path):
            logger.info(f"兼容版响应文件已生成: {compat_path}")
        else:
            logger.error(f"兼容版响应文件未生成: {compat_path}")
        
        # 检查结果
        if os.path.exists(agent_response_path) and os.path.exists(compat_path):
            logger.info("测试成功：通知机制工作正常")
            return True
        else:
            logger.error("测试失败：通知机制未正常工作")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False

def cleanup(success=False):
    """
    清理测试环境
    
    Args:
        success: 测试是否成功
    """
    if not success:
        logger.info("保留测试目录以供检查问题")
        return
    
    try:
        # 清理SeparatedAudioText/测试诊室目录
        test_dir = os.path.join("SeparatedAudioText", "测试诊室")
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            logger.info(f"已清理测试目录: {test_dir}")
    except Exception as e:
        logger.error(f"清理测试环境时发生错误: {str(e)}")

if __name__ == "__main__":
    logger.info("开始测试转写通知机制")
    success = test_notification_system()
    
    if success:
        logger.info("测试通过：通知机制正常工作")
        cleanup(success=True)
    else:
        logger.error("测试失败：通知机制未正常工作") 