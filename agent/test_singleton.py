#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试单例模式
----------
此脚本用于测试SignalHandler和RoomMappingDAO的单例模式是否有效。
"""

import os
import sys
import time
import threading
import logging

# 设置项目根目录
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_singleton')

def test_signal_handler_singleton():
    """测试SignalHandler的单例模式"""
    try:
        from stream_capture.core.signal.signal_handler import SignalHandler
        
        # 创建第一个实例
        logger.info("创建第一个SignalHandler实例")
        handler1 = SignalHandler()
        handler1_id = id(handler1)
        logger.info(f"第一个实例ID: {handler1_id}")
        
        # 创建第二个实例
        logger.info("创建第二个SignalHandler实例")
        handler2 = SignalHandler()
        handler2_id = id(handler2)
        logger.info(f"第二个实例ID: {handler2_id}")
        
        # 检查两个实例是否相同
        if handler1_id == handler2_id:
            logger.info("测试成功：SignalHandler的单例模式有效，两个实例ID相同")
            return True
        else:
            logger.error("测试失败：SignalHandler的单例模式无效，两个实例ID不同")
            return False
    except Exception as e:
        logger.error(f"测试SignalHandler单例模式时发生错误: {str(e)}")
        return False

def test_room_mapping_dao_singleton():
    """测试RoomMappingDAO的单例模式"""
    try:
        from stream_capture.core.db.room_mapping_dao import RoomMappingDAO
        
        # 创建第一个实例
        logger.info("创建第一个RoomMappingDAO实例")
        dao1 = RoomMappingDAO()
        dao1_id = id(dao1)
        logger.info(f"第一个实例ID: {dao1_id}")
        
        # 创建第二个实例
        logger.info("创建第二个RoomMappingDAO实例")
        dao2 = RoomMappingDAO()
        dao2_id = id(dao2)
        logger.info(f"第二个实例ID: {dao2_id}")
        
        # 检查两个实例是否相同
        if dao1_id == dao2_id:
            logger.info("测试成功：RoomMappingDAO的单例模式有效，两个实例ID相同")
            return True
        else:
            logger.error("测试失败：RoomMappingDAO的单例模式无效，两个实例ID不同")
            return False
    except Exception as e:
        logger.error(f"测试RoomMappingDAO单例模式时发生错误: {str(e)}")
        return False

def test_init_signal_handler():
    """测试init_signal_handler函数是否返回相同的实例"""
    try:
        from stream_capture.api.signal_receiver import init_signal_handler
        
        # 第一次初始化
        logger.info("第一次调用init_signal_handler")
        handler1 = init_signal_handler()
        handler1_id = id(handler1)
        logger.info(f"第一个实例ID: {handler1_id}")
        
        # 第二次初始化
        logger.info("第二次调用init_signal_handler")
        handler2 = init_signal_handler()
        handler2_id = id(handler2)
        logger.info(f"第二个实例ID: {handler2_id}")
        
        # 检查两个实例是否相同
        if handler1_id == handler2_id:
            logger.info("测试成功：init_signal_handler返回相同的实例")
            return True
        else:
            logger.error("测试失败：init_signal_handler返回不同的实例")
            return False
    except Exception as e:
        logger.error(f"测试init_signal_handler时发生错误: {str(e)}")
        return False

def test_multithreaded_init():
    """测试多线程环境下的初始化"""
    from stream_capture.core.signal.signal_handler import SignalHandler
    
    results = []
    
    def worker():
        handler = SignalHandler()
        results.append(id(handler))
        logger.info(f"线程 {threading.current_thread().name} 创建的实例ID: {id(handler)}")
    
    # 创建多个线程
    threads = []
    for i in range(5):
        thread = threading.Thread(target=worker, name=f"Thread-{i}")
        threads.append(thread)
    
    # 启动所有线程
    for thread in threads:
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    # 检查所有实例ID是否相同
    if len(set(results)) == 1:
        logger.info("测试成功：多线程环境下，SignalHandler的单例模式有效")
        return True
    else:
        logger.error("测试失败：多线程环境下，SignalHandler的单例模式无效")
        return False

def main():
    """主函数"""
    success = True
    
    # 测试SignalHandler的单例模式
    logger.info("=== 测试SignalHandler的单例模式 ===")
    if not test_signal_handler_singleton():
        success = False
    
    # 测试RoomMappingDAO的单例模式
    logger.info("\n=== 测试RoomMappingDAO的单例模式 ===")
    if not test_room_mapping_dao_singleton():
        success = False
    
    # 测试init_signal_handler函数
    logger.info("\n=== 测试init_signal_handler函数 ===")
    if not test_init_signal_handler():
        success = False
    
    # 测试多线程环境
    logger.info("\n=== 测试多线程环境下的初始化 ===")
    if not test_multithreaded_init():
        success = False
    
    # 总结
    if success:
        logger.info("\n所有测试通过，单例模式实现有效")
        return 0
    else:
        logger.error("\n部分测试失败，单例模式实现可能存在问题")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 