#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HSI集成测试脚本
-----------
用于测试向HSI系统发送电子病例结果
"""

import os
import sys
import json
import logging
import argparse
import requests
from pathlib import Path

# 将项目根目录添加到Python路径
script_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(script_dir)
sys.path.append(root_dir)

# 获取统一的日志目录
logs_dir = os.environ.get('AUDIO_SEPARATION_LOGS_DIR', 'logs')
os.makedirs(logs_dir, exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(logs_dir, 'test_hsi.log'))
    ]
)
logger = logging.getLogger('test_hsi')

def send_to_hsi(task_id, json_file_path):
    """
    将指定的JSON文件内容发送到HSI系统
    
    Args:
        task_id: 要使用的任务ID
        json_file_path: 要发送的JSON文件路径
        
    Returns:
        bool: 是否成功发送
    """
    try:
        # 读取JSON文件
        with open(json_file_path, 'r', encoding='utf-8') as f:
            json_content = json.load(f)
        
        # 添加task_id
        data = {
            "task_id": task_id
        }
        data.update(json_content)
        
        # 发送POST请求
        url = "https://hz.cddmi.cn/AppService/SaveSignalResult"
        logger.info(f"正在将电子病例结果发送到HSI系统: {task_id}")
        logger.debug(f"发送数据: {data}")
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        # 发送请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 检查响应
        if response.status_code == 200:
            logger.info(f"成功发送电子病例结果到HSI系统: {task_id}, 响应: {response.text}")
            return True
        else:
            logger.error(f"发送电子病例结果到HSI系统失败: {task_id}, 状态码: {response.status_code}, 响应: {response.text}")
            return False
    
    except Exception as e:
        logger.error(f"发送电子病例结果到HSI系统时发生错误: {str(e)}")
        return False

def generate_test_json(output_path):
    """
    生成测试用的JSON数据
    
    Args:
        output_path: 输出文件路径
        
    Returns:
        bool: 是否成功生成
    """
    try:
        # 示例电子病例数据
        test_data = {
            "chief_complaint": "中上腹部疼痛3天，伴烧灼感",
            "current_illness_history": "患者3天前无明显诱因出现中上腹部疼痛，呈烧灼样，饭后加重，伴腹胀、恶心、呕吐1次，反酸、打嗝频繁。大便偏稀，颜色深，无黑便或便血。发病前有食用辛辣食物及饮酒史。曾自行服用铝碳酸镁片，症状稍缓解。夜间因疼痛影响睡眠，体重无变化。",
            "past_history": "",
            "personal_history": "近期有饮酒及食用辛辣食物史。",
            "menstrual_history": "",
            "allergy_records": "",
            "auxiliary_examination": "建议完善血常规、大便隐血试验及胃镜检查。",
            "presumptive_symptoms": "",
            "tcm_diagnosis": "胃痛病-湿热中阻证",
            "treatment_recommendations": "1. 避免饮酒及辛辣刺激性食物；2. 完善相关检查；3. 对症治疗。",
            "tcm_diagnosis_type": "湿热中阻证",
            "western_diagnosis": "胃炎或消化性溃疡（待胃镜确诊）",
            "blood_glucose_type": "",
            "blood_glucose_value": "",
            "disease_location": "胃",
            "disease_nature": "湿热",
            "clinical_tests": "血常规、大便隐血试验",
            "clinical_examination": "腹部压痛",
            "treatment_method": "清热化湿，和胃止痛",
            "western_medication": "铝碳酸镁片（已用）",
            "clinical_treatment": "建议胃镜检查"
        }
        
        # 保存到文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"成功生成测试JSON文件: {output_path}")
        return True
    
    except Exception as e:
        logger.error(f"生成测试JSON文件时发生错误: {str(e)}")
        return False

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='HSI集成测试工具')
    parser.add_argument('--task-id', type=str, help='任务ID，默认为TEST_ID', default='TEST_ID')
    parser.add_argument('--input', type=str, help='要发送的JSON文件路径')
    parser.add_argument('--generate', action='store_true', help='生成测试用的JSON文件')
    parser.add_argument('--output', type=str, help='生成的测试JSON文件保存路径', default='test_data.json')
    
    args = parser.parse_args()
    
    # 生成测试数据
    if args.generate:
        output_path = args.output
        if generate_test_json(output_path):
            logger.info(f"测试数据已生成: {output_path}")
            logger.info(f"你可以使用以下命令发送测试数据:")
            logger.info(f"python {__file__} --task-id YOUR_TASK_ID --input {output_path}")
        return
    
    # 发送JSON数据
    if args.input:
        json_file_path = args.input
        task_id = args.task_id
        
        if not os.path.exists(json_file_path):
            logger.error(f"指定的JSON文件不存在: {json_file_path}")
            return
        
        if send_to_hsi(task_id, json_file_path):
            logger.info(f"成功向HSI系统发送数据")
        else:
            logger.error(f"向HSI系统发送数据失败")
    else:
        logger.error("请指定要发送的JSON文件路径")
        logger.info(f"使用示例: python {__file__} --task-id YOUR_TASK_ID --input path/to/json_file.json")
        logger.info(f"或者生成测试数据: python {__file__} --generate")

if __name__ == "__main__":
    main() 