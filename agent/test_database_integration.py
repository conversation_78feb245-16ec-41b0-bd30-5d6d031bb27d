#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库集成测试脚本
--------------
用于测试Agent处理后保存数据到数据库的集成功能
"""

import os
import sys
import json
import logging
import time
import argparse
from pathlib import Path

# 将项目根目录添加到Python路径
script_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(script_dir)
sys.path.append(root_dir)

# 导入所需模块
from stream_capture.core.signal.signal_handler import SignalHandler

# 获取统一的日志目录
logs_dir = os.environ.get('AUDIO_SEPARATION_LOGS_DIR', 'logs')
os.makedirs(logs_dir, exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(logs_dir, 'test_db_integration.log'))
    ]
)
logger = logging.getLogger('test_db_integration')

def prepare_test_environment(task_id, room_name):
    """
    准备测试环境
    
    Args:
        task_id: 任务ID
        room_name: 房间名称
        
    Returns:
        bool: 是否成功准备
    """
    try:
        # 创建必要的目录
        task_dir = os.path.join("SeparatedAudioText", room_name, task_id)
        os.makedirs(task_dir, exist_ok=True)
        
        # 创建录制目录及模拟视频文件
        recordings_dir = os.path.join("stream_capture", "recordings", room_name)
        os.makedirs(recordings_dir, exist_ok=True)
        
        video_path = os.path.join(recordings_dir, f"{task_id}.mp4")
        if not os.path.exists(video_path):
            with open(video_path, 'wb') as f:
                # 写入一个简单的MP4头部
                f.write(b'\x00\x00\x00\x18ftypmp42\x00\x00\x00\x00mp42mp41\x00\x00\x00\x00moov')
        
        # 创建模拟的sentences.json
        sentences_path = os.path.join(task_dir, "sentences.json")
        sentences_data = {
            "dialogue": [
                {
                    "role": "医生",
                    "content": "您好，今天有什么不舒服？"
                },
                {
                    "role": "患者",
                    "content": "医生，我这两天胃有点疼，吃东西后感觉特别难受。"
                },
                {
                    "role": "医生",
                    "content": "疼痛是什么性质的？比如说是烧灼感还是钝痛？"
                },
                {
                    "role": "患者",
                    "content": "感觉是烧灼感，吃完饭后会更加严重。"
                }
            ]
        }
        
        with open(sentences_path, 'w', encoding='utf-8') as f:
            json.dump(sentences_data, f, ensure_ascii=False, indent=2)
        
        # 创建speaker目录和模拟视频片段
        for speaker_id in range(2):
            speaker_dir = os.path.join(task_dir, f"speaker_{speaker_id}")
            os.makedirs(speaker_dir, exist_ok=True)
            
            # 创建模拟的视频片段
            for segment_id in range(1, 3):
                segment_path = os.path.join(speaker_dir, f"segment_{segment_id}.mp4")
                if not os.path.exists(segment_path):
                    with open(segment_path, 'wb') as f:
                        # 写入一个简单的MP4头部
                        f.write(b'\x00\x00\x00\x18ftypmp42\x00\x00\x00\x00mp42mp41\x00\x00\x00\x00moov')
        
        # 创建一个模拟的Agent响应
        agent_response = {
            "chief_complaint": "胃痛伴烧灼感",
            "current_illness_history": "患者近两天出现胃部疼痛，呈烧灼样，饭后症状加重。",
            "past_history": "",
            "personal_history": "",
            "menstrual_history": "",
            "allergy_records": "",
            "auxiliary_examination": "",
            "presumptive_symptoms": "",
            "tcm_diagnosis": "胃热证",
            "treatment_recommendations": "建议清淡饮食，避免辛辣刺激",
            "tcm_diagnosis_type": "胃热证",
            "western_diagnosis": "急性胃炎",
            "blood_glucose_type": "",
            "blood_glucose_value": "",
            "disease_location": "胃",
            "disease_nature": "热",
            "clinical_tests": "",
            "clinical_examination": "",
            "treatment_method": "清热和胃",
            "western_medication": "",
            "clinical_treatment": ""
        }
        
        # 保存Agent响应文件
        agent_response_path = os.path.join(task_dir, f"{task_id}.json")
        with open(agent_response_path, 'w', encoding='utf-8') as f:
            json.dump(agent_response, f, ensure_ascii=False, indent=2)
        
        # 保存完整响应文件(模拟)
        complete_response_path = os.path.join(task_dir, "agent_response.json")
        complete_response = {
            "conversation_id": "test-conversation-id",
            "answer": json.dumps(agent_response, ensure_ascii=False)
        }
        with open(complete_response_path, 'w', encoding='utf-8') as f:
            json.dump(complete_response, f, ensure_ascii=False, indent=2)
        
        logger.info(f"测试环境准备完成，任务ID: {task_id}, 房间: {room_name}")
        return True
    
    except Exception as e:
        logger.error(f"准备测试环境时发生错误: {str(e)}")
        return False

def test_save_to_database(task_id, room_name):
    """
    测试保存到数据库
    
    Args:
        task_id: 任务ID
        room_name: 房间名称
        
    Returns:
        bool: 测试是否成功
    """
    try:
        # 初始化信号处理器
        handler = SignalHandler()
        
        # 调用数据库保存方法
        logger.info(f"开始测试保存到数据库: {task_id}")
        success = handler._save_to_database(task_id, room_name)
        
        if success:
            logger.info(f"数据库保存方法调用成功，请稍等片刻等待后台进程完成")
            
            # 等待一段时间，让后台进程完成
            time.sleep(15)
            
            logger.info(f"请手动验证数据是否已保存到数据库中，可以使用以下命令:")
            logger.info(f"python front/test_db_changes.py --query")
            
            return True
        else:
            logger.error(f"数据库保存方法调用失败")
            return False
    
    except Exception as e:
        logger.error(f"测试保存到数据库时发生错误: {str(e)}")
        return False

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='数据库集成测试工具')
    parser.add_argument('--task-id', type=str, help='任务ID', default=f"TEST_DB_{int(time.time())}")
    parser.add_argument('--room', type=str, help='房间名称', default='测试诊室')
    parser.add_argument('--skip-prep', action='store_true', help='跳过环境准备步骤')
    
    args = parser.parse_args()
    
    task_id = args.task_id
    room_name = args.room
    
    # 准备测试环境
    if not args.skip_prep:
        logger.info(f"开始准备测试环境: {task_id}, {room_name}")
        if not prepare_test_environment(task_id, room_name):
            logger.error("准备测试环境失败，测试终止")
            return 1
    
    # 测试保存到数据库
    logger.info(f"开始测试保存到数据库: {task_id}, {room_name}")
    if test_save_to_database(task_id, room_name):
        logger.info("测试完成，请验证数据库中是否成功保存数据")
        return 0
    else:
        logger.error("测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 