#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试biz_cloud_health表存储功能
-----------------------------
此脚本用于测试biz_cloud_health表的数据存储功能。

测试内容：
1. 直接调用MediaUploader类的save_to_cloud_health方法
2. 测试用户ID和用户名为空的情况
3. 测试完整参数的情况
"""

import os
import sys
import json
import argparse
import logging
import random
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

# 导入MediaUploader类
from front.media_uploader import MediaUploader

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_biz_cloud_health.log')
    ]
)
logger = logging.getLogger('test_biz_cloud_health')

def test_with_empty_params():
    """
    测试用户ID和用户名为空的情况
    """
    logger.info("=== 测试用户ID和用户名为空的情况 ===")
    
    # 创建MediaUploader实例
    uploader = MediaUploader()
    
    # 连接数据库
    if not uploader.connect_db():
        logger.error("连接数据库失败")
        return False
    
    try:
        # 生成唯一的任务ID
        task_id = f"TEST_BIZ_CLOUD_{int(time.time())}"
        
        # 调用save_to_cloud_health方法，用户ID和用户名为空
        result = uploader.save_to_cloud_health(
            task_id=task_id,
            userid="",
            username="",
            chief_complaint="测试主诉"
        )
        
        logger.info(f"测试结果: {'成功' if result else '失败'}")
        return result
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False
    finally:
        # 关闭数据库连接
        uploader.close_db()

def test_with_full_params():
    """
    测试完整参数的情况
    """
    logger.info("=== 测试完整参数的情况 ===")
    
    # 创建MediaUploader实例
    uploader = MediaUploader()
    
    # 连接数据库
    if not uploader.connect_db():
        logger.error("连接数据库失败")
        return False
    
    try:
        # 生成唯一的任务ID
        task_id = f"TEST_BIZ_CLOUD_{int(time.time())}"
        
        # 调用save_to_cloud_health方法，提供完整参数
        result = uploader.save_to_cloud_health(
            task_id=task_id,
            userid="test_user_001",
            username="测试用户",
            chief_complaint="头痛伴眼部胀痛1周"
        )
        
        logger.info(f"测试结果: {'成功' if result else '失败'}")
        return result
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False
    finally:
        # 关闭数据库连接
        uploader.close_db()

def test_specific_task(task_id, room_name, userid="", username="", chief_complaint="测试主诉"):
    """
    测试process_specific_task方法
    
    Args:
        task_id: 任务ID
        room_name: 诊室名称
        userid: 用户ID
        username: 用户名
        chief_complaint: 主诉
    """
    logger.info(f"=== 测试process_specific_task方法: {task_id} ===")
    
    # 创建MediaUploader实例
    uploader = MediaUploader()
    
    # 创建必要的目录和文件
    test_dir = os.path.join("SeparatedAudioText", room_name, task_id)
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建sentences.json
    sentences_json = [{"speaker": 0, "text": "这是一个测试句子。"}]
    with open(os.path.join(test_dir, "sentences.json"), "w", encoding="utf-8") as f:
        json.dump(sentences_json, f, ensure_ascii=False, indent=2)
    
    # 创建医疗信息文件
    medical_info = {
        "患者主诉": chief_complaint,
        "userid": userid,
        "username": username
    }
    with open(os.path.join(test_dir, "medical_info.json"), "w", encoding="utf-8") as f:
        json.dump(medical_info, f, ensure_ascii=False, indent=2)
    
    # 创建电子病历结果文件
    medical_record = {
        "chief_complaint": chief_complaint,
        "current_illness_history": "患者1周前无明显诱因起病",
        "past_history": "既往有高血压病史",
        "personal_history": "无特殊",
        "tcm_diagnosis": "头痛病"
    }
    with open(os.path.join(test_dir, f"{task_id}.json"), "w", encoding="utf-8") as f:
        json.dump(medical_record, f, ensure_ascii=False, indent=2)
    
    # 调用process_specific_task方法
    result = uploader.process_specific_task(
        room_name=room_name,
        task_id=task_id,
        userid=userid,
        username=username,
        chief_complaint=chief_complaint
    )
    
    logger.info(f"测试结果: {'成功' if result else '失败'}")
    return result

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试biz_cloud_health表存储功能')
    parser.add_argument('--empty', action='store_true', help='测试用户ID和用户名为空的情况')
    parser.add_argument('--full', action='store_true', help='测试完整参数的情况')
    parser.add_argument('--task', action='store_true', help='测试process_specific_task方法')
    
    args = parser.parse_args()
    
    # 如果没有指定任何测试，则运行所有测试
    if not (args.empty or args.full or args.task):
        logger.info("运行所有测试")
        args.empty = True
        args.full = True
        args.task = True
    
    # 运行指定的测试
    if args.empty:
        test_with_empty_params()
    
    if args.full:
        test_with_full_params()
    
    if args.task:
        # 生成唯一的任务ID
        task_id = f"TEST_TASK_{int(time.time())}"
        room_name = "测试诊室"
        
        # 测试用户ID和用户名为空的情况
        test_specific_task(
            task_id=f"{task_id}_empty",
            room_name=room_name
        )
        
        # 测试完整参数的情况
        test_specific_task(
            task_id=f"{task_id}_full",
            room_name=room_name,
            userid="test_user_001",
            username="测试用户",
            chief_complaint="头痛伴眼部胀痛1周"
        )

if __name__ == "__main__":
    main() 