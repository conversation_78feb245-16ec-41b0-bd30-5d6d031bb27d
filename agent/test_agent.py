#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Agent测试工具
-----------
提供用于测试Agent与系统集成的命令行工具
"""

import os
import sys
import json
import logging
import argparse
from pathlib import Path

# 将项目根目录添加到Python路径
script_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(script_dir)
sys.path.insert(0, root_dir)

# 导入Dify Agent客户端
try:
    from agent.dify_client import DifyAgentClient
except ImportError:
    print("错误: 无法导入DifyAgentClient。请确保已创建agent/dify_client.py文件。")
    sys.exit(1)

# 获取统一的日志目录
logs_dir = os.environ.get('AUDIO_SEPARATION_LOGS_DIR', 'logs')
os.makedirs(logs_dir, exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(logs_dir, 'test_agent.log'))  # 使用统一日志目录
    ]
)
logger = logging.getLogger('test_agent')

def find_all_sentence_files():
    """
    查找所有的sentences.json文件
    
    Returns:
        list: 找到的sentences.json文件列表
    """
    result = []
    base_dir = os.path.join(root_dir, 'SeparatedAudioText')
    
    if not os.path.exists(base_dir):
        logger.warning(f"目录不存在: {base_dir}")
        return result
        
    # 遍历SeparatedAudioText目录结构
    for room_dir in os.listdir(base_dir):
        room_path = os.path.join(base_dir, room_dir)
        if not os.path.isdir(room_path):
            continue
            
        for task_dir in os.listdir(room_path):
            task_path = os.path.join(room_path, task_dir)
            if not os.path.isdir(task_path):
                continue
                
            sentences_path = os.path.join(task_path, 'sentences.json')
            if os.path.exists(sentences_path):
                result.append({
                    'room_name': room_dir,
                    'task_id': task_dir,
                    'path': sentences_path
                })
    
    return result

def test_specific_file(file_path, args):
    """
    测试特定的sentences.json文件
    
    Args:
        file_path: 要测试的文件路径
        args: 命令行参数
        
    Returns:
        bool: 是否成功
    """
    logger.info(f"测试文件: {file_path}")
    
    # 创建客户端
    client = DifyAgentClient(
        base_url=args.api_url,
        api_key=args.api_key
    )
    
    # 发送到Agent
    response = client.process_sentences_file(file_path, "streaming")  # 强制使用streaming模式
    
    if response:
        logger.info("成功从Agent获取响应")
        
        # 保存响应
        if args.save:
            output_dir = os.path.dirname(file_path)
            output_path = os.path.join(output_dir, 'agent_response.json')
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(response, f, ensure_ascii=False, indent=2)
                
            logger.info(f"已保存响应到: {output_path}")
            
        return True
    else:
        logger.error("从Agent获取响应失败")
        return False

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='Dify Agent测试工具')
    parser.add_argument('--input', type=str, help='指定的sentences.json文件路径')
    parser.add_argument('--list', action='store_true', help='列出所有可用的sentences.json文件')
    parser.add_argument('--latest', action='store_true', help='测试最新的sentences.json文件')
    parser.add_argument('--all', action='store_true', help='测试所有sentences.json文件')
    parser.add_argument('--mode', type=str, choices=['streaming'], default='streaming', 
                        help='响应模式: 只支持streaming(流式)')
    parser.add_argument('--save', action='store_true', help='保存Agent响应')
    parser.add_argument('--api-key', type=str, default='app-hpL6A5CNAR5HdSf2sO6yvEhn', 
                        help='Dify API的密钥')
    parser.add_argument('--api-url', type=str, default='http://**************:5023', 
                        help='Dify API的URL')
    
    args = parser.parse_args()
    
    # 列出所有可用的sentences.json文件
    if args.list:
        files = find_all_sentence_files()
        if files:
            print(f"找到 {len(files)} 个sentences.json文件:")
            for i, file_info in enumerate(files, 1):
                print(f"{i}. 诊室: {file_info['room_name']}, 任务ID: {file_info['task_id']}")
                print(f"   路径: {file_info['path']}")
                print()
        else:
            print("未找到任何sentences.json文件")
        return
    
    # 测试特定的文件
    if args.input:
        if os.path.exists(args.input):
            test_specific_file(args.input, args)
        else:
            logger.error(f"文件不存在: {args.input}")
        return
    
    # 获取所有sentences.json文件
    files = find_all_sentence_files()
    if not files:
        logger.error("未找到任何sentences.json文件")
        return
    
    # 测试最新的文件
    if args.latest:
        # 按修改时间排序
        files.sort(key=lambda x: os.path.getmtime(x['path']), reverse=True)
        latest_file = files[0]
        logger.info(f"测试最新的文件: {latest_file['path']}")
        test_specific_file(latest_file['path'], args)
        return
    
    # 测试所有文件
    if args.all:
        success_count = 0
        for file_info in files:
            if test_specific_file(file_info['path'], args):
                success_count += 1
                
        logger.info(f"测试完成: {success_count}/{len(files)} 个文件成功")
        return
    
    # 默认行为：显示帮助信息
    parser.print_help()

if __name__ == "__main__":
    main() 