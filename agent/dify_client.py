#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Dify Agent客户端
--------------
提供读取转写结果并与Dify Agent交互的功能
"""

import os
import json
import logging
import requests
import argparse
from typing import Dict, List, Optional, Any, Union

# 获取统一的日志目录
logs_dir = os.environ.get('AUDIO_SEPARATION_LOGS_DIR', 'logs')
os.makedirs(logs_dir, exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(logs_dir, 'agent.log'))  # 使用统一日志目录
    ]
)
logger = logging.getLogger('dify_agent')

class DifyAgentClient:
    """
    Dify Agent客户端
    
    用于读取sentences.json文件并将内容发送给Dify Agent
    """
    
    def __init__(self, base_url: str = "http://**************/v1", api_key: str = "app-XNL08UHTppArxyhycb7aO4c0"):
        """
        初始化Dify Agent客户端
        
        Args:
            base_url: Dify API的基础URL
            api_key: Dify API的密钥
        """
        # 尝试修复URL格式，确保正确的路径
        if not base_url.endswith('/v1'):
            if base_url.endswith('/'):
                base_url = base_url + 'v1'
            else:
                base_url = base_url + '/v1'
                
        self.base_url = base_url
        self.api_key = api_key
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        self.conversation_id = None
    
    def read_sentences_json(self, json_path: str) -> Union[List[Dict], Dict]:
        """
        读取sentences.json文件
        
        Args:
            json_path: sentences.json文件的路径
            
        Returns:
            Dict or List: 文件内容
        """
        try:
            if not os.path.exists(json_path):
                logger.error(f"文件不存在: {json_path}")
                return None
                
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            logger.info(f"成功读取文件: {json_path}")
            return data
            
        except Exception as e:
            logger.error(f"读取JSON文件时发生错误: {str(e)}")
            return None
    
    def format_sentences_for_agent(self, sentences: Union[List[Dict], Dict]) -> str:
        """
        将sentences.json的内容格式化为适合发送给Agent的文本
        注意：此方法已被简化，仅用于兼容性目的。实际格式化在SignalHandler中完成。
        
        Args:
            sentences: 从sentences.json读取的内容或预先格式化的数据
            
        Returns:
            str: 格式化后的文本（JSON字符串）
        """
        if not sentences:
            logger.warning("未找到有效的转写内容")
            return json.dumps([], ensure_ascii=False)
            
        # 直接返回JSON字符串，不做格式化处理
        try:
            # 如果已经是字符串，直接返回
            if isinstance(sentences, str):
                return sentences
                
            # 否则转换为JSON字符串
            return json.dumps(sentences, ensure_ascii=False)
        except Exception as e:
            logger.error(f"格式化句子数据时出错: {str(e)}")
            return json.dumps([], ensure_ascii=False)
    
    def send_to_agent(self, text: str, response_mode: str = "streaming", user: str = "system") -> Optional[Dict]:
        """
        将文本发送给Dify Agent
        
        Args:
            text: 要发送的文本
            response_mode: 响应模式，必须为"streaming"，因为Agent只支持流式响应
            user: 用户标识
            
        Returns:
            Dict: Agent的响应
        """
        try:
            # 强制使用streaming模式，因为Agent不支持blocking模式
            response_mode = "streaming"
            url = f"{self.base_url}/chat-messages"
            
            payload = {
                "query": text,
                "inputs": {},  # 必须包含inputs字段，即使为空
                "response_mode": response_mode,
                "user": user
            }
            
            # 如果存在会话ID，添加到请求中
            if self.conversation_id:
                payload["conversation_id"] = self.conversation_id
            
            # 添加详细的请求日志
            logger.info(f"发送请求到: {url}")
            logger.info(f"请求头: {json.dumps({k: v for k, v in self.headers.items() if k != 'Authorization'}, ensure_ascii=False)}")
            logger.info(f"请求体: {json.dumps(payload, ensure_ascii=False)}")
            
            # 对于流式响应，我们需要手动处理SSE流
            try:
                with requests.post(url, json=payload, headers=self.headers, stream=True) as response:
                    # 记录响应状态
                    logger.info(f"响应状态码: {response.status_code}")
                    if response.status_code != 200:
                        logger.error(f"请求失败，状态码: {response.status_code}, 响应内容: {response.text}")
                        return None
                        
                    response.raise_for_status()
                    
                    full_answer = ""
                    for line in response.iter_lines():
                        if line:
                            line = line.decode('utf-8')
                            # SSE格式是以"data: "开头的行
                            if line.startswith('data: '):
                                data_str = line[6:]  # 去掉"data: "前缀
                                try:
                                    data = json.loads(data_str)
                                    
                                    # 保存会话ID
                                    if "conversation_id" in data and not self.conversation_id:
                                        self.conversation_id = data["conversation_id"]
                                        logger.info(f"已保存会话ID: {self.conversation_id}")
                                    
                                    # 处理不同类型的事件
                                    if data.get("event") == "agent_message":
                                        answer_chunk = data.get("answer", "")
                                        full_answer += answer_chunk
                                        print(answer_chunk, end="", flush=True)
                                        
                                    elif data.get("event") == "message":
                                        answer_chunk = data.get("answer", "")
                                        full_answer += answer_chunk
                                        print(answer_chunk, end="", flush=True)
                                        
                                    elif data.get("event") == "message_end":
                                        print("\n")  # 消息结束，换行
                                        if "metadata" in data:
                                            logger.info(f"元数据: {data['metadata']}")
                                            
                                    elif data.get("event") == "error":
                                        logger.error(f"错误: {data.get('message')}")
                                        
                                    elif data.get("event") == "ping":
                                        logger.debug("收到ping事件")
                                        
                                    else:
                                        logger.debug(f"其他事件: {data.get('event')}, 数据: {data_str[:100]}...")
                                except json.JSONDecodeError:
                                    logger.error(f"无法解析JSON: {data_str[:200]}")
                    
                    logger.info(f"收到完整回答: {full_answer}")
                    return {"conversation_id": self.conversation_id, "answer": full_answer}
            except requests.exceptions.RequestException as e:
                logger.error(f"流式请求异常: {str(e)}")
                return None
                    
        except requests.exceptions.RequestException as e:
            logger.error(f"发送请求时发生错误: {str(e)}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"解析响应时发生错误: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"未知错误: {str(e)}")
            return None
    
    def process_sentences_file(self, json_path: str, response_mode: str = "streaming") -> Optional[Dict]:
        """
        处理sentences.json文件并将内容发送给Dify Agent
        
        Args:
            json_path: sentences.json文件的路径
            response_mode: 响应模式，注意Agent只支持streaming模式
            
        Returns:
            Dict: Agent的响应
        """
        sentences = self.read_sentences_json(json_path)
        if not sentences:
            return None
            
        formatted_text = self.format_sentences_for_agent(sentences)
        return self.send_to_agent(formatted_text, "streaming")  # 强制使用streaming模式

def main():
    """
    主函数，用于解析命令行参数并执行操作
    """
    parser = argparse.ArgumentParser(description='Dify Agent客户端')
    parser.add_argument('--input', type=str, required=True, help='sentences.json文件的路径')
    parser.add_argument('--mode', type=str, default='streaming', choices=['streaming'], 
                        help='响应模式: 只支持streaming(流式)')
    parser.add_argument('--api-key', type=str, default='app-hpL6A5CNAR5HdSf2sO6yvEhn', 
                        help='Dify API的密钥')
    parser.add_argument('--base-url', type=str, default='http://**************/v1', 
                        help='Dify API的基础URL')
    
    args = parser.parse_args()
    
    # 创建客户端
    client = DifyAgentClient(base_url=args.base_url, api_key=args.api_key)
    
    # 处理sentences.json并发送给Agent
    client.process_sentences_file(args.input, args.mode)

if __name__ == "__main__":
    main() 