#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import sys
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).resolve().parent.parent))

from agent.dify_client import DifyAgentClient
from stream_capture.core.signal.signal_handler import SignalHandler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_response_format.log')
    ]
)

logger = logging.getLogger(__name__)

def test_response_saving(test_dir="test_responses"):
    """测试不同类型响应的保存格式"""
    try:
        # 创建测试目录
        os.makedirs(test_dir, exist_ok=True)
        
        # 模拟Agent的响应
        mock_responses = [
            {
                "conversation_id": "test-conv-1",
                "answer": json.dumps({
                    "summary": "这是一个JSON格式的总结",
                    "details": ["点1", "点2", "点3"],
                    "recommendation": "这是一个建议"
                }, ensure_ascii=False),
                "task_id": "json_response"
            },
            {
                "conversation_id": "test-conv-2",
                "answer": "这是一个普通文本格式的回复，没有JSON结构。",
                "task_id": "text_response"
            }
        ]
        
        # 初始化SignalHandler
        signal_handler = SignalHandler()
        
        # 手动为测试设置dify_client属性
        signal_handler.dify_client = DifyAgentClient()
        
        # 测试保存响应功能
        for response in mock_responses:
            task_id = response["task_id"]
            test_path = os.path.join(test_dir, f"sentences_{task_id}.json")
            task_dir = os.path.join(test_dir, task_id)
            
            # 创建任务目录
            os.makedirs(task_dir, exist_ok=True)
            
            # 模拟测试转写文件
            with open(test_path, 'w', encoding='utf-8') as f:
                json.dump([{"speaker": "医生", "text": "测试文本"}], f, ensure_ascii=False)
            
            logger.info(f"测试场景: {task_id}")
            
            # 手动提取answer内容
            answer_content = response.get('answer', '')
            
            # 模拟保存响应
            # 保存完整响应到agent_response.json
            compat_path = os.path.join(task_dir, 'agent_response.json')
            with open(compat_path, 'w', encoding='utf-8') as f:
                json.dump(response, f, ensure_ascii=False, indent=2)
            logger.info(f"完整响应已保存到: {compat_path}")
            
            # 只保存answer内容到task_id.json
            agent_response_path = os.path.join(task_dir, f"{task_id}.json")
            
            # 尝试解析回复内容是否为JSON
            try:
                # 尝试解析为JSON对象
                json_content = json.loads(answer_content)
                # 如果成功解析为JSON，直接以JSON格式保存
                with open(agent_response_path, 'w', encoding='utf-8') as f:
                    # 使用json.dump保存，以保持格式完全一致
                    json.dump(json_content, f, ensure_ascii=False, indent=2)
                logger.info(f"已保存JSON格式回复到: {agent_response_path}")
            except json.JSONDecodeError:
                # 如果不是JSON格式，直接保存原始字符串
                with open(agent_response_path, 'w', encoding='utf-8') as f:
                    f.write(answer_content)
                logger.info(f"已保存文本格式回复到: {agent_response_path}")
            
            # 验证保存结果
            validate_saved_files(task_dir, task_id, response)
    
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}", exc_info=True)

def validate_saved_files(task_dir, task_id, original_response):
    """验证保存的文件格式是否正确"""
    try:
        # 验证agent_response.json
        compat_path = os.path.join(task_dir, 'agent_response.json')
        with open(compat_path, 'r', encoding='utf-8') as f:
            saved_response = json.load(f)
        
        if saved_response != original_response:
            logger.error(f"完整响应保存不一致: {compat_path}")
        else:
            logger.info(f"完整响应验证通过: {compat_path}")
        
        # 验证task_id.json
        task_path = os.path.join(task_dir, f"{task_id}.json")
        answer_content = original_response.get('answer', '')
        
        try:
            # 如果原始answer是JSON格式的
            json_content = json.loads(answer_content)
            
            # 读取保存的task_id.json
            with open(task_path, 'r', encoding='utf-8') as f:
                saved_content = json.load(f)
            
            if saved_content != json_content:
                logger.error(f"JSON格式回复保存不一致: {task_path}")
            else:
                logger.info(f"JSON格式回复验证通过: {task_path}")
                
        except json.JSONDecodeError:
            # 如果原始answer是纯文本格式的
            with open(task_path, 'r', encoding='utf-8') as f:
                saved_content = f.read()
            
            if saved_content != answer_content:
                logger.error(f"文本格式回复保存不一致: {task_path}")
            else:
                logger.info(f"文本格式回复验证通过: {task_path}")
    
    except Exception as e:
        logger.error(f"验证文件时发生错误: {str(e)}", exc_info=True)

if __name__ == "__main__":
    test_response_saving()
    logger.info("测试完成，请检查test_responses目录下的文件") 