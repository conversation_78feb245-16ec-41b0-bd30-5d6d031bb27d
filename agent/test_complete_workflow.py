#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
完整工作流测试脚本
-----------
用于测试从生成转写结果到发送给Agent并保存到数据库的全过程
"""

import os
import sys
import json
import time
import shutil
import logging
import argparse
import requests
import random
from pathlib import Path

# 将项目根目录添加到Python路径
script_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(script_dir)
sys.path.append(root_dir)

# 导入信号处理器
from stream_capture.core.signal.signal_handler import SignalHandler

# 获取统一的日志目录
logs_dir = os.environ.get('AUDIO_SEPARATION_LOGS_DIR', 'logs')
os.makedirs(logs_dir, exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(logs_dir, 'test_workflow.log'))
    ]
)
logger = logging.getLogger('test_workflow')

def generate_test_sentences_json(output_dir, task_id):
    """
    生成测试用的sentences.json文件
    
    Args:
        output_dir: 输出目录
        task_id: 任务ID
        
    Returns:
        str: 生成的文件路径
    """
    try:
        # 确保目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 示例对话数据
        test_data = {
            "dialogue": [
                {
                    "role": "医生",
                    "content": "您好，今天有什么不舒服？"
                },
                {
                    "role": "患者",
                    "content": "医生，我这两天胃有点疼，吃东西后感觉特别难受。"
                },
                {
                    "role": "医生",
                    "content": "疼痛是什么性质的？比如说是烧灼感还是钝痛？"
                },
                {
                    "role": "患者",
                    "content": "感觉是烧灼感，吃完饭后会更加严重。"
                },
                {
                    "role": "医生",
                    "content": "什么时候开始的症状？之前有过类似情况吗？"
                },
                {
                    "role": "患者",
                    "content": "大概三天前开始的，以前偶尔也会这样，但没有这次严重。"
                },
                {
                    "role": "医生",
                    "content": "最近饮食有什么变化吗？比如吃了刺激性食物、饮酒或者不规律饮食？"
                },
                {
                    "role": "患者",
                    "content": "上周末朋友聚会，吃了烧烤，喝了点啤酒。"
                },
                {
                    "role": "医生",
                    "content": "还有其他症状吗？比如恶心、呕吐、打嗝或者大便习惯改变？"
                },
                {
                    "role": "患者",
                    "content": "有时会打嗝，早上起床有点恶心，但没有呕吐。大便正常。"
                },
                {
                    "role": "医生",
                    "content": "目前看起来可能是胃炎，建议您做个胃镜检查确认一下。现在我给您开点药先缓解症状。记得最近饮食要清淡，不要吃辛辣刺激性食物，禁酒。"
                },
                {
                    "role": "患者",
                    "content": "好的，医生，谢谢您。"
                }
            ]
        }
        
        # 保存到文件
        file_path = os.path.join(output_dir, 'sentences.json')
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"成功生成测试句子文件: {file_path}")
        return file_path
    
    except Exception as e:
        logger.error(f"生成测试句子文件时发生错误: {str(e)}")
        return None

def create_dummy_mp4_file(output_dir, task_id):
    """
    创建一个空的MP4文件，用于模拟录制的视频
    
    Args:
        output_dir: 输出目录
        task_id: 任务ID
        
    Returns:
        str: 创建的文件路径
    """
    try:
        # 确保目录存在
        os.makedirs("stream_capture/recordings", exist_ok=True)
        recordings_dir = os.path.join("stream_capture/recordings", output_dir)
        os.makedirs(recordings_dir, exist_ok=True)
        
        # 示例视频文件路径
        file_path = os.path.join(recordings_dir, f"{task_id}.mp4")
        
        # 创建一个空的MP4文件
        with open(file_path, 'wb') as f:
            # 写入一个简单的MP4头部
            f.write(b'\x00\x00\x00\x18ftypmp42\x00\x00\x00\x00mp42mp41\x00\x00\x00\x00moov')
        
        logger.info(f"成功创建模拟视频文件: {file_path}")
        return file_path
    
    except Exception as e:
        logger.error(f"创建模拟视频文件时发生错误: {str(e)}")
        return None

def mock_agent_response():
    """
    模拟Agent的响应
    
    Returns:
        dict: 模拟的Agent响应
    """
    # 示例电子病例数据
    return {
        "chief_complaint": "胃痛伴烧灼感2-3天",
        "current_illness_history": "患者2-3天前开始出现胃部疼痛，呈烧灼样，饭后症状加重。伴有打嗝、早晨恶心但无呕吐。症状出现前有饮酒和食用烧烤等刺激性食物史。",
        "past_history": "偶尔有类似症状但未曾这么严重",
        "personal_history": "有饮酒和食用刺激性食物史",
        "menstrual_history": "",
        "allergy_records": "",
        "auxiliary_examination": "建议胃镜检查",
        "presumptive_symptoms": "",
        "tcm_diagnosis": "胃痛-胃热证",
        "treatment_recommendations": "1. 饮食清淡，避免辛辣刺激性食物；2. 禁酒；3. 规律饮食；4. 服用抑酸和胃黏膜保护药物。",
        "tcm_diagnosis_type": "胃热证",
        "western_diagnosis": "急性胃炎",
        "blood_glucose_type": "",
        "blood_glucose_value": "",
        "disease_location": "胃",
        "disease_nature": "热",
        "clinical_tests": "建议胃镜",
        "clinical_examination": "上腹部轻压痛",
        "treatment_method": "清热和胃",
        "western_medication": "质子泵抑制剂、胃黏膜保护剂",
        "clinical_treatment": "对症治疗"
    }

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='完整工作流测试工具')
    parser.add_argument('--task-id', type=str, help='任务ID，默认使用随机生成的ID', 
                       default=f"TEST_{int(time.time())}")
    parser.add_argument('--room', type=str, help='诊室名称', default='测试诊室')
    
    args = parser.parse_args()
    
    task_id = args.task_id
    room_name = args.room
    
    logger.info(f"开始测试完整工作流，任务ID: {task_id}，诊室: {room_name}")
    
    # 步骤1: 创建模拟视频文件
    create_dummy_mp4_file(room_name, task_id)
    
    # 步骤2: 生成转写结果文件
    output_dir = os.path.join("SeparatedAudioText", room_name, task_id)
    sentences_json_path = generate_test_sentences_json(output_dir, task_id)
    
    if not sentences_json_path:
        logger.error("生成测试文件失败，测试中止")
        return
    
    # 步骤3: 创建模拟的Agent响应
    agent_response = mock_agent_response()
    agent_response_path = os.path.join(output_dir, "agent_response.json")
    agent_pure_response_path = os.path.join(output_dir, f"{task_id}.json")
    
    # 保存模拟的Agent响应
    with open(agent_response_path, "w", encoding="utf-8") as f:
        json.dump({"response": agent_response}, f, ensure_ascii=False, indent=2)
    
    with open(agent_pure_response_path, "w", encoding="utf-8") as f:
        json.dump(agent_response, f, ensure_ascii=False, indent=2)
    
    logger.info(f"已保存模拟的Agent响应: {agent_response_path}")
    
    # 步骤4: 手动调用信号处理器的_send_result_to_hsi方法（现在是兼容方法，实际保存到数据库）
    logger.info("初始化信号处理器")
    handler = SignalHandler()
    
    logger.info(f"正在调用_send_result_to_hsi方法，该方法现在会将电子病历结果保存到数据库，任务ID: {task_id}")
    result = handler._send_result_to_hsi(task_id, agent_response)
    
    if result:
        logger.info(f"成功调用兼容方法：电子病历结果已保存到数据库中: {task_id}")
    else:
        logger.error(f"兼容方法调用失败: {task_id}")
    
    # 步骤5: 也可以模拟handle_transcript_ready方法的调用
    logger.info(f"正在模拟handle_transcript_ready方法的调用，任务ID: {task_id}")
    handler.handle_transcript_ready(task_id, room_name)
    
    logger.info("完整工作流测试完成")

if __name__ == "__main__":
    main() 