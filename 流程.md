# 医患交互对话识别系统流程

## 系统概述
本项目的主要功能是识别医患交互对话，通过视频流捕获、音频提取和语音识别，实现医疗场景中的对话内容记录与分析。

## 工作流程

1. 诊室的电脑通过流媒体url推送视频流到服务器，服务器会通过ffmpeg，持续捕获视频流（仅捕获不保存）。

2. 在接受推流的同时，服务器会接受HSI系统发送的"开始"和"停止"信号：
   - 当HSI系统发送"开始"信号时，服务器开始保存视频流，"开始"信号包含：病人姓名、病人身份证、任务ID、开始时间等信息
   - 当HSI系统发送"停止"信号时，服务器停止保存视频流，"停止"信号包含：结束时间等信息
   - 服务器通过复制的方式，将捕获的视频流保存为MP4格式

3. 服务器为了区分不同病房推送的不同视频流，定义了诊室电脑MAC-诊室名称-流媒体URL的关系映射，这样服务器就可以根据流媒体的URL判断，是哪个诊室的推流视频，避免混淆。

4. 调用backend中的算法，处理已保存的MP4视频。

5. 将处理的结果，比如分割的视频、音频、语音识别结果保存到数据库。

## 开发语音识别系统：

1. 视频流捕获模块：
   - 实现基于ffmpeg的视频流实时捕获功能（仅捕获不保存）
   - 支持根据URL接收和处理流媒体数据
   - 确保捕获过程的稳定性和低延迟

2. 信号处理模块：
   - 开发接收HSI系统"开始"和"停止"信号的接口
   - "开始"信号处理：接收并解析病人姓名、身份证、任务ID、开始时间
   - "停止"信号处理：接收并解析结束时间
   - 实现信号触发的视频保存控制逻辑
   - 根据接收到的信息生成视频文件的命名和存储路径

3. 映射查询功能：
   - 开发根据流媒体URL查询诊室信息的功能
   - 实现诊室视频流的区分和归类
   - 维护MAC地址-诊室名称-流媒体URL的映射关系

4. 音频提取与语音识别：
   - 从捕获的MP4视频中提取音频
   - 调用语音识别API或算法进行转写
   - 处理识别结果并规范化输出
   - 优化医患对话场景下的识别准确率

5. 数据持久化：
   - 设计数据库结构，存储视频、音频和识别结果
   - 关联患者信息、任务ID与识别结果
   - 实现数据的存取和管理功能
   - 提供查询接口支持后续分析和应用


1.实时接受视频流，但是在保存视频流的时候，需要根据HSI系统发送的"开始"和"停止"信号，来确定视频流的开始和结束。
2.保存为.mp4文件后，需要主动请求app_api.py中的接口，进行音频的分割和语音识别。其中app_api.py在后台常驻。

# 实施建议和优化方向
def implementation_recommendations():
    """
    实施建议:
    
    1. 阶段性实施:
       - 第一阶段: 将app_api.py中的处理逻辑封装为可导入模块
       - 第二阶段: 修改main.py添加模型预加载
       - 第三阶段: 修改signal_handler实现自动触发处理
    
    2. 性能优化:
       - 考虑增加缓存机制，避免重复处理相同视频
       - 根据系统资源情况动态调整处理并发数
       - 考虑添加处理进度通知和状态查询接口
    
    3. 系统扩展:
       - 设计可插拔的处理模块架构，便于未来功能扩展
       - 考虑添加分布式处理能力，处理大规模视频文件
       - 考虑添加失败重试队列
    """
    return "建议分阶段实施，注重性能优化和系统稳定性"