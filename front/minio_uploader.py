#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import requests
from typing import Optional


def upload_to_minio(file_path: str, biz_path: Optional[str] = None) -> str:
    """
    上传文件到Minio对象存储并返回URL
    
    参数:
        file_path: 要上传的文件路径
        biz_path: 业务路径，如不提供则根据文件类型自动判断
        
    返回:
        str: 上传成功后的文件URL
    """
    try:
        url = "http://10.200.116.173:9033/cdu/sys/upload/uploadMinio"
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        # 如果未提供业务路径，根据文件类型自动判断
        if biz_path is None:
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext in ['.wav', '.mp3', '.mp4', '.avi', '.mov']:
                biz_path = 'video'
            elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                biz_path = 'image'
            elif file_ext in ['.pth', '.ckpt', '.pt', '.bin', '.onnx']:
                biz_path = 'model'
            elif file_ext in ['.txt', '.pdf', '.doc', '.docx', '.xls', '.xlsx']:
                biz_path = 'document'
            elif file_ext in ['.mid', '.midi']:
                biz_path = 'music'
            else:
                biz_path = 'other'
        
        # 强制检查：确保视频文件使用正确的业务路径
        # 修复前面的错误：传入自定义业务路径不能与文件类型冲突
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext in ['.wav', '.mp3', '.mp4', '.avi', '.mov']:
            # 对于视频和音频文件，使用专用参数
            custom_bizpath = biz_path  # 保存自定义业务路径
            biz_path = 'video'  # 强制使用video类型
        else:
            custom_bizpath = None
            
        print(f"正在上传文件: {file_path}")
        print(f"业务路径: {biz_path}")
        
        # 准备 multipart/form-data 请求
        with open(file_path, 'rb') as f:
            files = {
                'file': (os.path.basename(file_path), f, 'application/octet-stream')
            }
            
            params = {
                'bizPath': biz_path
            }
            
            # 添加自定义业务路径作为额外参数
            if custom_bizpath:
                params['customPath'] = custom_bizpath
            
            headers = {
                'X-Access-Token': 'global'
            }
            
            # 发送请求
            response = requests.post(
                url,
                files=files,
                params=params,
                headers=headers,
                timeout=120  # 增加超时时间到2分钟，大文件可能需要更长时间
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('errno') == 0 and 'data' in result:
                    file_url = result['data'].get('url')
                    if file_url:
                        print(f"上传成功! URL: {file_url}")
                        return file_url
                    else:
                        raise ValueError("响应中没有URL信息")
                else:
                    error_msg = result.get('message', '未知错误')
                    print(f"服务器返回错误: {error_msg}")
                    print(f"完整响应: {result}")
                    raise ValueError(f"上传失败: {error_msg}")
            else:
                print(f"HTTP请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                raise ValueError(f"请求失败，状态码: {response.status_code}, 响应: {response.text}")
                
    except Exception as e:
        print(f"上传文件到Minio时出错: {str(e)}")
        raise


def main():
    """主函数，处理命令行参数并执行上传"""
    parser = argparse.ArgumentParser(description='上传文件到Minio对象存储')
    parser.add_argument('file_path', help='要上传的文件路径')
    parser.add_argument('--biz-path', '-b', help='业务路径 (video/image/model/music/document等)')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细信息')
    
    args = parser.parse_args()
    
    try:
        file_url = upload_to_minio(args.file_path, args.biz_path)
        print(f"上传成功！")
        print(f"文件URL: {file_url}")
        return 0
    except Exception as e:
        print(f"上传失败: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 