# 医患对话数据库设计方案

## 表结构设计

本设计方案包含两张表，用于存储医患对话的视频、音频和转写内容。

### 1. 医患对话记录表(medical_conversation)

此表用于存储每次医患对话的基本信息，包括任务ID、诊室名称、完整视频URL以及转写内容。

```sql
CREATE TABLE IF NOT EXISTS `medical_conversation` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_id` varchar(64) NOT NULL COMMENT '任务ID',
    `room_name` varchar(64) DEFAULT NULL COMMENT '诊室名称',
    `full_video_url` varchar(512) DEFAULT NULL COMMENT '完整视频URL',
    `sentences_json` text COMMENT '转写内容JSON',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_id` (`task_id`),
    KEY `idx_room_name` (`room_name`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='医患对话记录表';
```

字段说明：
- `id`: 自增主键，用于唯一标识每条记录
- `task_id`: 任务ID，设置为唯一键，与文件系统中的目录名一致
- `room_name`: 诊室名称，如"A402诊室"
- `full_video_url`: 存储在MinIO中的完整视频URL
- `sentences_json`: 存储sentences.json的转写内容
- `create_time`: 记录创建时间
- `update_time`: 记录更新时间

### 2. 对话说话人视频片段表(conversation_speaker_clips)

此表用于存储每个说话人的视频片段URL，与医患对话记录表通过task_id关联。

```sql
CREATE TABLE IF NOT EXISTS `conversation_speaker_clips` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_id` varchar(64) NOT NULL COMMENT '关联的任务ID',
    `speaker_id` int(11) NOT NULL COMMENT '说话人ID',
    `segment_id` varchar(64) DEFAULT NULL COMMENT '视频片段标识(如segment_1)',
    `clip_url` varchar(512) NOT NULL COMMENT '视频片段URL',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_speaker_segment` (`task_id`, `speaker_id`, `segment_id`),
    KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对话说话人视频片段表';
```

字段说明：
- `id`: 自增主键
- `task_id`: 关联的任务ID，与medical_conversation表关联
- `speaker_id`: 说话人ID，与sentences.json中的speaker字段对应
- `segment_id`: 视频片段标识，如"segment_1"，用于区分同一说话人的不同视频片段
- `clip_url`: 说话人视频片段在MinIO中的URL
- `create_time`: 记录创建时间

## 表关系

两张表通过`task_id`字段关联：
- `medical_conversation`表存储每次对话的基本信息
- `conversation_speaker_clips`表存储每个说话人的各视频片段信息，一个说话人可能有多个视频片段

## 数据流程

1. 从`stream_capture/recordings/[诊室名称]/[任务ID].mp4`获取完整视频，上传到MinIO，将返回的URL存入`medical_conversation.full_video_url`
2. 从`SeparatedAudioText/[诊室名称]/[任务ID]/sentences.json`读取转写内容，存入`medical_conversation.sentences_json`
3. 从`SeparatedAudioText/[诊室名称]/[任务ID]/speaker_[0-9]/`获取各个说话人的所有视频片段，上传到MinIO
4. 提取视频片段的`segment_id`（如"segment_1"），将每个说话人的每个视频片段URL和segment_id一同存入`conversation_speaker_clips`表

## 查询示例

1. 获取某个诊室的所有对话记录：
```sql
SELECT * FROM medical_conversation 
WHERE room_name = 'A402诊室' 
ORDER BY create_time DESC;
```

2. 获取某次对话的所有说话人视频片段：
```sql
SELECT * FROM conversation_speaker_clips 
WHERE task_id = 'TASK2023110100006' 
ORDER BY speaker_id, segment_id;
```

3. 获取特定说话人的所有视频片段：
```sql
SELECT * FROM conversation_speaker_clips 
WHERE task_id = 'TASK2023110100006' AND speaker_id = 0
ORDER BY segment_id;
```

4. 获取完整的对话信息（包括所有说话人片段）：
```sql
SELECT mc.*, csc.speaker_id, csc.segment_id, csc.clip_url 
FROM medical_conversation mc
LEFT JOIN conversation_speaker_clips csc ON mc.task_id = csc.task_id
WHERE mc.task_id = 'TASK2023110100006'
ORDER BY csc.speaker_id, csc.segment_id;
```

## 索引说明

1. `medical_conversation`表：
   - 主键索引：`id`
   - 唯一索引：`task_id`
   - 普通索引：`room_name`、`create_time`

2. `conversation_speaker_clips`表：
   - 主键索引：`id`
   - 唯一索引：(`task_id`, `speaker_id`, `segment_id`)组合唯一索引
   - 普通索引：`task_id`

这些索引的设计目的是优化常用查询场景的性能。

## 工具使用说明

### 1. 数据库表创建/升级工具 (create_tables.py)

```bash
# 创建数据库表
python front/create_tables.py

# 删除并重新创建表
python front/create_tables.py --drop

# 升级现有表结构
python front/create_tables.py --upgrade
```

### 2. 媒体文件上传与存储工具 (media_uploader.py)

```bash
# 列出所有可处理的任务
python front/media_uploader.py --list

# 处理特定任务
python front/media_uploader.py --room "A402诊室" --task "TASK2023110100006"

# 处理所有任务
python front/media_uploader.py --all

# 指定项目根目录
python front/media_uploader.py --all --base-dir "/data/AudioSeparation"
```

### 3. 数据库变更测试工具 (test_db_changes.py)

```bash
# 检查表结构
python front/test_db_changes.py --structure

# 插入测试数据
python front/test_db_changes.py --insert

# 查询测试数据
python front/test_db_changes.py --query

# 执行所有测试
python front/test_db_changes.py
```

## 注意事项

1. 数据库使用utf8mb4字符集，支持完整的Unicode字符（包括表情符号）
2. `task_id`在两张表之间形成逻辑外键关系，但未设置物理外键约束，以提高性能
3. 视频和音频文件本身不存储在数据库中，而是上传到MinIO对象存储服务，数据库中仅存储URL
4. `sentences_json`字段使用text类型，可存储完整的转写内容
5. segment_id字段用于存储视频片段的唯一标识，通常以"segment_1"、"segment_2"等格式命名
6. 一个说话人(speaker_id)可能有多个视频片段(segment_id)，每个片段都有自己的URL
7. 如果需要从现有数据库升级，请使用`create_tables.py --upgrade`命令

## 更新历史

### 2024-04-03
1. 将数据库从`jeecg-boot-smartward`更改为`chd6_4_yph_test`
2. 在`medical_conversation`表中新增`id`字段作为自增主键
3. 修改`task_id`字段从主键变为唯一键
4. 添加测试脚本`test_db_changes.py`用于验证表结构和功能 