-- 房间流媒体映射表
CREATE TABLE IF NOT EXISTS `room_stream_mapping` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_name` varchar(100) NOT NULL COMMENT '诊室名称',
  `mac_address` varchar(50) NOT NULL COMMENT 'IP地址，用于标识设备',
  `stream_url` varchar(255) NOT NULL COMMENT '流媒体URL',
  `description` varchar(255) DEFAULT NULL COMMENT '描述信息',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活，1=激活，0=禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_name` (`room_name`),
  UNIQUE KEY `uk_mac_address` (`mac_address`),
  UNIQUE KEY `uk_stream_url` (`stream_url`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='诊室流媒体映射表';

-- 导入初始数据
INSERT INTO `room_stream_mapping` (`room_name`, `mac_address`, `stream_url`, `description`)
VALUES 
('A402诊室', '70:85:c4:61:66:78', 'https://livell.cdutcm.edu.cn/live/f2ya.live.flv', 'A402诊室固定设备'),
('笔记本电脑', 'e4:60:17:1d:42:b0', 'https://livell.cdutcm.edu.cn/live/efy1a.live.flv', '移动笔记本电脑')
ON DUPLICATE KEY UPDATE
`mac_address` = VALUES(`mac_address`),
`stream_url` = VALUES(`stream_url`),
`description` = VALUES(`description`),
`is_active` = 1;

-- 查询示例
-- SELECT * FROM `room_stream_mapping` WHERE `mac_address` = '70:85:c4:61:66:78';
-- SELECT * FROM `room_stream_mapping` WHERE `room_name` = 'A402诊室';
-- SELECT * FROM `room_stream_mapping` WHERE `stream_url` = 'https://livell.cdutcm.edu.cn/live/f2ya.live.flv'; 