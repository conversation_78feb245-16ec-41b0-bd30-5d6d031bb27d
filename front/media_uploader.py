#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
视频文件上传与数据库存储工具
---------------------------
这个脚本用于:
1. 上传SeparatedAudioText目录下的各说话人视频片段到MinIO
2. 上传stream_capture/recordings目录下的完整视频到MinIO
3. 将视频信息和转写内容存储到数据库中
"""

import os
import sys
import json
import argparse
import logging
import pymysql
import re
import time
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import random

# 导入MinIO上传模块
from minio_uploader import upload_to_minio

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('media_uploader.log')
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    "host": "*************",
    "port": 3307,
    "user": "sgkj",
    "password": "cdutcm@123",
    "database": "chd6_4_yph_test",
    "charset": "utf8mb4"
}

class MediaUploader:
    """
    媒体文件上传器
    处理视频上传和数据库存储
    """
    
    def __init__(self, base_dir: str = None):
        """
        初始化上传器
        
        Args:
            base_dir: 项目根目录路径，如不提供则使用当前目录
        """
        self.base_dir = base_dir if base_dir else os.getcwd()
        self.separated_audio_dir = os.path.join(self.base_dir, "SeparatedAudioText")
        self.recordings_dir = os.path.join(self.base_dir, "stream_capture", "recordings")
        
        # 检查目录是否存在
        if not os.path.exists(self.separated_audio_dir):
            logger.warning(f"SeparatedAudioText目录不存在: {self.separated_audio_dir}")
        
        if not os.path.exists(self.recordings_dir):
            logger.warning(f"recordings目录不存在: {self.recordings_dir}")
        
        # 数据库连接
        self.conn = None
        self.cursor = None
        
    def connect_db(self) -> bool:
        """
        连接到数据库
        
        Returns:
            bool: 是否成功连接
        """
        try:
            self.conn = pymysql.connect(
                host=DB_CONFIG["host"],
                port=DB_CONFIG["port"],
                user=DB_CONFIG["user"],
                password=DB_CONFIG["password"],
                database=DB_CONFIG["database"],
                charset=DB_CONFIG["charset"]
            )
            self.cursor = self.conn.cursor()
            logger.info("成功连接到数据库")
            return True
        except Exception as e:
            logger.error(f"连接数据库失败: {str(e)}")
            return False
    
    def close_db(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
            logger.info("数据库连接已关闭")
    
    def read_sentences_json(self, file_path: str) -> str:
        """
        读取sentences.json文件内容
        
        Args:
            file_path: sentences.json文件路径
            
        Returns:
            str: 文件内容的JSON字符串
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # 读取JSON并验证格式
                content = json.load(f)
                # 返回格式化后的JSON字符串
                return json.dumps(content, ensure_ascii=False)
        except Exception as e:
            logger.error(f"读取sentences.json文件失败: {str(e)}")
            return ""
    
    def _try_upload_with_retry(self, file_path: str, biz_path: str, max_retries: int = 3) -> Optional[str]:
        """
        尝试上传文件并在失败时重试
        
        Args:
            file_path: 文件路径
            biz_path: 业务路径
            max_retries: 最大重试次数
            
        Returns:
            Optional[str]: 上传成功后的URL，失败返回None
        """
        retry_count = 0
        last_error = None
        
        while retry_count < max_retries:
            try:
                if retry_count > 0:
                    logger.info(f"重试上传 ({retry_count}/{max_retries}): {file_path}")
                    # 在重试前等待一段时间
                    time.sleep(2 * retry_count)  # 逐渐增加等待时间
                
                # 调用上传函数，对视频文件始终使用"video"业务路径
                return upload_to_minio(file_path, biz_path)
                
            except Exception as e:
                retry_count += 1
                last_error = str(e)
                logger.warning(f"上传失败 (尝试 {retry_count}/{max_retries}): {last_error}")
        
        logger.error(f"上传失败，已达到最大重试次数: {file_path}")
        logger.error(f"最后一次错误: {last_error}")
        return None
    
    def upload_full_video(self, room_name: str, task_id: str) -> Optional[str]:
        """
        上传完整视频文件
        
        Args:
            room_name: 诊室名称
            task_id: 任务ID
            
        Returns:
            Optional[str]: 上传成功后的URL，失败返回None
        """
        try:
            # 构建视频文件路径
            video_path = os.path.join(self.recordings_dir, room_name, f"{task_id}.mp4")
            
            if not os.path.exists(video_path):
                logger.warning(f"完整视频文件不存在: {video_path}")
                return None
            
            # 上传到MinIO - 使用正确的业务路径"video"
            logger.info(f"开始上传完整视频: {video_path}")
            url = self._try_upload_with_retry(video_path, "video")
            
            if url:
                logger.info(f"完整视频上传成功: {url}")
                return url
            else:
                logger.error(f"完整视频上传失败，重试后仍然失败: {video_path}")
                return None
                
        except Exception as e:
            logger.error(f"上传完整视频失败: {str(e)}")
            return None
    
    def upload_speaker_clips(self, room_name: str, task_id: str) -> List[Dict[str, Any]]:
        """
        上传说话人视频片段
        
        Args:
            room_name: 诊室名称
            task_id: 任务ID
            
        Returns:
            List[Dict[str, Any]]: 说话人视频片段信息列表，每项包含speaker_id、segment_id和clip_url
        """
        result = []
        
        try:
            # 构建任务目录路径
            task_dir = os.path.join(self.separated_audio_dir, room_name, task_id)
            
            if not os.path.exists(task_dir):
                logger.warning(f"任务目录不存在: {task_dir}")
                return result
            
            # 查找所有speaker_目录
            for item in os.listdir(task_dir):
                if item.startswith("speaker_") and os.path.isdir(os.path.join(task_dir, item)):
                    try:
                        # 提取说话人ID
                        speaker_id = int(item.split("_")[1])
                        
                        # 查找目录中的MP4文件
                        speaker_dir = os.path.join(task_dir, item)
                        mp4_files = [f for f in os.listdir(speaker_dir) if f.endswith(".mp4")]
                        
                        if not mp4_files:
                            logger.warning(f"在目录 {speaker_dir} 中未找到MP4文件")
                            continue
                        
                        # 上传每个MP4文件
                        for mp4_file in mp4_files:
                            clip_path = os.path.join(speaker_dir, mp4_file)
                            
                            # 提取segment_id (如 segment_1, segment_2 等)
                            segment_id = None
                            segment_match = re.match(r'(segment_\d+)\.mp4', mp4_file, re.IGNORECASE)
                            if segment_match:
                                segment_id = segment_match.group(1)
                            else:
                                # 如果文件名不是segment_格式，使用不带扩展名的文件名作为segment_id
                                segment_id = os.path.splitext(mp4_file)[0]
                            
                            # 上传到MinIO - 使用正确的业务路径"video"
                            logger.info(f"开始上传说话人 {speaker_id} 的视频片段 {segment_id}: {clip_path}")
                            url = self._try_upload_with_retry(clip_path, "video")
                            
                            if url:
                                logger.info(f"说话人 {speaker_id} 的视频片段 {segment_id} 上传成功: {url}")
                                
                                # 添加到结果
                                result.append({
                                    'speaker_id': speaker_id,
                                    'segment_id': segment_id,
                                    'clip_url': url
                                })
                            else:
                                logger.warning(f"说话人 {speaker_id} 的视频片段 {segment_id} 上传失败")
                        
                    except ValueError as ve:
                        logger.warning(f"无法解析说话人ID: {item} - {str(ve)}")
                    except Exception as e:
                        logger.error(f"处理说话人 {item} 的视频片段时出错: {str(e)}")
            
            if not result:
                logger.warning("未能成功上传任何视频片段")
            else:
                logger.info(f"成功上传 {len(result)} 个视频片段")
                
            return result
        except Exception as e:
            logger.error(f"上传说话人视频片段失败: {str(e)}")
            return result
    
    def save_to_database(self, room_name: str, task_id: str, full_video_url: str, 
                        sentences_json: str, speaker_clips: List[Dict[str, Any]], 
                        medical_record_json: str = None) -> bool:
        """
        将信息保存到数据库
        
        Args:
            room_name: 诊室名称
            task_id: 任务ID
            full_video_url: 完整视频URL
            sentences_json: sentences.json内容
            speaker_clips: 说话人视频片段信息列表
            medical_record_json: 电子病历结果JSON内容
            
        Returns:
            bool: 是否成功保存
        """
        if not self.conn or not self.cursor:
            logger.error("数据库未连接")
            return False
        
        try:
            # 1. 保存医患对话记录
            # 首先检查表是否存在
            self.cursor.execute("SHOW TABLES LIKE 'medical_conversation'")
            if not self.cursor.fetchone():
                logger.error("medical_conversation表不存在")
                return False
            
            # 检查表结构
            self.cursor.execute("DESCRIBE medical_conversation")
            fields = [row[0] for row in self.cursor.fetchall()]
            logger.info(f"医患对话记录表字段: {fields}")
            
            # 构建正确的插入语句，确保与表结构匹配
            insert_conversation_sql = """
            INSERT INTO medical_conversation 
            (task_id, room_name, full_video_url, sentences_json, medical_record_json)
            VALUES (%s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE 
            room_name = VALUES(room_name),
            full_video_url = VALUES(full_video_url),
            sentences_json = VALUES(sentences_json),
            medical_record_json = IF(VALUES(medical_record_json) IS NOT NULL, VALUES(medical_record_json), medical_record_json),
            update_time = CURRENT_TIMESTAMP
            """
            
            # 记录SQL语句和参数，用于调试
            log_params = (task_id, room_name, full_video_url, 
                          "sentences_json内容(太长省略)" if sentences_json else None, 
                          "medical_record_json内容(太长省略)" if medical_record_json else None)
            logger.info(f"执行SQL: {insert_conversation_sql}")
            logger.info(f"参数: {log_params}")
            
            self.cursor.execute(
                insert_conversation_sql, 
                (task_id, room_name, full_video_url, sentences_json, medical_record_json)
            )
            
            # 记录影响的行数
            affected_rows = self.cursor.rowcount
            logger.info(f"医患对话记录SQL执行完成，影响行数: {affected_rows}")
            
            # 2. 保存说话人视频片段
            if speaker_clips:
                # 先删除旧的记录
                self.cursor.execute(
                    "DELETE FROM conversation_speaker_clips WHERE task_id = %s",
                    (task_id,)
                )
                
                # 插入新记录
                insert_clip_sql = """
                INSERT INTO conversation_speaker_clips
                (task_id, speaker_id, segment_id, clip_url)
                VALUES (%s, %s, %s, %s)
                """
                
                for clip_info in speaker_clips:
                    self.cursor.execute(
                        insert_clip_sql,
                        (
                            task_id, 
                            clip_info['speaker_id'], 
                            clip_info['segment_id'],
                            clip_info['clip_url']
                        )
                    )
            
            # 提交事务
            self.conn.commit()
            logger.info(f"成功保存任务 {task_id} 的信息到数据库")
            return True
            
        except Exception as e:
            # 回滚事务
            self.conn.rollback()
            logger.error(f"保存到数据库失败: {str(e)}")
            # 记录详细错误信息
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False
    
    def save_to_cloud_health(self, task_id: str, userid: str, username: str, chief_complaint: str) -> bool:
        """
        将信息保存到biz_cloud_health表
        
        Args:
            task_id: 任务ID
            userid: 用户ID
            username: 用户姓名
            chief_complaint: 患者主诉
            
        Returns:
            bool: 是否成功保存
        """
        if not self.conn or not self.cursor:
            logger.error("数据库未连接")
            return False
        
        try:
            # 首先检查表是否存在
            self.cursor.execute("SHOW TABLES LIKE 'biz_cloud_health'")
            if not self.cursor.fetchone():
                logger.error("biz_cloud_health表不存在")
                return False
            
            # 检查表结构
            self.cursor.execute("DESCRIBE biz_cloud_health")
            fields = [row[0] for row in self.cursor.fetchall()]
            logger.info(f"biz_cloud_health表字段: {fields}")
            
            # 获取当前时间
            now = time.strftime('%Y-%m-%d %H:%M:%S')
            
            # 生成唯一ID: 毫秒级时间戳 + 3位随机数
            timestamp_ms = int(time.time() * 1000)
            random_num = random.randint(100, 999)
            unique_id = timestamp_ms * 1000 + random_num
            
            # 使用合理的默认值，确保字段不为空
            if not userid or userid.strip() == '':
                userid = f"system_{task_id}"
                logger.info(f"使用默认用户ID: {userid}")
            
            if not username or username.strip() == '':
                username = "系统用户"
                logger.info(f"使用默认用户名: {username}")
            
            # 确保userid和username是字符串类型
            userid = str(userid)
            username = str(username)
            
            # 构建消息内容
            msgcontent = f"主诉：{chief_complaint}" if chief_complaint else "主诉：未提供"
            
            # 详细记录要执行的操作
            logger.info(f"准备将数据保存到biz_cloud_health表 - 任务ID: {task_id}")
            logger.info(f"用户信息 - userid: '{userid}', username: '{username}'")
            logger.info(f"消息内容: {msgcontent}")
            logger.info(f"生成的唯一ID: {unique_id}")
            
            # 插入数据到biz_cloud_health表
            insert_sql = """
            INSERT INTO biz_cloud_health 
            (id, msgname, msgtype, msgcontent, userid, username, msgid, createtime, enable, createdate, msgtypeid)
            VALUES (%s, '名医智能辨证辅助系统', 'AI电子病历生成', %s, %s, %s, 6992, %s, 1, %s, 4447378052527161344)
            """
            
            # 记录SQL语句
            log_sql = insert_sql.replace("%s", "'{}'").format(
                unique_id, msgcontent, userid, username, now, now
            )
            logger.info(f"执行SQL: {log_sql}")
            
            # 准备参数并确保类型正确
            params = (unique_id, msgcontent, userid, username, now, now)
            logger.info(f"SQL参数类型: {[type(p).__name__ for p in params]}")
            
            self.cursor.execute(insert_sql, params)
            
            # 记录影响的行数
            affected_rows = self.cursor.rowcount
            logger.info(f"SQL执行完成，影响行数: {affected_rows}")
            
            # 提交事务
            self.conn.commit()
            logger.info(f"成功保存任务 {task_id} 的信息到biz_cloud_health表，事务已提交")
            return True
            
        except Exception as e:
            # 回滚事务
            self.conn.rollback()
            logger.error(f"保存到biz_cloud_health表失败: {str(e)}")
            # 记录详细错误信息
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False
    
    def process_task(self, room_name: str, task_id: str, medical_record_json: str = None) -> bool:
        """
        处理单个任务
        
        Args:
            room_name: 诊室名称
            task_id: 任务ID
            medical_record_json: 电子病历结果JSON内容
            
        Returns:
            bool: 是否成功处理
        """
        try:
            logger.info(f"开始处理任务: {room_name}/{task_id}")
            
            # 1. 上传完整视频
            full_video_url = self.upload_full_video(room_name, task_id)
            if not full_video_url:
                logger.warning(f"任务 {task_id} 的完整视频上传失败，将继续处理其他内容")
            
            # 2. 读取sentences.json
            sentences_path = os.path.join(self.separated_audio_dir, room_name, task_id, "sentences.json")
            sentences_json = ""
            if os.path.exists(sentences_path):
                sentences_json = self.read_sentences_json(sentences_path)
                if not sentences_json:
                    logger.warning(f"任务 {task_id} 的sentences.json读取失败")
            else:
                logger.warning(f"sentences.json文件不存在: {sentences_path}")
            
            # 3. 读取电子病历结果JSON文件
            if medical_record_json is None:
                medical_record_json = None
                medical_record_path = os.path.join(self.separated_audio_dir, room_name, task_id, f"{task_id}.json")
                if os.path.exists(medical_record_path):
                    try:
                        with open(medical_record_path, 'r', encoding='utf-8') as f:
                            medical_record_content = json.load(f)
                            medical_record_json = json.dumps(medical_record_content, ensure_ascii=False)
                            logger.info(f"成功读取电子病历结果: {medical_record_path}")
                    except Exception as e:
                        logger.error(f"读取电子病历结果文件失败: {str(e)}")
                else:
                    logger.warning(f"电子病历结果文件不存在: {medical_record_path}")
            
            # 4. 上传说话人视频片段
            speaker_clips = self.upload_speaker_clips(room_name, task_id)
            if not speaker_clips:
                logger.warning(f"任务 {task_id} 没有成功上传的说话人视频片段")
            
            # 5. 保存到数据库
            if not self.save_to_database(room_name, task_id, full_video_url, sentences_json, speaker_clips, medical_record_json):
                logger.error(f"任务 {task_id} 的信息保存到数据库失败")
                return False
            
            logger.info(f"任务 {room_name}/{task_id} 处理完成")
            return True
            
        except Exception as e:
            logger.error(f"处理任务 {task_id} 时出错: {str(e)}")
            return False
    
    def find_all_tasks(self) -> List[Tuple[str, str]]:
        """
        查找所有可处理的任务
        
        Returns:
            List[Tuple[str, str]]: (诊室名称, 任务ID)列表
        """
        tasks = []
        
        try:
            # 遍历SeparatedAudioText目录
            if os.path.exists(self.separated_audio_dir):
                for room_name in os.listdir(self.separated_audio_dir):
                    room_path = os.path.join(self.separated_audio_dir, room_name)
                    if os.path.isdir(room_path):
                        for task_id in os.listdir(room_path):
                            task_path = os.path.join(room_path, task_id)
                            if os.path.isdir(task_path):
                                tasks.append((room_name, task_id))
            
            logger.info(f"找到 {len(tasks)} 个可处理的任务")
            return tasks
            
        except Exception as e:
            logger.error(f"查找任务时出错: {str(e)}")
            return []
    
    def process_all(self) -> Tuple[int, int]:
        """
        处理所有任务
        
        Returns:
            Tuple[int, int]: (成功处理数, 总任务数)
        """
        # 连接数据库
        if not self.connect_db():
            return 0, 0
        
        try:
            # 查找所有任务
            tasks = self.find_all_tasks()
            
            if not tasks:
                logger.warning("未找到可处理的任务")
                return 0, 0
            
            # 处理每个任务
            success_count = 0
            for room_name, task_id in tasks:
                if self.process_task(room_name, task_id):
                    success_count += 1
            
            return success_count, len(tasks)
            
        except Exception as e:
            logger.error(f"处理所有任务时出错: {str(e)}")
            return 0, 0
        finally:
            # 关闭数据库连接
            self.close_db()
    
    def process_specific_task(self, room_name: str, task_id: str, medical_record_json: str = None,
                            userid: str = '', username: str = '', chief_complaint: str = '') -> bool:
        """
        处理特定任务
        
        Args:
            room_name: 诊室名称
            task_id: 任务ID
            medical_record_json: 电子病历结果JSON内容
            userid: 用户ID
            username: 用户姓名
            chief_complaint: 患者主诉
            
        Returns:
            bool: 是否成功处理
        """
        # 连接数据库
        if not self.connect_db():
            return False
        
        try:
            # 记录传入的用户信息
            logger.info(f"处理任务开始 - 任务ID: {task_id}, 诊室: {room_name}")
            logger.info(f"用户信息 - userid: '{userid}', username: '{username}', chief_complaint: '{chief_complaint}'")
            
            # 处理指定任务
            result = self.process_task(room_name, task_id, medical_record_json)
            
            # 保存到biz_cloud_health表
            if result:
                # 无论是否提供userid和username，都调用save_to_cloud_health方法
                # 在save_to_cloud_health方法中会处理空值的情况
                logger.info(f"尝试保存任务 {task_id} 的信息到biz_cloud_health表")
                cloud_health_result = self.save_to_cloud_health(task_id, userid, username, chief_complaint)
                logger.info(f"保存到biz_cloud_health表结果: {cloud_health_result}")
            
            return result
            
        except Exception as e:
            logger.error(f"处理任务 {room_name}/{task_id} 时出错: {str(e)}")
            # 记录详细错误信息
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False
        finally:
            # 关闭数据库连接
            self.close_db()


def main():
    """主函数，处理命令行参数"""
    parser = argparse.ArgumentParser(description='上传视频文件到MinIO并保存信息到数据库')
    parser.add_argument('--base-dir', '-d', help='项目根目录路径')
    parser.add_argument('--room', '-r', help='指定诊室名称')
    parser.add_argument('--task', '-t', help='指定任务ID')
    parser.add_argument('--all', '-a', action='store_true', help='处理所有任务')
    parser.add_argument('--list', '-l', action='store_true', help='列出所有可处理的任务')
    parser.add_argument('--test-upload', help='测试上传单个文件，指定文件路径')
    parser.add_argument('--medical-record', '-m', help='指定电子病历JSON文件路径')
    parser.add_argument('--userid', help='用户ID')
    parser.add_argument('--username', help='用户姓名')
    parser.add_argument('--chief-complaint', help='患者主诉')
    
    args = parser.parse_args()
    
    # 测试上传单个文件
    if args.test_upload:
        if os.path.exists(args.test_upload):
            print(f"测试上传文件: {args.test_upload}")
            try:
                url = upload_to_minio(args.test_upload, "video")
                print(f"上传成功，URL: {url}")
                return 0
            except Exception as e:
                print(f"上传失败: {str(e)}")
                return 1
        else:
            print(f"文件不存在: {args.test_upload}")
            return 1
    
    # 创建上传器
    uploader = MediaUploader(args.base_dir)
    
    # 列出所有任务
    if args.list:
        tasks = uploader.find_all_tasks()
        if tasks:
            print(f"找到 {len(tasks)} 个可处理的任务:")
            for i, (room_name, task_id) in enumerate(tasks, 1):
                print(f"{i}. 诊室: {room_name}, 任务ID: {task_id}")
        else:
            print("未找到可处理的任务")
        return 0
    
    # 处理单个任务
    if args.room and args.task:
        # 如果提供了电子病历JSON文件路径，先处理它
        medical_record_json = None
        if args.medical_record and os.path.exists(args.medical_record):
            try:
                with open(args.medical_record, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 尝试解析JSON内容
                try:
                    # 如果内容以```json开头，提取JSON部分
                    if content.startswith('```json') and '```' in content[6:]:
                        start_pos = content.find('```json') + 7
                        end_pos = content.find('```', start_pos)
                        
                        if end_pos > start_pos:
                            json_content = content[start_pos:end_pos].strip()
                            json_obj = json.loads(json_content)
                            medical_record_json = json.dumps(json_obj, ensure_ascii=False)
                            logger.info(f"成功从Markdown代码块中提取电子病历JSON内容")
                    else:
                        # 直接尝试解析
                        json_obj = json.loads(content)
                        medical_record_json = json.dumps(json_obj, ensure_ascii=False)
                        logger.info("成功解析电子病历JSON内容")
                except json.JSONDecodeError as je:
                    logger.error(f"解析电子病历JSON内容失败: {str(je)}")
                    # 如果解析失败，使用原始内容
                    medical_record_json = content
                    logger.warning("使用原始文件内容作为医疗记录JSON字符串")
                    
                logger.info(f"已读取电子病历JSON文件: {args.medical_record}")
            except Exception as e:
                logger.error(f"读取电子病历JSON文件失败: {str(e)}")
        
        # 处理任务
        success = uploader.process_specific_task(args.room, args.task, medical_record_json, args.userid, args.username, args.chief_complaint)
        if success:
            logger.info(f"成功处理任务: {args.room}/{args.task}")
            return 0
        else:
            logger.error(f"处理任务失败: {args.room}/{args.task}")
            return 1
    
    # 处理所有任务
    if args.all:
        success_count, total = uploader.process_all()
        if total > 0:
            logger.info(f"成功处理 {success_count}/{total} 个任务")
            return 0 if success_count == total else 1
        else:
            logger.warning("未找到可处理的任务")
            return 0
    
    # 如果没有指定任何操作，显示帮助信息
    parser.print_help()
    return 0


if __name__ == "__main__":
    sys.exit(main()) 