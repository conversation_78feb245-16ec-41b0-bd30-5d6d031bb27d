#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
医患对话数据库表创建工具
---------------------
用于创建存储医患对话数据的数据库表
"""

import sys
import logging
import pymysql
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('create_tables.log')
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    "host": "*************",
    "port": 3307,
    "user": "sgkj",
    "password": "cdutcm@123",
    "database": "chd6_4_yph_test",
    "charset": "utf8mb4"
}

# 建表SQL语句
CREATE_TABLES_SQL = [
    """
    CREATE TABLE IF NOT EXISTS `medical_conversation` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `task_id` varchar(64) NOT NULL COMMENT '任务ID',
        `room_name` varchar(64) DEFAULT NULL COMMENT '诊室名称',
        `full_video_url` varchar(512) DEFAULT NULL COMMENT '完整视频URL',
        `sentences_json` text COMMENT '转写内容JSON',
        `medical_record_json` text COMMENT '电子病历结果JSON',
        `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `uk_task_id` (`task_id`),
        KEY `idx_room_name` (`room_name`),
        KEY `idx_create_time` (`create_time`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='医患对话记录表';
    """,
    """
    CREATE TABLE IF NOT EXISTS `conversation_speaker_clips` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `task_id` varchar(64) NOT NULL COMMENT '关联的任务ID',
        `speaker_id` int(11) NOT NULL COMMENT '说话人ID',
        `segment_id` varchar(64) DEFAULT NULL COMMENT '视频片段标识(如segment_1)',
        `clip_url` varchar(512) NOT NULL COMMENT '视频片段URL',
        `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `uk_task_speaker_segment` (`task_id`, `speaker_id`, `segment_id`),
        KEY `idx_task_id` (`task_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对话说话人视频片段表';
    """
]

# SQL升级脚本 - 在已存在的表上添加新字段
ALTER_TABLE_SQL = """
ALTER TABLE `conversation_speaker_clips`
ADD COLUMN `segment_id` varchar(64) DEFAULT NULL COMMENT '视频片段标识(如segment_1)' AFTER `speaker_id`;
"""

# SQL升级脚本 - 在medical_conversation表中添加id字段
ALTER_TABLE_SQL_ID = """
ALTER TABLE `medical_conversation`
ADD COLUMN `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID' FIRST,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `uk_task_id` (`task_id`);
"""

# SQL升级脚本 - 在medical_conversation表中添加id字段 (不包含唯一键约束)
ALTER_TABLE_SQL_ID_NO_CONSTRAINT = """
ALTER TABLE `medical_conversation`
ADD COLUMN `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID' FIRST,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`id`);
"""

# 添加唯一键约束的SQL
ADD_TASK_ID_CONSTRAINT = """
ALTER TABLE `medical_conversation`
ADD UNIQUE KEY `uk_task_id` (`task_id`);
"""

# SQL升级脚本 - 在medical_conversation表中添加medical_record_json字段
ALTER_TABLE_SQL_MEDICAL_RECORD = """
ALTER TABLE `medical_conversation`
ADD COLUMN `medical_record_json` text COMMENT '电子病历结果JSON' AFTER `sentences_json`;
"""

# 单独的SQL语句用于更新唯一键约束
UPDATE_CONSTRAINT_SQL = """
ALTER TABLE `conversation_speaker_clips`
DROP INDEX `uk_task_speaker`,
ADD UNIQUE KEY `uk_task_speaker_segment` (`task_id`, `speaker_id`, `segment_id`);
"""

def upgrade_existing_tables():
    """
    升级现有表结构
    
    Returns:
        bool: 是否成功升级
    """
    conn = None
    cursor = None
    
    try:
        # 连接数据库
        conn = pymysql.connect(
            host=DB_CONFIG["host"],
            port=DB_CONFIG["port"],
            user=DB_CONFIG["user"],
            password=DB_CONFIG["password"],
            database=DB_CONFIG["database"],
            charset=DB_CONFIG["charset"]
        )
        cursor = conn.cursor()
        
        # 处理conversation_speaker_clips表的segment_id字段
        cursor.execute("SHOW COLUMNS FROM `conversation_speaker_clips` LIKE 'segment_id'")
        field_exists = cursor.fetchone() is not None
        
        if not field_exists:
            # 执行添加字段的SQL
            logger.info(f"添加segment_id字段:\n{ALTER_TABLE_SQL}")
            cursor.execute(ALTER_TABLE_SQL)
            logger.info("segment_id字段添加成功")
        else:
            logger.info("segment_id字段已存在")
        
        # 检查唯一键约束
        cursor.execute("SHOW INDEX FROM `conversation_speaker_clips` WHERE Key_name = 'uk_task_speaker'")
        old_constraint_exists = cursor.fetchone() is not None
        
        cursor.execute("SHOW INDEX FROM `conversation_speaker_clips` WHERE Key_name = 'uk_task_speaker_segment'")
        new_constraint_exists = cursor.fetchone() is not None
        
        if old_constraint_exists and not new_constraint_exists:
            # 执行更新约束的SQL
            logger.info(f"更新唯一键约束:\n{UPDATE_CONSTRAINT_SQL}")
            cursor.execute(UPDATE_CONSTRAINT_SQL)
            logger.info("唯一键约束更新成功")
        elif not old_constraint_exists and not new_constraint_exists:
            # 添加新的约束
            add_constraint_sql = "ALTER TABLE `conversation_speaker_clips` ADD UNIQUE KEY `uk_task_speaker_segment` (`task_id`, `speaker_id`, `segment_id`)"
            logger.info(f"添加新的唯一键约束:\n{add_constraint_sql}")
            cursor.execute(add_constraint_sql)
            logger.info("新的唯一键约束添加成功")
        elif old_constraint_exists and new_constraint_exists:
            # 删除旧约束，保留新约束
            logger.info("发现同时存在旧约束和新约束，删除旧约束")
            cursor.execute("ALTER TABLE `conversation_speaker_clips` DROP INDEX `uk_task_speaker`")
            logger.info("旧的唯一键约束删除成功")
        else:
            logger.info("唯一键约束已更新")
        
        # 处理medical_conversation表的id字段
        cursor.execute("SHOW COLUMNS FROM `medical_conversation` LIKE 'id'")
        id_field_exists = cursor.fetchone() is not None
        
        if not id_field_exists:
            # 检查是否已存在唯一键约束
            cursor.execute("SHOW INDEX FROM `medical_conversation` WHERE Key_name = 'uk_task_id'")
            task_id_constraint_exists = cursor.fetchone() is not None
            
            if task_id_constraint_exists:
                # 如果已存在约束，使用不包含约束的SQL
                logger.info(f"向medical_conversation表添加id字段(不添加约束):\n{ALTER_TABLE_SQL_ID_NO_CONSTRAINT}")
                cursor.execute(ALTER_TABLE_SQL_ID_NO_CONSTRAINT)
                logger.info("id字段添加成功，主键已更新")
            else:
                # 如果不存在约束，使用完整SQL
                logger.info(f"向medical_conversation表添加id字段:\n{ALTER_TABLE_SQL_ID}")
                cursor.execute(ALTER_TABLE_SQL_ID)
                logger.info("id字段添加成功，主键已更新")
        else:
            logger.info("id字段已存在于medical_conversation表中")
            
            # 检查是否已存在唯一键约束
            cursor.execute("SHOW INDEX FROM `medical_conversation` WHERE Key_name = 'uk_task_id'")
            task_id_constraint_exists = cursor.fetchone() is not None
            
            if not task_id_constraint_exists:
                # 如果不存在约束，添加约束
                logger.info(f"向medical_conversation表添加唯一键约束:\n{ADD_TASK_ID_CONSTRAINT}")
                cursor.execute(ADD_TASK_ID_CONSTRAINT)
                logger.info("唯一键约束添加成功")
        
        # 处理medical_conversation表的medical_record_json字段
        cursor.execute("SHOW COLUMNS FROM `medical_conversation` LIKE 'medical_record_json'")
        medical_record_field_exists = cursor.fetchone() is not None
        
        if not medical_record_field_exists:
            # 执行添加medical_record_json字段的SQL
            logger.info(f"向medical_conversation表添加medical_record_json字段:\n{ALTER_TABLE_SQL_MEDICAL_RECORD}")
            cursor.execute(ALTER_TABLE_SQL_MEDICAL_RECORD)
            logger.info("medical_record_json字段添加成功")
        else:
            logger.info("medical_record_json字段已存在于medical_conversation表中")
        
        conn.commit()
        logger.info("成功升级数据库表结构")
        return True
        
    except Exception as e:
        logger.error(f"升级数据库表结构时出错: {str(e)}")
        if conn:
            conn.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def create_tables(drop_existing=False):
    """
    创建数据库表
    
    Args:
        drop_existing: 是否先删除已存在的表
    
    Returns:
        bool: 是否成功创建表
    """
    conn = None
    cursor = None
    
    try:
        # 连接数据库
        conn = pymysql.connect(
            host=DB_CONFIG["host"],
            port=DB_CONFIG["port"],
            user=DB_CONFIG["user"],
            password=DB_CONFIG["password"],
            database=DB_CONFIG["database"],
            charset=DB_CONFIG["charset"]
        )
        cursor = conn.cursor()
        
        # 如果指定了删除已存在的表
        if drop_existing:
            logger.info("删除已存在的表...")
            cursor.execute("DROP TABLE IF EXISTS `conversation_speaker_clips`")
            cursor.execute("DROP TABLE IF EXISTS `medical_conversation`")
            logger.info("已删除现有表")
        
        # 创建表
        for sql in CREATE_TABLES_SQL:
            logger.info(f"执行SQL:\n{sql}")
            cursor.execute(sql)
        
        conn.commit()
        logger.info("成功创建数据库表")
        return True
        
    except Exception as e:
        logger.error(f"创建数据库表时出错: {str(e)}")
        if conn:
            conn.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def main():
    """主函数，处理命令行参数"""
    parser = argparse.ArgumentParser(description='创建或升级医患对话数据库表')
    parser.add_argument('--drop', '-d', action='store_true', help='先删除已存在的表再创建')
    parser.add_argument('--upgrade', '-u', action='store_true', help='升级现有表结构')
    
    args = parser.parse_args()
    
    if args.upgrade:
        print("开始升级医患对话数据库表结构...")
        if upgrade_existing_tables():
            print("数据库表结构升级成功")
            return 0
        else:
            print("数据库表结构升级失败，请查看日志获取详细信息")
            return 1
    else:
        print("开始创建医患对话数据库表...")
        if create_tables(args.drop):
            print("数据库表创建成功")
            return 0
        else:
            print("数据库表创建失败，请查看日志获取详细信息")
            return 1

if __name__ == "__main__":
    sys.exit(main()) 