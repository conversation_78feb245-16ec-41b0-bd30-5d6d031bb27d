/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-0ubuntu0.18.04.1)
 Source Host           : *************:3307
 Source Schema         : chd6_4_yph_test

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-0ubuntu0.18.04.1)
 File Encoding         : 65001

 Date: 26/05/2025 14:15:05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for biz_cloud_health
-- ----------------------------
DROP TABLE IF EXISTS `biz_cloud_health`;
CREATE TABLE `biz_cloud_health`  (
  `id` bigint(11) NOT NULL,
  `msgname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系统消息来源',
  `msgtype` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
  `msgcontent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息内容',
  `userid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户姓名',
  `msgid` bigint(20) NULL DEFAULT NULL COMMENT '系统消息来源id',
  `createtime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `enable` bit(1) NULL DEFAULT NULL COMMENT '是否展示',
  `createdate` datetime NULL DEFAULT NULL COMMENT '消息发生时间',
  `msgtypeid` bigint(20) NULL DEFAULT NULL COMMENT '消息类型id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
