# 房间流媒体映射系统

## 概述

房间流媒体映射系统用于管理诊室与视频流之间的映射关系，支持通过IP地址或流媒体URL查询对应的诊室信息。系统包含以下组件：

1. **数据库表设计**：存储房间映射关系的数据库表
2. **数据访问层**：提供统一的接口访问映射数据
3. **管理工具**：用于增删改查映射数据
4. **REST API**：提供HTTP API接口，便于前端访问

## 数据库设计

房间流媒体映射表结构如下：

```sql
CREATE TABLE IF NOT EXISTS `room_stream_mapping` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_name` varchar(100) NOT NULL COMMENT '诊室名称',
  `ip_address` varchar(50) NOT NULL COMMENT 'IP地址，用于标识设备',
  `stream_url` varchar(255) NOT NULL COMMENT '流媒体URL',
  `description` varchar(255) DEFAULT NULL COMMENT '描述信息',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活，1=激活，0=禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_name` (`room_name`),
  UNIQUE KEY `uk_ip_address` (`ip_address`),
  UNIQUE KEY `uk_stream_url` (`stream_url`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='诊室流媒体映射表';
```

特点：
- 使用`room_name`、`ip_address`和`stream_url`作为唯一键，确保数据唯一性
- 支持逻辑删除，通过`is_active`字段标记记录是否有效
- 自动记录创建时间和更新时间

## 组件说明

### 1. 数据访问对象（DAO）

位置：`stream_capture/core/db/room_mapping_dao.py`

功能：
- 提供单例模式，确保全局只有一个实例
- 支持根据IP地址、流媒体URL或房间名称查询映射关系
- 内置本地缓存，提高查询性能
- 支持备用配置文件，当数据库连接失败时自动回退到本地JSON配置

用法示例：
```python
from stream_capture.core.db.room_mapping_dao import RoomMappingDAO

# 创建DAO实例
dao = RoomMappingDAO()

# 根据IP地址查询房间信息
room_info = dao.get_room_by_ip("70:85:c4:61:66:78")
if room_info:
    print(f"找到房间: {room_info['room_name']}")

# 根据流媒体URL查询房间信息
room_info = dao.get_room_by_stream_url("https://livell.cdutcm.edu.cn/live/f2ya.live.flv")
if room_info:
    print(f"找到房间: {room_info['room_name']}")

# 获取所有房间映射
all_rooms = dao.get_all_rooms()
```

### 2. 映射管理工具

位置：`front/room_mapping_manager.py`

功能：
- 初始化映射表结构
- 提供完整的增删改查功能
- 支持从JSON配置文件导入映射数据
- 支持导出映射数据到JSON文件

用法示例：
```bash
# 初始化表并导入数据
python front/init_room_mapping.py

# 导出数据到JSON文件
python front/init_room_mapping.py --export exported_config.json

# 管理映射数据（可以直接使用Python API）
```

Python API 示例：
```python
from front.room_mapping_manager import RoomMappingManager

# 创建管理器并连接数据库
manager = RoomMappingManager()
manager.connect_db()

# 添加映射
manager.add_mapping(
    room_name="测试诊室",
    ip_address="12:34:56:78:90:AB",
    stream_url="https://example.com/stream.flv",
    description="测试用诊室"
)

# 更新映射
manager.update_mapping(
    id=1,
    stream_url="https://example.com/new_stream.flv"
)

# 删除映射（逻辑删除）
manager.delete_mapping(id=1)

# 关闭数据库连接
manager.close_db()
```

### 3. REST API

位置：
- API入口：`stream_capture/api/api.py`
- 房间映射API：`stream_capture/api/room_mapping_api.py`

启动API服务器：
```bash
python stream_capture/start_api_server.py --port 5000
```

API接口列表：

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/api/room-mapping/list` | GET | 获取所有映射列表 |
| `/api/room-mapping/detail/<id>` | GET | 获取指定ID的映射详情 |
| `/api/room-mapping/query` | GET | 根据条件查询映射 |
| `/api/room-mapping/add` | POST | 添加新映射 |
| `/api/room-mapping/update/<id>` | PUT | 更新映射 |
| `/api/room-mapping/delete/<id>` | DELETE | 删除映射 |
| `/api/room-mapping/export` | GET | 导出映射数据为JSON |

示例请求：

1. 获取所有映射：

```http
GET /api/room-mapping/list
```

2. 添加新映射：

```http
POST /api/room-mapping/add
Content-Type: application/json

{
  "room_name": "测试诊室",
  "ip_address": "12:34:56:78:90:AB", 
  "stream_url": "https://example.com/stream.flv",
  "description": "测试用诊室"
}
```

3. 更新映射：

```http
PUT /api/room-mapping/update/1
Content-Type: application/json

{
  "stream_url": "https://example.com/new_stream.flv"
}
```

## 与原系统的集成

系统已实现了与原有代码的无缝集成：

1. `StreamCapturer`类已更新为使用RoomMappingDAO获取房间映射，并在数据库连接失败时自动回退到本地配置文件。
2. `SignalHandler`类也已经修改为使用RoomMappingDAO，保持原有功能不变。
3. 新增REST API服务，方便前端通过HTTP接口访问和管理映射数据。

## 配置与依赖

数据库配置在以下文件中定义：
- `front/room_mapping_manager.py`
- `stream_capture/core/db/room_mapping_dao.py`

依赖项：
- Python 3.6+
- PyMySQL
- Flask (仅REST API需要)
- Flask-CORS (仅REST API需要)

可通过以下命令安装依赖：
```bash
pip install pymysql flask flask-cors
```

## 使用流程

1. 初始化数据库：
   ```bash
   python front/init_room_mapping.py
   ```

2. 启动API服务器：
   ```bash
   python stream_capture/start_api_server.py
   ```

3. 通过API管理映射数据，或直接在代码中使用DAO：
   ```python
   from stream_capture.core.db.room_mapping_dao import RoomMappingDAO
   
   dao = RoomMappingDAO()
   room_info = dao.get_room_by_ip("70:85:c4:61:66:78")
   ```

## 注意事项

1. 确保数据库连接配置正确（主机、端口、用户名、密码、数据库名）
2. 如需修改表结构，请在`room_mapping_manager.py`的`init_table`方法中更新
3. API服务器默认监听所有地址（0.0.0.0），可通过参数调整
4. 为保持兼容性，保留了对`mac_address`字段的支持，会自动映射到`ip_address` 