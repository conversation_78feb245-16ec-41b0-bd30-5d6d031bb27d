#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
房间流媒体映射管理工具
-------------------
这个脚本用于管理房间流媒体映射的数据库表，提供以下功能：
1. 初始化房间映射表（如果不存在）
2. 导入现有的JSON配置到数据库
3. 导出数据库映射为JSON配置
4. 提供房间映射的增删改查功能
5. 根据IP地址或stream_url查询房间信息
"""

import os
import sys
import json
import argparse
import logging
import pymysql
from typing import Dict, List, Tuple, Optional, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('room_mapping_manager.log')
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    "host": "*************",
    "port": 3307,
    "user": "sgkj",
    "password": "cdutcm@123",
    "database": "chd6_4_yph_test",
    "charset": "utf8mb4"
}


class RoomMappingManager:
    """房间流媒体映射管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.conn = None
        self.cursor = None
    
    def connect_db(self) -> bool:
        """
        连接到数据库
        
        Returns:
            bool: 是否成功连接
        """
        try:
            self.conn = pymysql.connect(
                host=DB_CONFIG["host"],
                port=DB_CONFIG["port"],
                user=DB_CONFIG["user"],
                password=DB_CONFIG["password"],
                database=DB_CONFIG["database"],
                charset=DB_CONFIG["charset"]
            )
            self.cursor = self.conn.cursor(pymysql.cursors.DictCursor)  # 使用字典游标
            logger.info("成功连接到数据库")
            return True
        except Exception as e:
            logger.error(f"连接数据库失败: {str(e)}")
            return False
    
    def close_db(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
            logger.info("数据库连接已关闭")
    
    def init_table(self) -> bool:
        """
        初始化房间映射表
        
        Returns:
            bool: 是否成功初始化
        """
        if not self.conn or not self.cursor:
            logger.error("数据库未连接")
            return False
        
        try:
            # 创建表
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS `room_stream_mapping` (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
              `room_name` varchar(100) NOT NULL COMMENT '诊室名称',
              `ip_address` varchar(50) NOT NULL COMMENT 'IP地址，用于标识设备',
              `stream_url` varchar(255) NOT NULL COMMENT '流媒体URL',
              `description` varchar(255) DEFAULT NULL COMMENT '描述信息',
              `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活，1=激活，0=禁用',
              `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              PRIMARY KEY (`id`),
              UNIQUE KEY `uk_room_name` (`room_name`),
              UNIQUE KEY `uk_ip_address` (`ip_address`),
              UNIQUE KEY `uk_stream_url` (`stream_url`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='诊室流媒体映射表';
            """
            self.cursor.execute(create_table_sql)
            
            # 检查是否已经有数据
            self.cursor.execute("SELECT COUNT(*) as count FROM `room_stream_mapping`")
            count = self.cursor.fetchone()['count']
            
            logger.info(f"房间映射表初始化成功，当前有 {count} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"初始化房间映射表失败: {str(e)}")
            return False
    
    def import_from_json(self, json_file: str) -> bool:
        """
        从JSON配置文件导入映射数据
        
        Args:
            json_file: JSON配置文件路径
            
        Returns:
            bool: 是否成功导入
        """
        if not self.conn or not self.cursor:
            logger.error("数据库未连接")
            return False
        
        try:
            # 读取JSON文件
            with open(json_file, 'r', encoding='utf-8') as f:
                mappings = json.load(f)
            
            if not isinstance(mappings, list):
                logger.error(f"JSON文件格式错误，应为列表: {json_file}")
                return False
            
            # 插入数据
            insert_sql = """
            INSERT INTO `room_stream_mapping` (`room_name`, `ip_address`, `stream_url`, `description`)
            VALUES (%s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            `ip_address` = VALUES(`ip_address`),
            `stream_url` = VALUES(`stream_url`),
            `description` = VALUES(`description`),
            `is_active` = 1
            """
            
            success_count = 0
            for item in mappings:
                try:
                    # 确保必要字段存在，ip_address来自mac_address字段
                    if 'room_name' not in item or ('mac_address' not in item and 'ip_address' not in item) or 'stream_url' not in item:
                        logger.warning(f"跳过无效项: {item}")
                        continue
                    
                    # 兼容处理：使用mac_address字段作为ip_address
                    ip_address = item.get('ip_address', item.get('mac_address', ''))
                    
                    self.cursor.execute(
                        insert_sql, 
                        (
                            item['room_name'], 
                            ip_address, 
                            item['stream_url'], 
                            item.get('description', f"{item['room_name']}设备")
                        )
                    )
                    success_count += 1
                except Exception as e:
                    logger.warning(f"导入项目失败: {item} - {str(e)}")
            
            self.conn.commit()
            logger.info(f"成功导入 {success_count}/{len(mappings)} 条映射记录")
            return success_count > 0
            
        except Exception as e:
            self.conn.rollback()
            logger.error(f"从JSON导入映射数据失败: {str(e)}")
            return False
    
    def export_to_json(self, json_file: str) -> bool:
        """
        导出映射数据到JSON配置文件
        
        Args:
            json_file: JSON配置文件路径
            
        Returns:
            bool: 是否成功导出
        """
        if not self.conn or not self.cursor:
            logger.error("数据库未连接")
            return False
        
        try:
            # 查询所有活跃的映射
            self.cursor.execute("""
                SELECT `room_name`, `ip_address`, `stream_url`, `description` 
                FROM `room_stream_mapping` 
                WHERE `is_active` = 1
                ORDER BY `id`
            """)
            mappings = self.cursor.fetchall()
            
            # 转换为与原配置兼容的格式
            result = []
            for item in mappings:
                mapping = {
                    'room_name': item['room_name'],
                    'ip_address': item['ip_address'],  # 使用ip_address替代mac_address
                    'stream_url': item['stream_url']
                }
                if item['description']:
                    mapping['description'] = item['description']
                result.append(mapping)
            
            # 保存到JSON文件
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"成功导出 {len(result)} 条映射记录到 {json_file}")
            return True
            
        except Exception as e:
            logger.error(f"导出映射数据到JSON失败: {str(e)}")
            return False
    
    def add_mapping(self, room_name: str, ip_address: str, stream_url: str, description: str = None) -> bool:
        """
        添加映射
        
        Args:
            room_name: 诊室名称
            ip_address: IP地址
            stream_url: 流媒体URL
            description: 描述信息
            
        Returns:
            bool: 是否成功添加
        """
        if not self.conn or not self.cursor:
            logger.error("数据库未连接")
            return False
        
        try:
            insert_sql = """
            INSERT INTO `room_stream_mapping` (`room_name`, `ip_address`, `stream_url`, `description`)
            VALUES (%s, %s, %s, %s)
            """
            
            self.cursor.execute(insert_sql, (room_name, ip_address, stream_url, description))
            self.conn.commit()
            
            logger.info(f"成功添加映射: {room_name} - {ip_address} - {stream_url}")
            return True
            
        except Exception as e:
            self.conn.rollback()
            logger.error(f"添加映射失败: {str(e)}")
            return False
    
    def update_mapping(self, id: int, room_name: str = None, ip_address: str = None, 
                      stream_url: str = None, description: str = None, is_active: int = None) -> bool:
        """
        更新映射
        
        Args:
            id: 映射ID
            room_name: 诊室名称
            ip_address: IP地址
            stream_url: 流媒体URL
            description: 描述信息
            is_active: 是否激活
            
        Returns:
            bool: 是否成功更新
        """
        if not self.conn or not self.cursor:
            logger.error("数据库未连接")
            return False
        
        try:
            # 构建更新字段
            update_fields = []
            params = []
            
            if room_name is not None:
                update_fields.append("`room_name` = %s")
                params.append(room_name)
            
            if ip_address is not None:
                update_fields.append("`ip_address` = %s")
                params.append(ip_address)
            
            if stream_url is not None:
                update_fields.append("`stream_url` = %s")
                params.append(stream_url)
            
            if description is not None:
                update_fields.append("`description` = %s")
                params.append(description)
            
            if is_active is not None:
                update_fields.append("`is_active` = %s")
                params.append(is_active)
            
            if not update_fields:
                logger.warning("没有提供要更新的字段")
                return False
            
            # 添加ID条件
            params.append(id)
            
            # 构建SQL
            update_sql = f"""
            UPDATE `room_stream_mapping` 
            SET {', '.join(update_fields)}
            WHERE `id` = %s
            """
            
            self.cursor.execute(update_sql, params)
            if self.cursor.rowcount == 0:
                logger.warning(f"没有找到ID为 {id} 的映射记录")
                self.conn.rollback()
                return False
            
            self.conn.commit()
            
            logger.info(f"成功更新ID为 {id} 的映射")
            return True
            
        except Exception as e:
            self.conn.rollback()
            logger.error(f"更新映射失败: {str(e)}")
            return False
    
    def delete_mapping(self, id: int) -> bool:
        """
        删除映射
        
        Args:
            id: 映射ID
            
        Returns:
            bool: 是否成功删除
        """
        if not self.conn or not self.cursor:
            logger.error("数据库未连接")
            return False
        
        try:
            # 使用逻辑删除，设置is_active=0
            delete_sql = """
            UPDATE `room_stream_mapping` 
            SET `is_active` = 0
            WHERE `id` = %s
            """
            
            self.cursor.execute(delete_sql, (id,))
            if self.cursor.rowcount == 0:
                logger.warning(f"没有找到ID为 {id} 的映射记录")
                self.conn.rollback()
                return False
            
            self.conn.commit()
            
            logger.info(f"成功删除(禁用)ID为 {id} 的映射")
            return True
            
        except Exception as e:
            self.conn.rollback()
            logger.error(f"删除映射失败: {str(e)}")
            return False
    
    def get_room_by_ip(self, ip_address: str) -> Optional[Dict]:
        """
        根据IP地址查询房间信息
        
        Args:
            ip_address: IP地址
            
        Returns:
            Optional[Dict]: 房间信息，如果未找到则返回None
        """
        if not self.conn or not self.cursor:
            logger.error("数据库未连接")
            return None
        
        try:
            self.cursor.execute("""
                SELECT * FROM `room_stream_mapping` 
                WHERE `ip_address` = %s AND `is_active` = 1
            """, (ip_address,))
            
            result = self.cursor.fetchone()
            if result:
                logger.info(f"通过IP地址 {ip_address} 找到房间: {result['room_name']}")
            else:
                logger.warning(f"未通过IP地址 {ip_address} 找到房间")
            
            return result
            
        except Exception as e:
            logger.error(f"根据IP地址查询房间失败: {str(e)}")
            return None
    
    def get_room_by_stream_url(self, stream_url: str) -> Optional[Dict]:
        """
        根据流媒体URL查询房间信息
        
        Args:
            stream_url: 流媒体URL
            
        Returns:
            Optional[Dict]: 房间信息，如果未找到则返回None
        """
        if not self.conn or not self.cursor:
            logger.error("数据库未连接")
            return None
        
        try:
            self.cursor.execute("""
                SELECT * FROM `room_stream_mapping` 
                WHERE `stream_url` = %s AND `is_active` = 1
            """, (stream_url,))
            
            result = self.cursor.fetchone()
            if result:
                logger.info(f"通过流媒体URL找到房间: {result['room_name']}")
            else:
                logger.warning(f"未通过流媒体URL {stream_url} 找到房间")
            
            return result
            
        except Exception as e:
            logger.error(f"根据流媒体URL查询房间失败: {str(e)}")
            return None
    
    def get_all_mappings(self, active_only: bool = True) -> List[Dict]:
        """
        获取所有映射
        
        Args:
            active_only: 是否只获取激活的映射
            
        Returns:
            List[Dict]: 映射列表
        """
        if not self.conn or not self.cursor:
            logger.error("数据库未连接")
            return []
        
        try:
            if active_only:
                self.cursor.execute("""
                    SELECT * FROM `room_stream_mapping` 
                    WHERE `is_active` = 1
                    ORDER BY `id`
                """)
            else:
                self.cursor.execute("""
                    SELECT * FROM `room_stream_mapping` 
                    ORDER BY `id`
                """)
            
            results = self.cursor.fetchall()
            logger.info(f"获取到 {len(results)} 条映射记录")
            return results
            
        except Exception as e:
            logger.error(f"获取所有映射失败: {str(e)}")
            return []
    
    def get_mapping_by_id(self, id: int) -> Optional[Dict]:
        """
        根据ID获取映射
        
        Args:
            id: 映射ID
            
        Returns:
            Optional[Dict]: 映射信息，如果未找到则返回None
        """
        if not self.conn or not self.cursor:
            logger.error("数据库未连接")
            return None
        
        try:
            self.cursor.execute("""
                SELECT * FROM `room_stream_mapping` 
                WHERE `id` = %s
            """, (id,))
            
            result = self.cursor.fetchone()
            if result:
                logger.info(f"找到ID为 {id} 的映射: {result['room_name']}")
            else:
                logger.warning(f"未找到ID为 {id} 的映射")
            
            return result
            
        except Exception as e:
            logger.error(f"根据ID获取映射失败: {str(e)}")
            return None


def main():
    """主函数，处理命令行参数"""
    parser = argparse.ArgumentParser(description='房间流媒体映射管理工具')
    parser.add_argument('--init', '-i', action='store_true', help='初始化映射表')
    parser.add_argument('--import', '-m', dest='import_file', help='从JSON文件导入映射')
    parser.add_argument('--export', '-e', dest='export_file', help='导出映射到JSON文件')
    parser.add_argument('--list', '-l', action='store_true', help='列出所有映射')
    parser.add_argument('--add', '-a', action='store_true', help='添加新映射')
    parser.add_argument('--update', '-u', type=int, help='更新指定ID的映射')
    parser.add_argument('--delete', '-d', type=int, help='删除指定ID的映射')
    parser.add_argument('--get-by-ip', '-ip', help='根据IP地址查询映射')
    parser.add_argument('--get-by-url', '-url', help='根据流媒体URL查询映射')
    
    # 添加映射参数
    parser.add_argument('--room-name', help='诊室名称')
    parser.add_argument('--ip-address', help='IP地址')
    parser.add_argument('--stream-url', help='流媒体URL')
    parser.add_argument('--description', help='描述信息')
    parser.add_argument('--active', type=int, choices=[0, 1], help='是否激活')
    
    args = parser.parse_args()
    
    # 创建管理器
    manager = RoomMappingManager()
    
    # 连接数据库
    if not manager.connect_db():
        return 1
    
    try:
        # 初始化表
        if args.init:
            if manager.init_table():
                print("成功初始化房间映射表")
            else:
                print("初始化房间映射表失败")
                return 1
        
        # 导入映射
        if args.import_file:
            if os.path.exists(args.import_file):
                if manager.import_from_json(args.import_file):
                    print(f"成功从 {args.import_file} 导入映射数据")
                else:
                    print(f"从 {args.import_file} 导入映射数据失败")
                    return 1
            else:
                print(f"文件不存在: {args.import_file}")
                return 1
        
        # 导出映射
        if args.export_file:
            if manager.export_to_json(args.export_file):
                print(f"成功导出映射数据到 {args.export_file}")
            else:
                print(f"导出映射数据到 {args.export_file} 失败")
                return 1
        
        # 列出所有映射
        if args.list:
            mappings = manager.get_all_mappings()
            if mappings:
                print(f"找到 {len(mappings)} 条映射记录:")
                
                # 格式化输出
                format_str = "{:<4} {:<20} {:<20} {:<50} {:<30}"
                print(format_str.format("ID", "诊室名称", "IP地址", "流媒体URL", "描述"))
                print("-" * 120)
                
                for item in mappings:
                    print(format_str.format(
                        item['id'],
                        item['room_name'],
                        item['ip_address'],
                        item['stream_url'],
                        item['description'] or ''
                    ))
            else:
                print("未找到映射记录")
        
        # 添加新映射
        if args.add:
            if not args.room_name or not args.ip_address or not args.stream_url:
                print("添加映射需要提供诊室名称、IP地址和流媒体URL")
                return 1
            
            if manager.add_mapping(args.room_name, args.ip_address, args.stream_url, args.description):
                print(f"成功添加映射: {args.room_name}")
            else:
                print("添加映射失败")
                return 1
        
        # 更新映射
        if args.update is not None:
            if manager.update_mapping(
                args.update, 
                args.room_name, 
                args.ip_address, 
                args.stream_url, 
                args.description,
                args.active
            ):
                print(f"成功更新ID为 {args.update} 的映射")
            else:
                print(f"更新ID为 {args.update} 的映射失败")
                return 1
        
        # 删除映射
        if args.delete is not None:
            if manager.delete_mapping(args.delete):
                print(f"成功删除ID为 {args.delete} 的映射")
            else:
                print(f"删除ID为 {args.delete} 的映射失败")
                return 1
        
        # 根据IP地址查询
        if args.get_by_ip:
            room = manager.get_room_by_ip(args.get_by_ip)
            if room:
                print(f"找到映射: ID={room['id']}, 诊室={room['room_name']}, URL={room['stream_url']}")
            else:
                print(f"未找到IP地址为 {args.get_by_ip} 的映射")
        
        # 根据URL查询
        if args.get_by_url:
            room = manager.get_room_by_stream_url(args.get_by_url)
            if room:
                print(f"找到映射: ID={room['id']}, 诊室={room['room_name']}, IP={room['ip_address']}")
            else:
                print(f"未找到流媒体URL为 {args.get_by_url} 的映射")
        
        # 如果没有指定操作，显示帮助
        if not any([
            args.init, args.import_file, args.export_file, args.list, 
            args.add, args.update is not None, args.delete is not None,
            args.get_by_ip, args.get_by_url
        ]):
            parser.print_help()
        
        return 0
        
    finally:
        # 关闭数据库连接
        manager.close_db()


if __name__ == "__main__":
    sys.exit(main()) 