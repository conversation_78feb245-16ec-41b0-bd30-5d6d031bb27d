#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
初始化房间映射表
------------
此脚本用于初始化房间映射表，并将现有的映射数据导入到数据库中。
"""

import os
import sys
import argparse
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('init_room_mapping.log')
    ]
)
logger = logging.getLogger(__name__)

# 获取项目根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

# 导入房间映射管理器
try:
    from front.room_mapping_manager import RoomMappingManager
except ImportError as e:
    logger.error(f"导入房间映射管理器失败: {e}")
    sys.exit(1)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='初始化房间映射表')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--export', '-e', help='导出数据到指定的JSON文件')
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 使用默认配置文件路径
    config_path = args.config
    if not config_path:
        # 尝试多个可能的配置文件路径
        possible_paths = [
            os.path.join(root_dir, 'stream_capture', 'config', 'room_stream_mapping.json'),
            os.path.join(root_dir, 'config', 'room_stream_mapping.json'),
            os.path.join('stream_capture', 'config', 'room_stream_mapping.json'),
            os.path.join('config', 'room_stream_mapping.json'),
            'room_stream_mapping.json'
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                config_path = path
                logger.info(f"找到配置文件: {config_path}")
                break
        else:
            logger.error(f"找不到配置文件，尝试路径: {', '.join(possible_paths)}")
            return 1
    
    # 创建管理器
    manager = RoomMappingManager()
    
    # 连接数据库
    if not manager.connect_db():
        logger.error("连接数据库失败")
        return 1
    
    try:
        # 初始化表
        if not manager.init_table():
            logger.error("初始化房间映射表失败")
            return 1
        
        logger.info("房间映射表初始化成功")
        
        # 导入映射数据
        if not manager.import_from_json(config_path):
            logger.error(f"从 {config_path} 导入映射数据失败")
            return 1
        
        logger.info(f"从 {config_path} 导入映射数据成功")
        
        # 导出数据
        if args.export:
            if not manager.export_to_json(args.export):
                logger.error(f"导出映射数据到 {args.export} 失败")
                return 1
            
            logger.info(f"成功导出映射数据到 {args.export}")
        
        # 显示所有映射
        mappings = manager.get_all_mappings()
        if mappings:
            logger.info(f"当前有 {len(mappings)} 条映射记录:")
            
            # 格式化输出
            format_str = "{:<4} {:<20} {:<20} {:<50} {:<30}"
            headers = ["ID", "诊室名称", "IP地址", "流媒体URL", "描述"]
            logger.info(format_str.format(*headers))
            logger.info("-" * 120)
            
            for item in mappings:
                logger.info(format_str.format(
                    item['id'],
                    item['room_name'],
                    item['ip_address'],
                    item['stream_url'],
                    item['description'] or ''
                ))
        else:
            logger.warning("未找到映射记录")
        
        return 0
        
    except Exception as e:
        logger.error(f"初始化过程中发生错误: {str(e)}")
        return 1
        
    finally:
        # 关闭数据库连接
        manager.close_db()

if __name__ == "__main__":
    sys.exit(main()) 