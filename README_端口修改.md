# 医患交互对话识别系统 - 端口配置指南

## 概述

医患交互对话识别系统默认在端口 **5000** 上运行。本文档介绍如何修改系统的运行端口，以适应不同的网络环境需求。

## 方法一：使用命令行参数

启动服务时可以通过命令行参数直接指定端口号：

```bash
# 使用--port参数指定端口
bash start_service.sh --port 8080
```

或者简写形式：

```bash
bash start_service.sh -p 8080
```

这将使系统在端口8080上运行，而不是默认的5000端口。

## 方法二：修改脚本中的默认端口

如果需要永久修改默认端口，可以编辑 `start_service.sh` 文件中的 `PORT` 变量：

1. 打开 `start_service.sh` 文件：
   ```bash
   nano start_service.sh
   ```

2. 找到以下行（约在第30行附近）：
   ```bash
   PORT=5000 # 默认端口为5000
   ```

3. 修改为所需的端口号，例如：
   ```bash
   PORT=8080 # 默认端口为8080
   ```

4. 保存文件并退出编辑器

这样，即使不使用命令行参数，系统也会使用新设置的默认端口。

## 验证端口更改

启动服务后，可以通过以下方式验证端口更改是否生效：

1. 查看日志文件：
   ```bash
   tail -f logs/service.log
   ```
   应该能看到类似 `正在启动API服务，监听地址: 0.0.0.0:8080` 的记录

2. 检查端口占用情况：
   ```bash
   netstat -tunlp | grep python
   ```
   或
   ```bash
   lsof -i :8080
   ```

## 注意事项

1. 确保选择的端口未被其他服务占用
2. 如果系统运行在防火墙后面，需要确保新端口已在防火墙中开放
3. 修改端口后，需要更新任何访问该服务的客户端配置
4. 如果更改后无法连接服务，请检查：
   - 端口是否被占用
   - 防火墙设置
   - 服务是否正常启动（`tail -f logs/service.log`）

## 高级配置

如果需要更详细的网络配置，可以修改 `stream_capture/main.py` 文件，该文件包含更多可自定义的网络参数。 