#!/bin/bash

# 视频流捕获服务停止脚本
# 作者: Claude
# 日期: 2023-08-16
# 更新: 2024-03-27 - 使用统一日志目录

# 切换到项目根目录
cd "$(dirname "$0")"

# 统一日志目录
LOGS_DIR="logs"
MAIN_LOG="$LOGS_DIR/service.log"

# 检查日志目录是否存在
if [ ! -d "$LOGS_DIR" ]; then
    echo "警告: 日志目录 $LOGS_DIR 不存在，将创建"
    mkdir -p "$LOGS_DIR"
fi

# 检查服务是否正在运行
PID_FILE=".service.pid"
if [ ! -f "$PID_FILE" ]; then
    echo "未找到PID文件，服务可能未运行。"
    exit 1
fi

PID=$(cat "$PID_FILE")
if ! ps -p $PID > /dev/null; then
    echo "服务未运行 (PID: $PID)。"
    rm "$PID_FILE"
    exit 1
fi

# 停止服务
echo "正在停止视频流捕获服务 (PID: $PID)..."
echo "$(date): 停止服务" >> "$MAIN_LOG"

# 发送终止信号
kill $PID

# 等待服务结束
echo "等待服务终止..."
sleep 2

# 检查服务是否已终止
if ps -p $PID > /dev/null; then
    echo "服务未能正常终止，尝试强制终止..."
    kill -9 $PID
    sleep 1
fi

# 最终检查
if ps -p $PID > /dev/null; then
    echo "警告: 无法终止服务 (PID: $PID)。"
    exit 1
else
    echo "服务已成功停止。"
    echo "$(date): 服务已成功停止" >> "$MAIN_LOG"
    rm "$PID_FILE"
fi

exit 0 