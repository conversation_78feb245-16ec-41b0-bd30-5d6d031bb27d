#!/bin/bash

# 视频流捕获服务启动脚本
# 作者: <PERSON>
# 日期: 2023-08-16
# 更新: 2023-08-25 - 添加对Agent集成的支持
# 更新: 2024-03-27 - 统一日志路径，收集全流程日志
# 更新: 2025-04-07 - 添加端口配置选项

# 切换到项目根目录
cd "$(dirname "$0")"

# 设置彩色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 解析命令行参数
PARAMS=""
PORT=5000 # 默认端口为5000

while (( "$#" )); do
  case "$1" in
    -p|--port)
      if [ -n "$2" ] && [ ${2:0:1} != "-" ]; then
        PORT=$2
        shift 2
      else
        error "参数错误: $1 需要一个端口号"
        exit 1
      fi
      ;;
    *) # 保留其他参数
      PARAMS="$PARAMS $1"
      shift
      ;;
  esac
done

# 设置统一的日志目录和文件
LOGS_DIR="logs"
MAIN_LOG="$LOGS_DIR/service.log"
API_LOG="$LOGS_DIR/api.log"
AUDIO_LOG="$LOGS_DIR/audio.log"
AGENT_LOG="$LOGS_DIR/agent.log"

# 确保必要的目录存在
info "创建必要的目录..."
mkdir -p "$LOGS_DIR"
mkdir -p stream_capture/recordings
mkdir -p SeparatedAudioText
mkdir -p stream_capture/config
mkdir -p temp
mkdir -p agent
mkdir -p backend
success "目录创建完成"

# 检查配置文件是否存在
CONFIG_FILE="stream_capture/config/room_stream_mapping.json"
if [ ! -f "$CONFIG_FILE" ]; then
    warning "配置文件不存在，将创建默认配置"
    cat > "$CONFIG_FILE" <<EOL
[
  {
    "room_name": "A402诊室",
    "ip_address": "************",
    "stream_url": "https://livell.cdutcm.edu.cn/live/f2ya.live.flv"
  },
  {
    "room_name": "笔记本电脑",
    "ip_address": "**************",
    "stream_url": "https://livell.cdutcm.edu.cn/live/efy1a.live.flv"
  }
]
EOL
    success "已创建默认配置文件: $CONFIG_FILE"
    info "请根据实际情况修改配置文件中的流媒体URL"
fi

# 检查是否已经有服务在运行
PID_FILE=".service.pid"
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null; then
        warning "服务已经在运行 (PID: $PID)。如需重启，请先停止服务。"
        info "停止命令: bash stop_service.sh"
        exit 1
    else
        warning "发现过期的PID文件，将删除。"
        rm "$PID_FILE"
    fi
fi

# 检查Python是否已安装
if ! command -v python3 &> /dev/null; then
    error "未找到Python3。请安装Python3后再试。"
    exit 1
fi

# 检查ffmpeg是否已安装
if ! command -v ffmpeg &> /dev/null; then
    warning "未找到ffmpeg。音频提取功能可能无法正常工作。"
    info "请安装ffmpeg: sudo apt-get install ffmpeg"
fi

# 检查必要的Python包
info "检查必要的Python包..."
python3 -c "import flask" 2>/dev/null || warning "未安装flask包，API服务可能无法启动"
python3 -c "import cv2" 2>/dev/null || warning "未安装opencv-python包，视频处理可能无法正常工作"
python3 -c "import numpy" 2>/dev/null || warning "未安装numpy包，音频处理可能无法正常工作"
python3 -c "import requests" 2>/dev/null || warning "未安装requests包，Agent集成可能无法正常工作"

# 检查配置文件是否有权限问题
if [ -f "$CONFIG_FILE" ] && [ ! -r "$CONFIG_FILE" ]; then
    warning "配置文件没有读取权限，修复中..."
    chmod +r "$CONFIG_FILE"
    success "已修复配置文件权限"
fi

# 检查视频录制目录是否有写入权限
RECORDINGS_DIR="stream_capture/recordings"
if [ ! -w "$RECORDINGS_DIR" ]; then
    warning "录制目录没有写入权限，修复中..."
    chmod -R +w "$RECORDINGS_DIR"
    success "已修复录制目录权限"
fi

# 清空之前的日志文件（可选）
info "重置日志文件..."
> "$MAIN_LOG"
> "$API_LOG" 
> "$AUDIO_LOG"
> "$AGENT_LOG"
success "日志文件已重置"

# 设置日志环境变量，让Python应用能够获取统一的日志目录
export AUDIO_SEPARATION_LOGS_DIR="$LOGS_DIR"

# 设置音频处理并发参数
export AUDIO_SEPARATION_CONCURRENT="true"        # 启用并发模式
export AUDIO_SEPARATION_POOL_SIZE="3"            # 模型池大小（根据GPU内存调整）
export AUDIO_SEPARATION_MAX_WORKERS="4"          # 最大工作线程数

# 启动服务
info "正在启动视频流捕获服务，端口: $PORT..."
echo "$(date): 启动服务，端口: $PORT" >> "$MAIN_LOG"

# 构建启动命令，传递日志目录参数和端口参数
START_CMD="python3 stream_capture/main.py --logs-dir $LOGS_DIR --port $PORT"

# 使用nohup在后台运行，并将输出重定向到主日志文件
nohup $START_CMD > "$MAIN_LOG" 2>&1 &

# 保存进程ID
echo $! > "$PID_FILE"
success "服务已在后台启动 (PID: $!)。"
info "所有日志将保存在 $LOGS_DIR 目录下："
info " - 主服务日志: $MAIN_LOG"
info " - API服务日志: $API_LOG"
info " - 音频处理日志: $AUDIO_LOG"
info " - Agent处理日志: $AGENT_LOG"
info "查看主日志: tail -f $MAIN_LOG"
info "停止服务: bash stop_service.sh"
info "测试Agent集成: python3 agent/test_agent.py --list"
info "当前服务运行端口: $PORT"

exit 0 