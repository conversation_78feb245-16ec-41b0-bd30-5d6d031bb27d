# 设计文档

## 概述

本设计文档描述了医疗对话分析系统的详细架构设计。系统采用分层架构，包括接口与调度层、核心引擎层和数据存储层，通过异步处理和并发优化来实现高性能的音视频处理和AI分析能力。

## 架构

### 系统架构原则

1. **关注点分离 (SoC)**: stream_capture模块负责Web接口和视频录制，backend模块负责AI计算任务
2. **异步与解耦**: 录制模块与后台处理模块通过异步调用解耦，提高响应能力
3. **高内聚低耦合**: 模块内部功能高度相关，模块间通过清晰接口交互
4. **可扩展性**: 采用模型池+线程池架构，支持水平扩展

### 逻辑架构

```mermaid
graph TD
    subgraph "外部系统"
        HIS[医院信息系统]
    end

    subgraph "接口与调度层 (stream_capture)"
        API[API服务]
        SH[信号与任务管理器]
        RMM[诊室映射管理器]
        PC[生产者-消费者录制引擎]
    end

    subgraph "核心引擎层 (backend)"
        CA[并发任务调度器]
        AP[AI处理流水线]
    end

    subgraph "数据存储层"
        DB[(MySQL数据库)]
        S3[(文件系统/MinIO)]
    end
    
    subgraph "外部服务"
        LLM[LLM Agent服务]
    end

    HIS --> API
    API --> SH
    SH --> RMM
    RMM --> DB
    SH --> PC
    PC --> S3
    SH --> CA
    CA --> AP
    AP --> LLM
    AP --> S3
    AP --> DB
```

### 部署架构

系统设计为单机部署模式，所有组件运行在同一台服务器上：

```mermaid
graph TD
    subgraph "服务器"
        subgraph "应用容器"
            Flask[Flask Web服务器]
            Backend[后台处理服务]
        end
        
        subgraph "存储服务"
            MySQL[(MySQL Server)]
            MinIO[(MinIO Server)]
        end
        
        Flask -.-> Backend
        Backend --> MySQL
        Backend --> MinIO
        Flask --> MySQL
    end
    
    User[外部系统] --> Flask
```

## 组件和接口

### 接口与调度层组件

#### API服务 (api/signal_receiver.py)
- **职责**: HTTP请求网关，参数验证和路由分发
- **接口**:
  - `POST /api/signal/start`: 启动录制任务
  - `POST /api/signal/stop`: 停止录制任务
- **实现**: 基于Flask框架，委托业务逻辑给SignalHandler

#### 信号与任务管理器 (core/signal/signal_handler.py)
- **职责**: 任务生命周期管理，录制线程调度
- **核心方法**:
  - `start_recording(task_id, mac_address, clinical_info)`: 启动录制
  - `stop_recording(task_id)`: 停止录制并触发后台处理
- **状态管理**: 内存字典维护active_tasks状态

#### 诊室映射管理器 (front/room_mapping_manager.py)
- **职责**: 设备准入验证，配置管理
- **实现**: 启动时从数据库加载room_stream_mapping到内存缓存
- **接口**: `get_stream_info(mac_address)` 返回stream_url和room_name

#### 视频录制引擎
- **生产者 (core/producer/stream_capturer.py)**:
  - 使用OpenCV从视频流URL读取帧
  - 将帧放入任务专用的内存队列
- **消费者 (core/consumer/stream_saver.py)**:
  - 从队列获取帧并通过ffmpeg管道编码
  - 生成MP4视频文件

### 核心引擎层组件

#### 并发任务调度器 (backend/app_api_concurrent.py)
- **职责**: 管理模型池和工作线程池
- **架构特性**:
  - 预加载多份FunASR模型实例到GPU显存
  - 维护固定大小的工作线程池
  - 动态分配模型实例给工作线程

#### AI处理流水线 (backend/audio_processor.py)
- **处理步骤**:
  1. 音频提取: ffmpeg从MP4提取16kHz单声道WAV
  2. 说话人分离: FunASR模型标记说话人
  3. 语音识别: 生成带时间戳的文本
  4. 辨证论治分析: 调用外部LLM Agent
  5. 数据持久化: 文件归档和数据库存储

### 外部服务接口

#### LLM Agent服务
- **协议**: HTTP REST API
- **请求格式**: JSON包含对话文本和临床信息
- **响应格式**: 结构化JSON包含诊断、证型、治法、方剂等

## 数据模型

### 核心数据表

#### medical_conversation (医患对话记录表)
```sql
CREATE TABLE medical_conversation (
    task_id VARCHAR(255) PRIMARY KEY,
    room_name VARCHAR(255),
    full_video_url VARCHAR(1024),
    sentences_json JSON,
    medical_record_json JSON,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### room_stream_mapping (诊室流媒体映射表)
```sql
CREATE TABLE room_stream_mapping (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_name VARCHAR(100) UNIQUE,
    mac_address VARCHAR(50) UNIQUE,
    stream_url VARCHAR(255) UNIQUE,
    description VARCHAR(255),
    is_active TINYINT(1),
    create_time DATETIME,
    update_time DATETIME
);
```

#### conversation_speaker_clips (对话视频切片表)
```sql
CREATE TABLE conversation_speaker_clips (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id VARCHAR(255),
    speaker_id INT,
    segment_id VARCHAR(255),
    clip_url VARCHAR(1024),
    FOREIGN KEY (task_id) REFERENCES medical_conversation(task_id)
);
```

### 文件存储结构
```
SeparatedAudioText/
├── 诊室名/
│   └── 任务ID/
│       ├── full_transcript.txt
│       ├── speaker_0.txt
│       ├── speaker_0.mp3
│       ├── speaker_1.txt
│       ├── speaker_1.mp3
│       ├── sentences.json
│       ├── agent_response.json
│       └── medical_info.json
```

## 错误处理

### 错误分类和处理策略

#### API层错误
- **400 Bad Request**: 请求格式错误或缺少必要字段
- **403 Forbidden**: 设备未注册或已禁用
- **404 Not Found**: 任务ID不存在
- **500 Internal Server Error**: 系统内部错误

#### 录制层错误
- **视频流连接失败**: 重试3次，间隔5秒，最终失败记录严重错误
- **磁盘写入失败**: 立即停止任务，记录错误，清理资源
- **线程异常**: 优雅停止相关线程，释放资源

#### AI处理层错误
- **模型加载失败**: 记录错误，使用备用模型或降级处理
- **Agent服务调用失败**: 重试机制，超时处理，保存已有结果
- **数据库写入失败**: 事务回滚，错误记录，数据一致性保证

### 日志记录策略
- **操作日志**: 记录关键业务操作和状态变更
- **错误日志**: 详细记录异常信息、堆栈跟踪和上下文
- **性能日志**: 记录处理时间、资源使用情况
- **审计日志**: 记录用户操作和数据访问

## 测试策略

### 单元测试
- API接口参数验证和响应格式测试
- 核心业务逻辑组件测试
- 数据库操作和数据模型测试
- 错误处理和异常情况测试

### 集成测试
- 端到端业务流程测试
- 外部服务集成测试
- 数据库和文件系统集成测试
- 并发处理能力测试

### 性能测试
- 并发录制任务处理能力
- AI模型处理性能和资源使用
- 数据库查询和写入性能
- 系统整体吞吐量测试

### 可靠性测试
- 系统故障恢复测试
- 数据一致性和完整性测试
- 长时间运行稳定性测试
- 资源泄漏和内存管理测试