# 需求文档

## 介绍

本项目旨在构建一个医疗场景下的智能对话分析系统，通过捕获医患沟通的音视频流，利用语音识别、说话人分离和自然语言处理技术，实现对话的实时记录、结构化信息提取和基于中医辨证论治理论的初步分析，最终生成结构化的电子病历并存入数据库，为医生提供临床决策支持。

## 需求

### 需求 1

**用户故事:** 作为医院信息系统管理员，我希望能够通过API接口启动和停止视频录制任务，以便在医患对话期间自动记录诊疗过程。

#### 验收标准

1. WHEN 外部系统发送包含task_id、mac_address和临床信息的POST请求到/api/signal/start THEN 系统SHALL验证设备准入并启动视频录制
2. WHEN 设备mac_address在数据库中不存在或未激活 THEN 系统SHALL返回错误响应并拒绝录制请求
3. WHEN 外部系统发送包含task_id的POST请求到/api/signal/stop THEN 系统SHALL停止对应的录制任务并触发后台处理
4. WHEN 录制任务成功启动 THEN 系统SHALL返回包含状态和诊室名称的成功响应
5. WHEN 录制任务成功停止 THEN 系统SHALL返回包含状态、消息和录制时长的成功响应

### 需求 2

**用户故事:** 作为系统用户，我希望系统能够自动从录制的视频中提取音频并进行说话人分离和语音识别，以便获得结构化的对话文本。

#### 验收标准

1. WHEN 视频录制完成后 THEN 系统SHALL自动从MP4文件中提取16kHz单声道WAV音频
2. WHEN 音频提取完成后 THEN 系统SHALL使用FunASR模型进行说话人分离，为每个语音片段标记说话人
3. WHEN 说话人分离完成后 THEN 系统SHALL对每个说话人的语音进行识别，生成带时间戳的文本
4. WHEN 语音识别完成后 THEN 系统SHALL生成完整的对话转录文本和各说话人的音频切片文件
5. WHEN 处理过程中发生错误 THEN 系统SHALL记录错误日志并停止后续处理流程

### 需求 3

**用户故事:** 作为医生，我希望系统能够基于对话内容和临床信息进行中医辨证论治分析，以便获得结构化的电子病历草稿。

#### 验收标准

1. WHEN 对话文本生成完成后 THEN 系统SHALL结合初始临床信息构造结构化的分析请求
2. WHEN 分析请求构造完成后 THEN 系统SHALL调用外部LLM Agent服务进行辨证论治分析
3. WHEN Agent服务返回分析结果 THEN 系统SHALL解析JSON响应并提取诊断、证型、治法、方剂等结构化信息
4. WHEN 分析结果解析完成后 THEN 系统SHALL生成完整的结构化电子病历数据
5. WHEN Agent服务调用失败或超时 THEN 系统SHALL记录错误并保存已有的处理结果

### 需求 4

**用户故事:** 作为系统管理员，我希望所有处理结果能够安全可靠地存储到数据库和文件系统中，以便后续查询和审计。

#### 验收标准

1. WHEN AI分析完成后 THEN 系统SHALL将结构化的电子病历信息写入medical_conversation表
2. WHEN 数据库写入完成后 THEN 系统SHALL将所有相关文件归档到指定的目录结构中
3. WHEN 文件归档完成后 THEN 系统SHALL可选地将文件备份到MinIO对象存储
4. WHEN 存储过程中发生错误 THEN 系统SHALL回滚已执行的操作并记录详细错误信息
5. WHEN 所有数据持久化完成后 THEN 系统SHALL更新任务状态并发送完成通知

### 需求 5

**用户故事:** 作为系统运维人员，我希望系统能够支持并发处理多个录制和分析任务，以便提高系统的处理能力和响应速度。

#### 验收标准

1. WHEN 多个录制请求同时到达 THEN 系统SHALL为每个任务分配独立的生产者-消费者线程对
2. WHEN 多个AI处理任务需要执行 THEN 系统SHALL使用模型池和线程池进行并发处理
3. WHEN 系统资源不足时 THEN 系统SHALL将新任务加入队列等待处理
4. WHEN 任务执行完成后 THEN 系统SHALL释放相关资源并更新任务状态
5. WHEN 系统关闭时 THEN 系统SHALL优雅地停止所有正在进行的任务并保存中间结果

### 需求 6

**用户故事:** 作为医院IT管理员，我希望系统提供完善的错误处理和日志记录功能，以便快速定位和解决问题。

#### 验收标准

1. WHEN API请求格式错误或缺少必要字段 THEN 系统SHALL返回400 Bad Request并提供明确的错误信息
2. WHEN 视频流连接失败 THEN 系统SHALL进行重试并在最终失败时记录严重错误
3. WHEN 磁盘写入失败 THEN 系统SHALL立即停止任务并记录错误，避免触发后续处理
4. WHEN 系统组件发生异常 THEN 系统SHALL记录详细的错误日志包括堆栈信息和上下文
5. WHEN 关键操作执行时 THEN 系统SHALL记录操作日志以便审计和问题追踪