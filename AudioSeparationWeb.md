# AudioSeparationWeb - 说话人分离系统Web版

这是基于FunASR的说话人分离系统的Web版本，用于在服务器环境中运行，支持多用户并发处理音频和视频文件。

## 1. 系统架构

系统采用前后端分离的架构：

- **前端**：使用HTML、CSS和JavaScript构建的Web界面，基于Bootstrap 5框架
- **后端**：基于Flask的RESTful API服务，处理文件上传、音频处理和结果返回
- **处理引擎**：使用FunASR模型进行语音识别和说话人分离
- **并发处理**：使用多线程处理多个任务，支持并发请求

## 2. 功能特性

- 支持多种音频格式（mp3, m4a, aac, ogg, wav, flac, wma, aif）

- 支持多种视频格式（mp4, avi, mov, mkv）
- 支持多文件批量上传和处理
- 自动识别不同说话人并分离音频
- 生成包含时间戳的文本记录
- 合并同一说话人的所有音频片段
- 实时显示处理进度和状态
- 支持下载分离后的音频片段和合并文件
- 支持在线预览和播放音频片段

## 3. 目录结构

```
AudioSeparationWeb/                # 项目根目录
├── backend/                       # 后端代码目录
│   └── app_server.py              # 后端服务主程序，包含API接口和处理逻辑
├── web/                           # 前端代码目录
│   ├── index.html                 # 主页面HTML
│   ├── css/                       # 样式表目录
│   │   └── style.css              # 主样式表
│   └── js/                        # JavaScript代码目录
│       └── app.js                 # 前端逻辑和交互
├── uploads/                       # 上传文件临时存储目录
├── SeparatedAudioText/            # 处理结果输出目录
│   └── YYYY-MM-DD/                # 按日期组织的输出文件
│       └── [音频名称]/            # 按原始文件名组织的目录
│           ├── [说话人ID]/        # 按说话人ID组织的目录
│           │   └── [片段文件]     # 分离后的音频/视频片段
│           ├── [说话人ID].mp3     # 合并后的说话人音频
│           └── spk[说话人ID].txt  # 说话人对应的文本记录
├── download_model.py              # 模型下载脚本
├── requirements.txt               # 项目依赖文件
└── AudioSeparationWeb.md          # 项目说明文档
```

### 3.1 关键文件说明

- **app_server.py**: 后端核心程序，包含Flask服务器、API接口和音频处理逻辑
- **index.html**: 前端主页面，提供用户界面
- **style.css**: 定义前端界面样式
- **app.js**: 前端交互逻辑，处理用户操作和API调用
- **download_model.py**: 用于下载FunASR模型的脚本
- **requirements.txt**: 列出项目所需的Python依赖包

### 3.2 数据流向

1. 用户通过web/index.html上传文件
2. 文件暂存到uploads/目录
3. backend/app_server.py处理文件
4. 处理结果保存到SeparatedAudioText/目录
5. 用户通过前端界面查看和下载结果

## 4. 后端API接口

### 4.1 文件上传

- **URL**: `/api/upload`
- **方法**: POST
- **参数**:
  - `file`: 音频或视频文件（可多个）
  - `split_chars`: 分离字数（控制每个音频片段的最大字符数）
- **返回**: 
  ```json
  {
    "task_id": "任务ID",
    "files": [{"filename": "文件名", "status": "状态"}],
    "message": "处理状态信息"
  }
  ```

### 4.2 获取任务状态

- **URL**: `/api/tasks/<task_id>`
- **方法**: GET
- **返回**: 任务详细信息，包括状态、进度、分段信息等

### 4.3 获取所有任务

- **URL**: `/api/tasks`
- **方法**: GET
- **返回**: 所有任务的列表，按创建时间倒序排列

### 4.4 下载文件

- **URL**: `/api/download/<path:filename>`
- **方法**: GET
- **返回**: 文件下载流

## 5. 处理流程

1. **文件上传**：用户通过Web界面上传音频或视频文件
2. **任务创建**：系统创建任务并分配唯一ID
3. **预处理**：使用FFmpeg将音频转换为适合模型处理的格式
4. **语音识别**：使用FunASR模型进行语音识别和说话人分离
5. **分段处理**：根据识别结果和时间戳切分音频/视频
6. **文本生成**：为每个说话人生成带时间戳的文本记录
7. **音频合并**：合并同一说话人的所有音频片段
8. **结果存储**：将处理结果保存到指定目录
9. **结果展示**：在Web界面上展示处理结果，提供下载和预览功能

## 6. 并发处理机制

系统使用多线程处理多个任务，主要包括：

1. **主线程**：处理HTTP请求和响应
2. **处理线程**：为每个上传的文件创建单独的处理线程
3. **文本写入线程**：处理文本记录的写入
4. **音频合并线程**：处理同一说话人的音频片段合并

这种设计允许系统同时处理多个用户的请求，充分利用服务器资源。

## 7. 安装和部署

### 7.1 环境要求

- Python 3.8+
- FFmpeg
- CUDA支持（推荐，用于加速处理）

### 7.2 依赖安装

```bash
pip install -r requirements.txt
```

### 7.3 模型下载

```bash
python download_model.py
```

### 7.4 启动服务

```bash
cd backend
python app_server.py
```

服务默认在 `http://0.0.0.0:5000` 启动，可以通过浏览器访问。

## 8. 使用说明

1. 打开浏览器访问服务地址
2. 点击"选择音频或视频文件"按钮，选择要处理的文件
3. 设置"分离字数"参数（控制每个音频片段的最大字符数）
4. 点击"开始分离"按钮上传文件并开始处理
5. 在"任务状态"区域查看处理进度
6. 点击任务查看详细信息，包括分段详情和合并文件
7. 点击下载按钮下载处理结果，或点击播放按钮在线预览

## 9. 注意事项

- 处理大文件可能需要较长时间，请耐心等待
- 服务器需要足够的存储空间来保存上传的文件和处理结果
- 建议定期清理 `uploads` 和 `SeparatedAudioText` 目录，避免占用过多存储空间
- 如需在生产环境部署，建议使用Nginx等Web服务器进行反向代理，并配置HTTPS

