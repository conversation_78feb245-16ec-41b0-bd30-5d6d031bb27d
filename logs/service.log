2025-07-30 14:19:34,768 - main - INFO - 日志系统初始化完成，日志将保存在 logs 目录
2025-07-30 14:19:34,768 - main - INFO - 使用配置文件: stream_capture/config/room_stream_mapping.json
2025-07-30 14:19:34,768 - main - INFO - 正在初始化视频流捕获服务...
2025-07-30 14:19:34,768 - main - INFO - 正在启动API服务，监听地址: 0.0.0.0:5000
2025-07-30 14:19:34,827 - signal_api - INFO - API日志系统初始化完成，日志将保存在 logs/api.log
2025-07-30 14:19:34,827 - signal_api - INFO - API日志系统初始化完成，日志将保存在 logs/api.log
2025-07-30 14:19:34,827 - signal_api - INFO - 初始化信号处理器
2025-07-30 14:19:34,827 - signal_api - INFO - 初始化信号处理器
2025-07-30 14:19:34,827 - signal_handler - INFO - 信号处理器日志系统初始化完成，日志保存在 logs/signal.log
2025-07-30 14:19:34,880 - room_mapping_dao - INFO - 成功连接到数据库
2025-07-30 14:19:34,883 - room_mapping_dao - INFO - 获取到 23 条房间映射记录
2025-07-30 14:19:34,883 - signal_handler - INFO - 成功加载配置文件，房间映射: ['A402诊室', '笔记本电脑', '徐鑫垚', '王佳豪-网页用', '林轲-网页用', '蒋璐霞-网页用', 'zxy杨雨晴-网页', '王子晋', '付勇志', '赵煜', '蒋璐霞-外网exe', '蒋璐霞-网页端', '金泰铬员工-外网exe', '赵煜-数字孪生', '赵煜-数字孪生网页', '王佳豪-数字孪生网页', '林轲-外网exe', '周良龙', '王佳豪-外网exe', '林轲新-网页端', 'zxy王鹏-网页', 'zxy李欣-网页', '温辛']
2025-07-30 14:19:34,883 - signal_handler - INFO - 音频处理模块已加载，将自动处理录制的视频
2025-07-30 14:19:34,883 - signal_handler - INFO - Dify Agent客户端已加载，将自动发送转写结果给Agent
2025-07-30 14:19:34,883 - signal_api - INFO - 信号处理器初始化完成
2025-07-30 14:19:34,883 - signal_api - INFO - 信号处理器初始化完成
2025-07-30 14:19:34,883 - audio_processor - INFO - 已注册转写就绪回调函数
2025-07-30 14:19:34,883 - main - INFO - 已成功注册转写就绪回调函数
2025-07-30 14:19:34,888 - main - INFO - Flask应用已在线程中启动，监听地址: 0.0.0.0:5000
2025-07-30 14:19:34,888 - main - INFO - 开始预加载ASR模型...
2025-07-30 14:19:34,888 - audio_processor - INFO - 开始预加载ASR模型...
 * Serving Flask app 'stream_capture.api.signal_receiver'
 * Debug mode: off
2025-07-30 14:19:34,893 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-30 14:19:34,893 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 14:19:36,890 - audio_processor - INFO - ASR模型预加载完成
2025-07-30 14:19:36,890 - main - INFO - ASR模型预加载成功启动
2025-07-30 14:19:36,890 - main - INFO - 服务已启动，按 CTRL+C 停止服务
2025-07-30 15:07:29,734 - signal_api - INFO - 接收到医疗信息字段: 患者主诉, 现病史, 既往史, 过敏史
2025-07-30 15:07:29,734 - signal_api - INFO - 接收到医疗信息字段: 患者主诉, 现病史, 既往史, 过敏史
2025-07-30 15:07:29,734 - signal_api - INFO - 接收到开始信号: {"past_medical_history": "乙肝携带者", "allergic_history": "无", "mac_address": "8PSHk3jpIxWni9Xn", "chief_complaint": "右下肢大拇指关节疼痛5天", "present_illness": "右下肢大拇指关节疼痛5天，已进行急诊输液4天", "task_id": "44022407", "userid": "4337917972048773121", "username": "赵煜", "medical_info": {"患者主诉": "右下肢大拇指关节疼痛5天", "现病史": "右下肢大拇指关节疼痛5天，已进行急诊输液4天", "既往史": "乙肝携带者", "过敏史": "无"}}
2025-07-30 15:07:29,734 - signal_api - INFO - 接收到开始信号: {"past_medical_history": "乙肝携带者", "allergic_history": "无", "mac_address": "8PSHk3jpIxWni9Xn", "chief_complaint": "右下肢大拇指关节疼痛5天", "present_illness": "右下肢大拇指关节疼痛5天，已进行急诊输液4天", "task_id": "44022407", "userid": "4337917972048773121", "username": "赵煜", "medical_info": {"患者主诉": "右下肢大拇指关节疼痛5天", "现病史": "右下肢大拇指关节疼痛5天，已进行急诊输液4天", "既往史": "乙肝携带者", "过敏史": "无"}}
2025-07-30 15:07:29,737 - room_mapping_dao - INFO - 通过MAC地址 8PSHk3jpIxWni9Xn 找到房间: 赵煜-数字孪生网页
2025-07-30 15:07:29,737 - signal_handler - INFO - 通过MAC地址 8PSHk3jpIxWni9Xn 找到房间: 赵煜-数字孪生网页
2025-07-30 15:07:29,737 - signal_handler - INFO - 接收到用户信息: userid=4337917972048773121, username=赵煜
2025-07-30 15:07:29,738 - signal_handler - INFO - 已保存医疗信息到文件: SeparatedAudioText/赵煜-数字孪生网页/44022407/medical_info.json
2025-07-30 15:07:29,739 - stream_saver.44022407.mp4 - INFO - 开始录制视频: https://livell.cdutcm.edu.cn/live/zxyzywy.live.flv -> stream_capture/recordings/赵煜-数字孪生网页/44022407.mp4
2025-07-30 15:07:29,743 - stream_saver_manager - INFO - 已启动视频录制: 44022407, 房间: 赵煜-数字孪生网页, 患者: 未知患者, 保存路径: stream_capture/recordings/赵煜-数字孪生网页/44022407.mp4
2025-07-30 15:07:29,745 - werkzeug - INFO - ************* - - [30/Jul/2025 15:07:29] "POST /api/signal/start HTTP/1.1" 200 -
2025-07-30 15:07:45,760 - stream_saver.44022407.mp4 - ERROR - ffmpeg进程意外结束，错误信息: https://livell.cdutcm.edu.cn/live/zxyzywy.live.flv: Server returned 404 Not Found

2025-07-30 15:09:59,912 - signal_api - INFO - 接收到停止信号: {"task_id": "44022407", "userid": "4337917972048773121", "username": "赵煜"}
2025-07-30 15:09:59,912 - signal_api - INFO - 接收到停止信号: {"task_id": "44022407", "userid": "4337917972048773121", "username": "赵煜"}
2025-07-30 15:09:59,912 - stream_saver.44022407.mp4 - WARNING - 没有正在进行的录制: stream_capture/recordings/赵煜-数字孪生网页/44022407.mp4
2025-07-30 15:09:59,913 - werkzeug - INFO - ************* - - [30/Jul/2025 15:09:59] "[31m[1mPOST /api/signal/stop HTTP/1.1[0m" 400 -
2025-07-31 11:27:16,018 - signal_api - INFO - 接收到医疗信息字段: 患者主诉, 现病史, 既往史, 过敏史
2025-07-31 11:27:16,018 - signal_api - INFO - 接收到医疗信息字段: 患者主诉, 现病史, 既往史, 过敏史
2025-07-31 11:27:16,019 - signal_api - INFO - 接收到开始信号: {"past_medical_history": "无", "allergic_history": "无", "mac_address": "mGubK34MDfuATaXK", "chief_complaint": "脚背痛", "present_illness": "脚背痛约一周，未提及已开展的治疗、用药及检查情况", "task_id": "44021877", "userid": "4337917972048773121", "username": "赵煜", "medical_info": {"患者主诉": "脚背痛", "现病史": "脚背痛约一周，未提及已开展的治疗、用药及检查情况", "既往史": "无", "过敏史": "无"}}
2025-07-31 11:27:16,019 - signal_api - INFO - 接收到开始信号: {"past_medical_history": "无", "allergic_history": "无", "mac_address": "mGubK34MDfuATaXK", "chief_complaint": "脚背痛", "present_illness": "脚背痛约一周，未提及已开展的治疗、用药及检查情况", "task_id": "44021877", "userid": "4337917972048773121", "username": "赵煜", "medical_info": {"患者主诉": "脚背痛", "现病史": "脚背痛约一周，未提及已开展的治疗、用药及检查情况", "既往史": "无", "过敏史": "无"}}
2025-07-31 11:27:16,029 - room_mapping_dao - WARNING - 未通过MAC地址 mGubK34MDfuATaXK 找到房间
2025-07-31 11:27:16,030 - room_mapping_dao - INFO - 成功加载备用配置文件，共 2 个房间
2025-07-31 11:27:16,030 - room_mapping_dao - WARNING - 在备用配置中未通过 mac_address=mGubK34MDfuATaXK 找到房间
2025-07-31 11:27:16,030 - signal_handler - ERROR - 找不到MAC地址 mGubK34MDfuATaXK 对应的房间信息
2025-07-31 11:27:16,031 - werkzeug - INFO - ************* - - [31/Jul/2025 11:27:16] "[31m[1mPOST /api/signal/start HTTP/1.1[0m" 400 -
2025-07-31 15:07:02,757 - signal_api - INFO - 接收到医疗信息字段: 患者主诉, 现病史, 既往史, 过敏史
2025-07-31 15:07:02,757 - signal_api - INFO - 接收到医疗信息字段: 患者主诉, 现病史, 既往史, 过敏史
2025-07-31 15:07:02,757 - signal_api - INFO - 接收到开始信号: {"past_medical_history": "有家族痛风史", "allergic_history": "无", "mac_address": "emuT1ChvEnfriQW0", "chief_complaint": "痛风", "present_illness": "痛风3天，已服用非布司他治疗，存在肾衰竭情况", "task_id": "44031116", "userid": "3838360406544351233", "username": "杨雨晴", "medical_info": {"患者主诉": "痛风", "现病史": "痛风3天，已服用非布司他治疗，存在肾衰竭情况", "既往史": "有家族痛风史", "过敏史": "无"}}
2025-07-31 15:07:02,757 - signal_api - INFO - 接收到开始信号: {"past_medical_history": "有家族痛风史", "allergic_history": "无", "mac_address": "emuT1ChvEnfriQW0", "chief_complaint": "痛风", "present_illness": "痛风3天，已服用非布司他治疗，存在肾衰竭情况", "task_id": "44031116", "userid": "3838360406544351233", "username": "杨雨晴", "medical_info": {"患者主诉": "痛风", "现病史": "痛风3天，已服用非布司他治疗，存在肾衰竭情况", "既往史": "有家族痛风史", "过敏史": "无"}}
2025-07-31 15:07:02,761 - room_mapping_dao - INFO - 通过MAC地址 emuT1ChvEnfriQW0 找到房间: zxy杨雨晴-网页
2025-07-31 15:07:02,762 - signal_handler - INFO - 通过MAC地址 emuT1ChvEnfriQW0 找到房间: zxy杨雨晴-网页
2025-07-31 15:07:02,762 - signal_handler - INFO - 接收到用户信息: userid=3838360406544351233, username=杨雨晴
2025-07-31 15:07:02,762 - signal_handler - INFO - 已保存医疗信息到文件: SeparatedAudioText/zxy杨雨晴-网页/44031116/medical_info.json
2025-07-31 15:07:02,763 - stream_saver.44031116.mp4 - INFO - 开始录制视频: https://livell.cdutcm.edu.cn/live/zxyyyq.live.flv -> stream_capture/recordings/zxy杨雨晴-网页/44031116.mp4
2025-07-31 15:07:02,767 - stream_saver_manager - INFO - 已启动视频录制: 44031116, 房间: zxy杨雨晴-网页, 患者: 未知患者, 保存路径: stream_capture/recordings/zxy杨雨晴-网页/44031116.mp4
2025-07-31 15:07:02,768 - werkzeug - INFO - ************* - - [31/Jul/2025 15:07:02] "POST /api/signal/start HTTP/1.1" 200 -
2025-07-31 15:07:18,783 - stream_saver.44031116.mp4 - ERROR - ffmpeg进程意外结束，错误信息: https://livell.cdutcm.edu.cn/live/zxyyyq.live.flv: Server returned 404 Not Found

2025-07-31 17:06:20,654 - signal_api - INFO - 接收到停止信号: {"task_id": "44031116", "userid": "3838360406544351233", "username": "杨雨晴"}
2025-07-31 17:06:20,654 - signal_api - INFO - 接收到停止信号: {"task_id": "44031116", "userid": "3838360406544351233", "username": "杨雨晴"}
2025-07-31 17:06:20,655 - stream_saver.44031116.mp4 - WARNING - 没有正在进行的录制: stream_capture/recordings/zxy杨雨晴-网页/44031116.mp4
2025-07-31 17:06:20,656 - werkzeug - INFO - ************* - - [31/Jul/2025 17:06:20] "[31m[1mPOST /api/signal/stop HTTP/1.1[0m" 400 -
2025-07-31 17:06:22,072 - signal_api - INFO - 接收到停止信号: {"task_id": "44031116", "userid": "3838360406544351233", "username": "杨雨晴"}
2025-07-31 17:06:22,072 - signal_api - INFO - 接收到停止信号: {"task_id": "44031116", "userid": "3838360406544351233", "username": "杨雨晴"}
2025-07-31 17:06:22,072 - stream_saver.44031116.mp4 - WARNING - 没有正在进行的录制: stream_capture/recordings/zxy杨雨晴-网页/44031116.mp4
2025-07-31 17:06:22,073 - werkzeug - INFO - ************* - - [31/Jul/2025 17:06:22] "[31m[1mPOST /api/signal/stop HTTP/1.1[0m" 400 -
2025-07-31 17:06:23,071 - signal_api - INFO - 接收到停止信号: {"task_id": "44031116", "userid": "3838360406544351233", "username": "杨雨晴"}
2025-07-31 17:06:23,071 - signal_api - INFO - 接收到停止信号: {"task_id": "44031116", "userid": "3838360406544351233", "username": "杨雨晴"}
2025-07-31 17:06:23,071 - stream_saver.44031116.mp4 - WARNING - 没有正在进行的录制: stream_capture/recordings/zxy杨雨晴-网页/44031116.mp4
2025-07-31 17:06:23,071 - werkzeug - INFO - ************* - - [31/Jul/2025 17:06:23] "[31m[1mPOST /api/signal/stop HTTP/1.1[0m" 400 -
2025-07-31 17:06:26,315 - signal_api - INFO - 接收到停止信号: {"task_id": "44031116", "userid": "3838360406544351233", "username": "杨雨晴"}
2025-07-31 17:06:26,315 - signal_api - INFO - 接收到停止信号: {"task_id": "44031116", "userid": "3838360406544351233", "username": "杨雨晴"}
2025-07-31 17:06:26,315 - stream_saver.44031116.mp4 - WARNING - 没有正在进行的录制: stream_capture/recordings/zxy杨雨晴-网页/44031116.mp4
2025-07-31 17:06:26,315 - werkzeug - INFO - ************* - - [31/Jul/2025 17:06:26] "[31m[1mPOST /api/signal/stop HTTP/1.1[0m" 400 -
2025-07-31 17:32:59,519 - werkzeug - INFO - 210.41.221.28 - - [31/Jul/2025 17:32:59] "[31m[1mPOST /api/signal/start HTTP/1.1[0m" 400 -
