# 辨证论治AI系统技术实现方案

## 1. 系统概述
### 1.1 项目目标
本项目旨在构建一个医疗场景下的智能对话分析系统。系统核心功能是通过捕获医患沟通的音视频流，利用语音识别（ASR）、说话人分离（Diarization）和自然语言处理（NLP）技术，实现对话的实时记录、结构化信息提取和基于中医辨证论治理论的初步分析，最终生成结构化的电子病历并存入数据库，为医生提供临床决策支持。

### 1.2 功能范围
- **视频流录制**: 根据外部信号按需录制指定视频流。
- **音视频处理**: 从录制的视频中自动提取音频。
- **语音分析**: 对音频进行说话人分离，并将每个人的语音转换成文本。
- **文本分析**:
    - **转录**: 生成完整的对话文本。
    - **信息提取**: 从对话中提取主诉、现病史等关键医疗信息。
    - **智能诊断**: 调用Agent（智能体）对文本进行辨证论治分析。
- **数据持久化**: 将原始录音、转录文本、分析结果等文件归档，并将结构化的电子病历存入数据库。
- **接口服务**: 提供HTTP API，供外部系统（如HIS）触发和控制录制分析任务。

### 1.3 核心业务流程
本系统的业务流程由外部API调用驱动，涵盖了从设备准入、视频录制、AI分析到数据持久化的完整闭环。

```mermaid
graph TD
    subgraph "阶段一：任务启动与视频录制"
        A[外部系统/HIS] -- 1. POST /api/signal/start <br/>(含mac_address, task_id, 临床信息) --> B(API 网关);
        B -- 2. 调度信号处理器 --> C{SignalHandler};
        C -- 3. 使用mac_address查询 --> D[(数据库<br/>room_stream_mapping)];
        D -- 4. 返回stream_url或拒绝 --> C;
        C -- 5. 验证通过, 启动录制线程 --> E[视频流捕获模块<br/>(生产者-消费者)];
        E -- 6. 从指定URL录制 --> F((本地视频文件<br/>.mp4));
    end
    
    subgraph "阶段二：任务停止与后台处理"
        G[外部系统/HIS] -- 7. POST /api/signal/stop <br/>(含task_id) --> B;
        B -- 8. 调度信号处理器 --> C;
        C -- 9. 停止录制线程 --> E;
        C -- 10. 触发核心引擎异步处理 --> H[核心处理引擎<br/>AudioProcessor];
    end

    subgraph "阶段三：AI分析与数据持久化"
        H -- 11. 传入视频文件路径 --> I{AI处理流水线};
        I -- 12. 音频提取 --> J[FunASR 引擎];
        J -- 13. 说话人分离+语音识别 --> K[对话文本<br/>(含说话人标签)];
        K -- 14. 结合临床信息构造Prompt --> L{调用外部Agent};
        L -- 15. HTTP 请求 --> M[大语言模型服务<br/>(LLM Agent)];
        M -- 16. 返回结构化分析结果 --> L;
        L -- 17. 生成电子病历 --> N{最终结果产物};
        N -- 18. 归档文件 --> O[(文件系统/MinIO)];
        N -- 19. 写入业务表 --> D;
    end
```

**流程步骤详解**:

1.  **任务启动**: 外部系统（如HIS）向本系统API网关发起`start`请求，请求体中必须包含设备的`mac_address`、全局唯一的`task_id`以及患者的初始临床信息（如主诉）。

2.  **设备准入验证**: `SignalHandler`接收到请求后，会立刻查询`room_stream_mapping`数据库表。
    *   **验证成功**: 如果`mac_address`存在于表中且状态为激活，系统将获取对应的`stream_url`并继续下一步。
    *   **验证失败**: 如果设备未注册或被禁用，则直接向外部系统返回错误，流程终止。

3.  **开始录制**: 验证通过后，`SignalHandler`会为该`task_id`启动一对独立的“生产者-消费者”线程，开始从指定的`stream_url`拉取视频流，并将其稳定地保存为本地MP4文件。

4.  **任务停止**: 外部系统在需要结束时，发起`stop`请求，并提供要停止的`task_id`。

5.  **触发后台处理**: `SignalHandler`收到停止信号后，会优雅地终止对应的录制线程，并立即调用核心处理引擎`AudioProcessor`，将录制好的视频文件路径及相关任务信息（如初始临床信息）传递过去，整个后台处理流程以**异步方式**启动。

6.  **AI分析流水线**:
    *   `AudioProcessor`首先从视频中提取音频。
    *   调用**FunASR**模型，对音频进行**说话人分离**和**语音识别**，产出带有说话人标签和时间戳的完整对话文本。
    *   整合对话文本和初始临床信息，构造一个结构化的Prompt。
    *   通过HTTP请求，调用外部的**大语言模型Agent服务**，发送该Prompt。
    *   接收并解析Agent返回的JSON格式的分析结果，生成结构化的电子病历。

7.  **数据持久化**:
    *   将处理过程中产生的所有文件（原始视频、音频切片、文本记录、分析结果JSON等）归档到**文件系统**或**MinIO对象存储**中。
    *   将结构化的电子病历数据写入`medical_conversation`等相关**数据库业务表**中，完成最终的数据落盘。


## 2. 系统架构

### 2.1 架构设计原则
- **关注点分离 (SoC)**: 严格区分系统的不同职责。`stream_capture`模块负责Web接口、任务调度和I/O密集型的视频录制；`backend`模块则封装所有CPU/GPU密集型的AI计算任务。这种分离使得两部分可以独立开发、测试和优化。
- **异步与解耦**: 录制模块与后台处理模块之间通过异步调用解耦。录制任务的完成不会阻塞API响应，而是触发一个后台处理流程，极大地提高了系统的响应能力和吞吐量。
- **高内聚低耦合**: 模块内部功能高度相关（如`backend`模块内部的AI流水线），模块之间则通过定义清晰的接口进行交互（如`AudioProcessor`类），降低了系统的复杂性和维护成本。
- **可扩展性**: 采用“模型池+线程池”的并发处理架构，使得系统可以通过增加计算资源（更多线程或GPU卡）来水平扩展其AI处理能力。

### 2.2 逻辑架构图
系统在逻辑上分为三层：接口与调度层、核心引擎层和数据存储层。

```mermaid
graph TD
    subgraph "外部系统"
        HIS[医院信息系统]
    end

    subgraph "接口与调度层 (stream_capture 模块)"
        style API fill:#cce5ff,stroke:#333,stroke-width:2px
        style SH fill:#cce5ff,stroke:#333,stroke-width:2px
        
        API[API服务 /api/signal]
        SH{信号与任务管理器<br/>SignalHandler}
        RMM[诊室映射管理器<br/>(内存缓存)]
        PC[生产者-消费者<br/>(视频录制线程)]
    end

    subgraph "核心引擎层 (backend 模块)"
        style CA fill:#d5e8d4,stroke:#333,stroke-width:2px
        style AP fill:#d5e8d4,stroke:#333,stroke-width:2px

        CA[并发任务调度器<br/>(模型池+线程池)]
        AP[AI处理流水线<br/>AudioProcessor]
    end

    subgraph "数据存储层"
        style DB fill:#f8cecc,stroke:#333,stroke-width:2px
        style S3 fill:#f8cecc,stroke:#333,stroke-width:2px

        DB[(MySQL数据库)]
        S3[(文件系统/MinIO)]
    end
    
    subgraph "外部服务"
        LLM[LLM Agent服务]
    end

    HIS -- 1. 发起/停止任务 --> API;
    API -- 2. 解析请求 --> SH;
    SH -- 3. 查询设备 --> RMM;
    RMM -- 4. 访问 --> DB;
    SH -- 5. 创建/停止录制 --> PC;
    PC -- 6. 写入视频 --> S3;
    SH -- 7. 录制完成, 提交异步任务 --> CA;
    CA -- 8. 从池中获取Worker --> AP;
    AP -- 9. 执行AI处理 --> AP;
    AP -- 10. 调用 --> LLM;
    AP -- 11. 读写文件 --> S3;
    AP -- 12. 写入结果 --> DB;

```

### 2.3 部署架构图
系统设计为单机部署模式，所有组件运行在同一台服务器上，通过内部调用进行交互。这种模式简化了初始部署和运维。

```mermaid
graph TD
    subgraph "服务器 (Server)"
        direction LR
        
        subgraph "应用容器 (Python Application)"
            Flask[Flask Web服务器 (stream_capture)]
            Backend[后台处理服务 (backend)]
        end
        
        subgraph "存储服务"
            MySQL[(MySQL Server)]
            MinIO[(MinIO Server)]
        end
        
        Flask -- gRPC/HTTP<br/>(内部调用) --> Backend;
        Backend -- TCP/IP --> MySQL;
        Backend -- S3 API --> MinIO;
        Flask -- TCP/IP --> MySQL;
    end
    
    User[外部系统/用户] -- HTTP/HTTPS --> Flask;

```
*   **应用容器**: 运行Python代码的主进程，包含作为Web入口的Flask应用和执行AI任务的后台服务。两者在同一进程空间内，可以直接通过方法调用进行交互。
*   **存储服务**: MySQL和MinIO可以作为独立的服务进程运行，或者通过Docker容器化部署在同一主机上。
*   **通信**: 外部系统通过HTTP与Flask应用通信；应用内部各组件与存储服务之间通过标准的数据库连接和S3 API进行通信。

## 3. 技术选型
### 3.1 核心技术栈
| 分类 | 技术 | 版本/说明 | 理由 |
|---|---|---|---|
| **编程语言** | Python | 3.8+ | 生态丰富，尤其在AI和数据科学领域是事实标准。 |
| **Web框架** | Flask | 2.x | 轻量级，易于上手，完全满足本项目API服务的需求。 |
| **数据库** | MySQL | 5.7+ / 8.0+ | 成熟稳定，社区支持广泛，满足结构化数据存储需求。 |
| **对象存储** | MinIO | latest | 兼容S3协议的开源对象存储，适合存放录音、视频等非结构化大文件。 |

### 3.2 关键依赖库
| 库/工具 | 用途 |
|---|---|
| **cv2 (OpenCV-Python)** | 从视频流中实时捕获视频帧。 |
| **ffmpeg** | 高效的音视频处理工具，用于从视频中提取音频、对视频帧进行编码。 |
| **numpy** | 高性能科学计算库，用于处理视频帧等数值数据。 |
| **mysql-connector-python**| 官方MySQL驱动，用于连接和操作数据库。 |
| **requests** | 业界标准的HTTP客户端库，用于调用外部LLM Agent服务。 |
| **minio** | MinIO官方Python客户端，用于上传和管理对象存储中的文件。 |

### 3.3 AI模型与服务
| 名称 | 类型 | 核心功能 |
|---|---|---|
| **FunASR** | 开源模型 | 提供业界领先的说话人分离(Diarization)和语音识别(ASR)能力。 |
| **LLM Agent** | 外部服务 | 执行核心的辨证论治分析，从对话文本中提取结构化信息并生成病历。 |

## 4. 核心模块详细设计

### 4.1 接口与调度模块 (`stream_capture`)

本模块是整个系统的“数据入口”和“总指挥”，负责对外提供API，对内管理录制任务的整个生命周期。其设计核心是**稳定性**与**高并发响应能力**。

#### 4.1.1 模块职责
-   **API网关**: 暴露`start`和`stop`两个HTTP端点，作为系统与外部交互的唯一入口。
-   **任务管理**: 管理所有录制任务的状态，确保每个任务的创建、执行和销毁都得到正确处理。
-   **设备准入**: 在任务开始前，通过查询数据库验证设备的合法性。
-   **视频录制**: 调用底层库，稳定、高效地完成视频流的捕获和存储。
-   **异步触发**: 在录制结束后，将计算密集型的AI分析任务提交给后台核心引擎，并立即返回响应，避免长时间阻塞。

#### 4.1.2 关键组件设计
-   **API服务 (`api/signal_receiver.py`)**:
    -   **角色**: 网关。
    -   **实现**: 基于Flask，负责解析请求、验证参数，然后将业务逻辑全权委托给`SignalHandler`。
-   **信号与任务管理器 (`core/signal/signal_handler.py`)**:
    -   **角色**: 总指挥。
    -   **实现**: 维护一个内存中的字典来追踪所有激活的任务。负责调用`RoomMappingManager`进行设备验证，创建和销毁录制线程，并在录制结束后调用`backend`模块。
-   **诊室映射管理器 (`front/room_mapping_manager.py`)**:
    -   **角色**: 配置服务。
    -   **实现**: 封装了与`room_stream_mapping`表的交互。在系统启动时，它会从数据库加载所有激活的诊室映射到内存缓存中，供`SignalHandler`在运行时快速查询。
-   **视频录制引擎 (生产者-消费者)**:
    -   **生产者 (`core/producer/stream_capturer.py`)**: 独立线程，使用`OpenCV`从视频流URL循环读取帧，并放入内存队列。
    -   **消费者 (`core/consumer/stream_saver.py`)**: 独立线程，从队列中取出帧，通过管道喂给`ffmpeg`子进程，由`ffmpeg`完成编码并写入MP4文件。这种模式有效解耦了网络I/O和磁盘I/O。

### 4.2 AI核心引擎模块 (`backend`)

本模块是系统的“计算核心”，负责执行所有CPU/GPU密集型的AI任务。它的设计核心是**高吞吐**和**高精度**。

#### 4.2.1 模块职责
-   接收上游传入的视频文件和相关元数据。
-   执行一个从视频到结构化电子病历的完整AI处理流水线。
-   将流水线中产生的所有中间文件和最终结果持久化到存储层。

#### 4.2.2 架构设计：模型池与线程池
为了高效处理并发的AI任务，`backend`模块采用了“**模型池 + 工作线程池**”的高性能架构。
-   **模型池**: 在服务启动时，系统会预先加载多份FunASR模型到不同的计算设备（如多张GPU卡）的显存中，形成一个模型实例池。
-   **工作线程池**: 系统维护一个固定大小的线程池。
-   **调度器 (`app_api_concurrent.py`)**: 当一个新任务到达时，调度器会从线程池中取出一个空闲线程，并从模型池中为其分配一个空闲的模型实例。任务完成后，线程和模型实例都将归还给池，等待下一个任务。这种设计避免了昂贵的模型反复加载和卸载，最大限度地利用了硬件资源，显著提高了系统的并发处理能力。

#### 4.2.3 AI处理流水线详解
每个工作线程执行的流水线 (`audio_processor.py`) 如下：
1.  **音频提取**: 使用`ffmpeg`从输入的MP4文件中无损抽取出`16kHz`单声道的WAV音频。
2.  **语音分析 (FunASR)**:
    -   **说话人分离 (Diarization)**: 对WAV文件进行分析，为每个语音片段打上说话人标签（`speaker_0`, `speaker_1`）。
    -   **语音识别 (ASR)**: 对每个带标签的语音片段进行识别，生成带有精确时间戳的句子列表。
3.  **辨证论治分析 (LLM Agent)**:
    -   **Prompt构造**: 将ASR识别出的完整对话文本，与任务开始时传入的患者主诉、现病史等临床信息，整合成一个结构化的Prompt。
    -   **Agent调用**: 使用`requests`库向外部的LLM Agent服务发起HTTP POST请求。
    -   **结果解析**: 解析Agent返回的JSON响应，提取出结构化的诊断、证型、治法、方剂等信息。
4.  **数据持久化**:
    -   **文件归档**: 将原始视频、音频、切片、文本、JSON等所有产物，统一上传到MinIO对象存储的指定路径下。
    -   **结果入库**: 将任务元数据和结构化的电子病历信息，写入MySQL数据库的`medical_conversation`等相关表中。

## 5. 接口设计 (API)
### 5.1 开始录制
- **URL**: `/api/signal/start`
- **Method**: `POST`
- **Body**: 包含`task_id`, `mac_address`以及可选的患者临床信息（主诉、现病史等）。
- **功能**: 根据`mac_address`查找视频流，创建录制任务，并记录传入的初始临床信息。

### 5.2 停止录制
- **URL**: `/api/signal/stop`
- **Method**: `POST`
- **Body**: 包含`task_id`。
- **功能**: 停止指定`task_id`的录制任务，并触发后台的核心处理流水线。

## 6. 数据库设计

基于对 `media_uploader.py` 脚本的分析，本系统使用`MySQL`数据库，并涉及以下核心数据表。

### 6.1 数据库选型
- **数据库**: `MySQL`
- **字符集**: `utf8mb4`，以支持包括Emoji在内的各类文本。

### 6.2 E-R关系图
```mermaid
erDiagram
    ROOM_STREAM_MAPPING {
        int id PK
        varchar(100) room_name UK
        varchar(50) mac_address UK
        varchar(255) stream_url UK
        tinyint(1) is_active
    }

    MEDICAL_CONVERSATION {
        varchar(255) task_id PK "任务唯一ID"
        varchar(255) room_name "诊室名称"
        varchar(1024) full_video_url "完整视频URL"
        json sentences_json "带时间戳和说话人的对话全量JSON"
        json medical_record_json "结构化电子病历JSON"
        timestamp update_time "更新时间"
        timestamp create_time "创建时间"
    }

    CONVERSATION_SPEAKER_CLIPS {
        bigint id PK "自增ID"
        varchar(255) task_id FK "关联的任务ID"
        int speaker_id "说话人ID"
        varchar(255) segment_id "片段ID"
        varchar(1024) clip_url "视频切片URL"
    }

    BIZ_CLOUD_HEALTH {
        bigint id PK "唯一ID (时间戳+随机数)"
        varchar(255) msgname "消息名称"
        varchar(255) msgtype "消息类型"
        text msgcontent "消息内容 (含主诉)"
        varchar(255) userid "用户ID"
        varchar(255) username "用户姓名"
        timestamp createtime "创建时间"
    }

    ROOM_STREAM_MAPPING ||..o{ MEDICAL_CONVERSATION : "provides_stream_for"
    MEDICAL_CONVERSATION ||--|{ CONVERSATION_SPEAKER_CLIPS : "contains"
    MEDICAL_CONVERSATION }|..|{ BIZ_CLOUD_HEALTH : "notifies"

```

### 6.3 表结构详情

#### **6.3.1 `medical_conversation` (医患对话记录表)**
此表是核心业务表，存储了每一次诊疗任务的完整结果。

| 字段名 | 数据类型 | 键 | 注释 |
| --- | --- | --- | --- |
| `task_id` | `VARCHAR(255)` | PK | 任务唯一ID，用于关联所有数据 |
| `room_name` | `VARCHAR(255)` | | 诊室名称 |
| `full_video_url` | `VARCHAR(1024)`| | 录制的完整视频在MinIO上的URL |
| `sentences_json` | `JSON` | | FunASR输出的，包含时间戳和说话人标签的完整对话JSON |
| `medical_record_json`| `JSON` | | Agent分析后生成的结构化电子病历JSON |
| `create_time` | `TIMESTAMP` | | 记录创建时间 (DEFAULT CURRENT_TIMESTAMP) |
| `update_time` | `TIMESTAMP` | | 记录更新时间 (ON UPDATE CURRENT_TIMESTAMP) |

#### **6.3.2 `conversation_speaker_clips` (对话视频切片表)**
此表存储了按说话人分割后的视频切片信息，与主表一对多关联。

| 字段名 | 数据类型 | 键 | 注释 |
| --- | --- | --- | --- |
| `id` | `BIGINT` | PK, AI | 自增主键 |
| `task_id` | `VARCHAR(255)` | FK | 关联 `medical_conversation.task_id` |
| `speaker_id` | `INT` | | 说话人ID (例如: 0, 1, 2) |
| `segment_id` | `VARCHAR(255)` | | 视频片段ID (例如: 'segment_10') |
| `clip_url` | `VARCHAR(1024)`| | 该视频切片在MinIO上的URL |

#### **6.3.3 `biz_cloud_health` (业务健康云消息表)**
此表似乎用于集成一个外部的“健康云”系统，用于发送通知或记录日志。

| 字段名 | 数据类型 | 键 | 注释 |
| --- | --- | --- | --- |
| `id` | `BIGINT` | PK | 唯一ID，由毫秒时间戳+随机数生成 |
| `msgname` | `VARCHAR(255)` | | 消息名称，固定为'名医智能辨证辅助系统' |
| `msgtype` | `VARCHAR(255)` | | 消息类型，固定为'AI电子病历生成' |
| `msgcontent`| `TEXT` | | 消息内容，通常包含患者主诉信息 |
| `userid` | `VARCHAR(255)` | | 操作用户的ID |
| `username` | `VARCHAR(255)` | | 操作用户的姓名 |
| `msgid` | `BIGINT` | | 固定的消息ID (6992) |
| `msgtypeid` | `BIGINT` | | 固定的消息类型ID |
| `enable` | `TINYINT` | | 是否启用，固定为1 |
| `createtime`| `TIMESTAMP` | | 消息创建时间 |
| `createdate`| `TIMESTAMP` | | 记录创建日期 |

#### **6.3.4 `room_stream_mapping` (诊室流媒体映射表)**
此表是系统的“准入控制”表，定义了哪些设备（通过MAC地址识别）被授权可以进行录制，并指定了对应的流媒体地址。

| 字段名 | 数据类型 | 键 | 注释 |
| --- | --- | --- | --- |
| `id` | `INT` | PK, AI | 自增主键 |
| `room_name` | `VARCHAR(100)` | UK | 诊室的易读名称 |
| `mac_address` | `VARCHAR(50)` | UK | 采集设备的唯一MAC地址 |
| `stream_url` | `VARCHAR(255)` | UK | 对应的视频流URL |
| `description` | `VARCHAR(255)` | | 描述信息 |
| `is_active` | `TINYINT(1)` | | 是否激活 (1=激活, 0=禁用) |
| `create_time` | `DATETIME` | | 记录创建时间 |
| `update_time` | `DATETIME` | | 记录更新时间 |

## 7. 项目完整实现思路和架构

### **7.1 视频录制模块**

本模块是整个系统的“数据入口”，负责响应外部API指令，完成视频流的按需录制与存储。其设计核心是**稳定性**与**并发性**，确保在高并发场景下也能可靠地、互不干扰地完成多个录制任务。

#### **7.1.1 模块职责与核心流程**

**职责**:
-   提供HTTP API接口，接收外部系统（HIS）的“开始”和“停止”录制指令。
-   管理并发的录制任务，为每个任务分配独立的资源。
-   根据指令中的标识符（MAC地址）查找对应的视频流URL。
-   稳定地从视频流URL拉取数据，并将其编码保存为MP4文件。
-   在录制结束后，触发后续的核心处理流水线（即调用`backend.AudioProcessor`）。

**核心流程**:
下面是处理一个完整录制任务的序列图：
```mermaid
sequenceDiagram
    participant HIS as 外部系统 (HIS)
    participant API as API服务 (signal_receiver.py)
    participant SH as 信号处理器 (SignalHandler)
    participant Capturer as 视频捕获线程 (Producer)
    participant Saver as 视频存储线程 (Consumer)
    participant AP as 核心处理引擎 (AudioProcessor)

    HIS->>+API: POST /api/signal/start (含task_id, mac_address)
    API->>+SH: 调用 start_recording(task_id, mac_address)
    SH->>SH: 查找配置, 获取stream_url
    SH->>Capturer: 创建并启动捕获线程
    SH->>Saver: 创建并启动存储线程
    activate Capturer
    activate Saver
    Capturer-->>SH: 线程已启动
    Saver-->>SH: 线程已启动
    SH-->>-API: 返回成功响应 {status: "success"}
    API-->>-HIS: HTTP 200 OK

    loop 录制进行中
        Capturer->>Capturer: 从视频流URL读取帧
        Capturer->>Saver: 将视频帧放入队列
        Saver->>Saver: 从队列获取视频帧
        Saver->>Saver: 使用ffmpeg编码并写入文件
    end

    HIS->>+API: POST /api/signal/stop (含task_id)
    API->>+SH: 调用 stop_recording(task_id)
    SH->>Capturer: 发送停止信号 (Event.set())
    SH->>Saver: 发送停止信号 (Event.set())
    deactivate Capturer
    deactivate Saver
    Capturer-->>SH: 线程已优雅退出
    Saver-->>SH: 线程已优雅退出
    SH->>+AP: 调用 process_video(video_path)
    AP-->>-SH: 返回处理任务已接收
    SH-->>-API: 返回成功响应 {status: "success"}
    API-->>-HIS: HTTP 200 OK
```

#### **7.1.2 架构设计与关键组件**

本模块采用经典的**生产者-消费者（Producer-Consumer）模式**，以实现网络I/O与磁盘I/O的解耦，有效应对网络抖动等不稳定情况。

-   **API服务 (`api/signal_receiver.py`)**:
    -   **角色**: 网关。
    -   **实现**: 基于Flask框架，提供`/api/signal/start`和`/api/signal/stop`两个路由。它负责解析请求、验证参数（如JSON格式是否正确、关键字段是否存在），然后将业务逻辑全权委托给`SignalHandler`。

-   **信号处理器 (`core/signal/signal_handler.py`)**:
    -   **角色**: 总指挥/任务管理器。
    -   **实现**: 一个核心类`SignalHandler`，它在内存中维护一个字典（如`active_tasks`），用于追踪所有正在进行的录制任务。
        -   收到`start`指令后，它负责创建任务上下文（包括任务ID、文件路径、线程、停止事件等），并启动对应的生产者和消费者线程。
        -   收到`stop`指令后，它负责找到对应的任务，通过线程间的`Event`对象通知生产者和消费者线程停止工作，并等待它们安全退出。
        -   是连接视频录制模块和后端处理模块的桥梁，在录制结束后调用`AudioProcessor`。

-   **诊室映射管理器 (RoomMappingManager)**:
    -   **角色**: 配置服务。
    -   **实现**: `SignalHandler`在初始化时，会创建一个`RoomMappingManager`实例。该管理器负责连接数据库，并将`room_stream_mapping`表中的所有激活的(`is_active=1`)记录加载到内存中的一个字典里。这种“启动时加载，运行时查内存”的模式，取代了原先直接读取JSON文件的方式，提供了更强的可管理性和动态性（理论上可通过数据库管理工具动态增删诊室而无需重启服务），并保证了在处理`start`请求时，能够通过内存查询快速、高效地获取到`stream_url`，避免了每次请求都进行数据库I/O的性能开销。`room_stream_mapping.json`文件则作为数据库的**初始数据源**，由`front/init_room_mapping.py`脚本在首次部署时导入。

-   **视频捕获线程 (`core/producer/stream_capturer.py`)**:
    -   **角色**: 生产者。
    -   **实现**: 一个继承自`threading.Thread`的类。其`run()`方法内含一个循环：使用`cv2.VideoCapture`连接到视频流URL，持续读取视频帧，然后将帧对象放入与特定任务关联的内存队列 (`queue.Queue`) 中。内置了对停止信号的监听。

-   **视频存储线程 (`core/consumer/stream_saver.py`)**:
    -   **角色**: 消费者。
    -   **实现**: 一个继承自`threading.Thread`的类。其`run()`方法会启动一个`ffmpeg`子进程，并打开其标准输入管道。在主循环中，它不断地从队列中获取视频帧，将其转换为适合`ffmpeg`的格式，然后写入子进程的管道中，由`ffmpeg`完成视频的编码和文件存储。同样，它也会监听停止信号以实现优雅退出。

-   **队列管理器 (`utils/queue_manager.py`)**:
    -   **角色**: 资源池。
    -   **实现**: 一个简单的工具类或单例，用于创建和管理任务ID与`queue.Queue`实例之间的映射，确保每个任务都有自己独立的队列，避免数据串流。

#### **7.1.3 接口设计 (API)**

-   **开始录制**:
    -   `POST /api/signal/start`
    -   **Request Body** (`application/json`):
        ```json
        {
          "task_id": "string (唯一任务ID)",
          "mac_address": "string (设备的MAC地址)",
          "patient_name": "string (可选)",
          "chief_complaint": "string (可选, 患者主诉)",
          "present_illness": "string (可选, 现病史)",
          "past_medical_history": "string (可选, 既往史)",
          "allergic_history": "string (可选, 过敏史)"
        }
        ```
    -   **Success Response** (`200 OK`):
        ```json
        {
          "status": "success",
          "message": "开始录制任务: [任务ID]",
          "room_name": "A402诊室"
        }
        ```
-   **停止录制**:
    -   `POST /api/signal/stop`
    -   **Request Body** (`application/json`):
        ```json
        {
          "task_id": "string (要停止的任务ID)"
        }
        ```
    -   **Success Response** (`200 OK`):
        ```json
        {
          "status": "success",
          "message": "停止录制任务: [任务ID], 录制时长: 120.5秒",
          "duration": 120.5
        }
        ```

#### **7.1.4 关键类与函数设计**

-   **`SignalHandler`**:
    -   `__init__(self, db_config)`: 初始化时创建`RoomMappingManager`实例，并由其完成数据库连接和数据加载。
    -   `start_recording(self, task_id, mac_address, ...)`:
        1.  检查`task_id`是否已在`self.active_tasks`中，防止重复启动。
        2.  通过`RoomMappingManager`**从内存缓存中**根据`mac_address`查找`stream_url`和`room_name`，若未找到或设备未激活，则返回错误。
        3.  构建视频保存路径，如`stream_capture/recordings/诊室名/任务ID.mp4`。
        4.  创建一个`threading.Event`作为停止信号，一个`queue.Queue`作为数据通道。
        5.  实例化`StreamCapturer`和`StreamSaver`线程，将队列和停止事件传入。
        6.  启动两个线程。
        7.  将任务信息（线程实例、停止事件、开始时间等）存入`self.active_tasks`字典。
    -   `stop_recording(self, task_id)`:
        1.  从`self.active_tasks`中查找`task_id`对应的任务信息，若未找到则返回错误。
        2.  调用任务信息中的`stop_event.set()`方法，向两个线程广播停止信号。
        3.  调用`producer_thread.join()`和`consumer_thread.join()`，等待线程执行完毕。
        4.  计算录制时长。
        5.  **调用下游模块**: `AudioProcessor.process_video(video_path, ...)`。
        6.  从`self.active_tasks`中移除该任务。

#### **7.1.5 配置与错误处理**

-   **配置管理**:
    -   系统的核心配置`room_stream_mapping`已从JSON文件迁移至数据库。
    -   `room_stream_mapping.json`文件仅作为**初始化数据源**使用。
    -   运行时的诊室配置信息在服务启动时由`RoomMappingManager`从数据库加载到内存。对于需要动态更新配置且不重启服务的场景，可为`RoomMappingManager`增加一个定时刷新或通过API触发刷新的机制。

-   **错误处理机制**:
    -   **API层**: 对请求体进行严格校验，对于格式错误、缺少必要字段的请求，直接返回`400 Bad Request`，并附带明确的错误信息。
    -   **配置错误**: 当`start`请求中的`mac_address`在数据库中找不到，或对应的记录`is_active`为0时，返回`404 Not Found`或自定义的业务错误码（如 403 Forbidden），告知调用方“设备未注册或已禁用”。
    -   **视频流连接失败**: `StreamCapturer`应包含重试逻辑。例如，尝试连接3次，每次间隔5秒。若最终仍失败，则任务失败，通过日志系统记录严重错误，并清理该任务的相关资源。
    -   **磁盘写入失败**: `StreamSaver`在检测到`ffmpeg`进程异常退出或写入错误时，应立即停止任务，记录严重错误，并避免触发后续的音频处理流程。
    -   **服务关闭**: 主程序通过捕获`SIGINT`/`SIGTERM`信号，会遍历所有`active_tasks`，并调用`stop_recording`方法，以确保在服务退出前，所有正在录制的视频都能被正确保存，实现“优雅停机”。

### **7.2 音视频处理与语音识别**

本模块是AI处理流水线的**第一站**，专注于技术层面的转换。它接收原始视频文件，负责将其处理成可供上层业务分析的、带说话人信息的纯文本对话记录。

#### **7.2.1 模块职责与核心流程**

**职责**:
-   接收上游模块传入的视频文件路径。
-   执行一个从视频到文本的转换流水线，包括：音频提取、说话人分离、语音识别。
-   整合处理结果，生成每个说话人的音频切片（`speaker_N.mp3`）、文本记录（`speaker_N.txt`），以及完整的对话转录（`full_transcript.txt`）。
-   将初步处理结果（如完整的对话文本）传递给下一阶段的“辨证论治分析模块”。

**核心处理流水线**:
```mermaid
graph TD
    subgraph "音视频处理与语音识别模块"
        A[输入: video_path] --> B{1. 音频提取};
        B -- 使用ffmpeg --> C[原始音频 .wav];
        C --> D{2. AI模型处理 (FunASR)};
        subgraph "FunASR Pipeline"
            D1[VAD: 静音检测] --> D2[Diarization: 说话人分离];
            D2 --> D3[ASR: 语音识别];
        end
        D -- 输出带有时间戳和说话人标签的句子 --> E{3. 结果后处理};
        E --> F[生成 speaker_N.txt];
        E --> G[切分生成 speaker_N.mp3];
        E --> H[合并生成 full_transcript.txt];
        H --> I[输出: 完整对话文本及切片文件];
    end
```

#### **7.2.2 架构设计与关键组件**

本阶段的处理由`backend`模块中的核心组件驱动，并采用高性能并发架构。

-   **音频处理器 (`backend/audio_processor.py`)**: 作为单任务执行器(Worker)，封装了上述完整的处理流水线，负责调用`ffmpeg`和`FunASR`模型。
-   **并发音频处理器 (`backend/app_api_concurrent.py`)**: 作为任务调度器，通过内部维护的“**模型池**”和“**工作线程池**”来并发地执行多个音视频处理任务，极大提高了系统的处理吞吐能力。

#### **7.2.3 AI处理流水线详解**

1.  **音频提取**: 使用`ffmpeg`从输入的MP4文件中无损抽取出`16kHz`单声道的WAV音频。
2.  **说话人分离 (Diarization)**: 使用`FunASR`模型对WAV文件进行分析，为每个语音片段打上说话人标签（`speaker_0`, `speaker_1`等）。
3.  **语音识别 (ASR)**: 使用`FunASR`模型对每个带标签的语音片段进行识别，生成带时间戳的文本。
4.  **文本整合与文件切分**: 遍历识别结果，将所有文本按时间顺序合并为`full_transcript.txt`；同时，根据时间戳将原始音频切分、合并，生成各说话人的`speaker_N.mp3`文件。

### **7.3 辨证论治分析与病历生成**

本模块是系统的**业务核心**，它承接上一阶段生成的对话文本，模拟中医专家的诊断过程，生成结构化的电子病历。

#### **7.3.1 模块职责与核心流程**

**职责**:
-   接收对话文本和在任务开始时录入的初始临床信息（如主诉、现病史等）。
-   调用核心的“Agent”服务，对信息进行综合分析。
-   从Agent的响应中解析出结构化的辨证论治结果。
-   将分析结果组装成一份完整的、结构化的电子病历草稿。

**核心处理流程**:
```mermaid
graph TD
    A[输入: 对话文本 + 初始临床信息] --> B{1. 构造Prompt};
    B -- 将所有信息整合为结构化输入 --> C{2. 调用Agent服务};
    C -- 发起HTTP请求 --> D[外部大语言模型(LLM)服务];
    D -- 返回JSON格式的分析结果 --> C;
    C --> E{3. 解析与格式化};
    E -- 提取关键字段 --> F[输出: 结构化的电子病历数据];
```

#### **7.3.2 核心组件：Agent (外部服务)**

-   **角色**: 智能诊断引擎。
-   **实现推测**: 本系统中的“Agent”并非内置模块，而是一个**外部的、通过HTTP API调用的AI服务**，很可能基于一个强大的大语言模型（如GPT、Kimi或医疗专用模型）。
-   **接口协议**: `AudioProcessor`通过`requests`库与此服务交互。
    -   **请求**: 将患者主诉、现病史、过敏史以及完整的医患对话文本，按照Agent服务要求的格式（通常是JSON）构造为请求体，发送给Agent的API端点。
    -   **响应**: Agent服务返回一个JSON对象，其中包含了结构化的分析结果，如`诊断`、`证型`、`治法`、`方剂`、`建议`等字段。这些结果被保存在`agent_response.json`和`medical_info.json`文件中。

### **7.4 数据持久化与归档**

本模块是整个处理流程的**最后一站**，负责将所有中间产物和最终结果安全、可靠地存入数据库和文件存储系统。

#### **7.4.1 模块职责**
-   将结构化的电子病历信息写入关系型数据库。
-   将处理过程中生成的所有文件（.mp4, .mp3, .txt, .json）归档到文件系统中。
-   （可选）将文件归档备份到对象存储（MinIO）。

#### **7.4.2 数据库存储**
-   **触发时机**: 在`AudioProcessor`的处理流程末尾，获取到Agent的分析结果后。
-   **操作**:
    1.  连接到在配置中指定的数据库（如MySQL）。
    2.  将任务信息、患者信息、完整的对话文本、结构化的Agent分析结果等，组装成数据实体。
    3.  将数据实体分别存入`consultations`（诊疗记录总表）、`dialogues`（对话详情表）等数据库表中，确保事务一致性。

#### **7.4.3 文件系统归档**
-   **目标**: 所有与一个任务相关的文件，都保存在`SeparatedAudioText/诊室名/任务ID/`这个唯一的目录下，便于日后追溯和查阅。
-   **文件清单**:
    -   `full_transcript.txt`: 完整对话文本。
    -   `speaker_N.txt`: 分角色的对话文本。
    -   `speaker_N.mp3`: 分角色的音频文件。
    -   `sentences.json`: 带时间戳和说话人标签的原始识别结果。
    -   `agent_response.json`: Agent服务返回的原始JSON。
    -   `medical_info.json`: 从Agent响应中提取的核心医疗信息。
    -   ...以及其他可能的中间文件。

#### **7.4.4 对象存储归档 (MinIO)**
-   **角色**: 数据备份与容灾。
-   **组件**: `backend/minio_uploader.py`。
-   **触发时机**: 在所有文件生成和数据库写入成功后（可选步骤）。
-   **操作**: 调用`MinIOUploader`的`upload_directory`方法，将上述的任务结果目录整体上传到MinIO服务器的一个指定存储桶（Bucket）中，路径可以设计为`bucket_name/诊室名/任务ID/`。这为数据提供了额外的安全保障。
