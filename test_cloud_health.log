2025-05-29 17:48:29,120 - media_uploader - INFO - 成功连接到数据库
2025-05-29 17:48:29,161 - media_uploader - INFO - 准备保存电子病历结果到biz_cloud_health表，用户信息: userid=test_user_1748512109, username=测试用户_1748512109
2025-05-29 17:48:29,162 - media_uploader - INFO - 成功读取medical_info.json文件: /data/AudioSeparation/SeparatedAudioText/测试诊室/TEST_1748512109/medical_info.json
2025-05-29 17:48:29,162 - media_uploader - INFO - 构建的msgcontent内容: 主诉：头痛两天，现病史：患者于两天前无明显诱因出现头痛，既往史：否认高血压、糖尿病病史，过敏史：青霉素过敏
2025-05-29 17:48:29,164 - media_uploader - INFO - 成功将电子病历结果保存到biz_cloud_health表，ID: 1082809528778489862
2025-05-29 17:48:29,164 - media_uploader - INFO - biz_cloud_health表保存详情 - 用户ID: test_user_1748512109, 用户名: 测试用户_1748512109, 消息内容: 主诉：头痛两天，现病史：患者于两天前无明显诱因出现头痛，既往史：否认高血压、糖尿病病史，过敏史：青霉素过敏
2025-05-29 17:48:29,166 - media_uploader - INFO - 成功保存任务 TEST_1748512109 的信息到数据库
2025-05-29 17:48:29,167 - media_uploader - INFO - 数据库连接已关闭
2025-05-29 17:49:16,528 - test_cloud_health - INFO - 成功连接到数据库
2025-05-29 17:49:16,531 - test_cloud_health - INFO - biz_cloud_health表结构正确
2025-05-29 17:49:16,535 - test_cloud_health - INFO - 成功插入记录到biz_cloud_health表，ID: 1082809528778489863
2025-05-29 17:49:16,537 - test_cloud_health - INFO - 成功查询到插入的记录: (1082809528778489863, '名医智能辨证辅助系统', 'AI电子病历生成', '诊断：感冒，主诉：发热、咳嗽两天', 'test_user_1748512156', '测试用户_1748512156', 6992, datetime.datetime(2025, 5, 29, 17, 49, 16), b'\x01', datetime.datetime(2025, 5, 29, 17, 49, 16), 4447378052527161344)
2025-05-29 17:49:16,625 - media_uploader - INFO - 成功连接到数据库
2025-05-29 17:49:16,629 - media_uploader - INFO - 准备保存电子病历结果到biz_cloud_health表，用户信息: userid=test_user_1748512156, username=测试用户_1748512156
2025-05-29 17:49:16,630 - media_uploader - INFO - 用户 test_user_1748512156 最近24小时内的记录已存在于biz_cloud_health表中，ID: 1082809528778489863，跳过插入新记录
2025-05-29 17:49:16,631 - media_uploader - INFO - 成功保存任务 TEST_1748512156 的信息到数据库
2025-05-29 17:49:16,633 - media_uploader - INFO - 数据库连接已关闭
